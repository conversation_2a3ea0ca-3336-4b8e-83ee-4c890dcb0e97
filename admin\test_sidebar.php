<?php
/**
 * اختبار القائمة الجانبية المحدثة
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$pageTitle = "اختبار القائمة الجانبية المحدثة - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">اختبار القائمة الجانبية المحدثة</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">اختبار القائمة الجانبية الجديدة</h5>
                    </div>
                    <div class="card-body">
                        <h6>الميزات الجديدة في القائمة الجانبية:</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">✅ الصفحات الأساسية:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-tachometer-alt text-primary"></i> لوحة التحكم</span>
                                        <a href="index.php" class="btn btn-sm btn-outline-primary">اختبار</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-box text-success"></i> المنتجات</span>
                                        <a href="products.php" class="btn btn-sm btn-outline-success">اختبار</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-tags text-info"></i> الأقسام</span>
                                        <a href="categories.php" class="btn btn-sm btn-outline-info">اختبار</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-shopping-cart text-warning"></i> الطلبات</span>
                                        <a href="orders.php" class="btn btn-sm btn-outline-warning">اختبار</a>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-success">🆕 الصفحات الجديدة:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-warehouse text-primary"></i> إدارة المخزون</span>
                                        <a href="inventory.php" class="btn btn-sm btn-outline-primary">اختبار</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-exchange-alt text-secondary"></i> حركات المخزون</span>
                                        <a href="stock-movements.php" class="btn btn-sm btn-outline-secondary">اختبار</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-users text-dark"></i> المستخدمين</span>
                                        <a href="users.php" class="btn btn-sm btn-outline-dark">اختبار</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="fas fa-history text-info"></i> سجل الأنشطة</span>
                                        <a href="activity-logs.php" class="btn btn-sm btn-outline-info">اختبار</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h6 class="text-info">🔧 الميزات الجديدة:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-bell fa-2x text-warning mb-2"></i>
                                        <h6>الشارات التفاعلية</h6>
                                        <p class="small text-muted">عرض عدد الطلبات الجديدة والمخزون المنخفض</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-sitemap fa-2x text-success mb-2"></i>
                                        <h6>القوائم الفرعية</h6>
                                        <p class="small text-muted">تنظيم أفضل للصفحات المترابطة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user-circle fa-2x text-primary mb-2"></i>
                                        <h6>معلومات المستخدم</h6>
                                        <p class="small text-muted">عرض اسم المستخدم والدور في أسفل القائمة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr class="my-4">
                        
                        <h6 class="text-danger">🧪 اختبار الوظائف:</h6>
                        
                        <?php
                        // اختبار حساب الشارات
                        try {
                            $newOrdersCount = $database->fetch("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count'] ?? 0;
                            $lowStockCount = $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND stock_quantity <= min_stock_level")['count'] ?? 0;
                            
                            echo "<div class='alert alert-success'>";
                            echo "<h6>✅ إحصائيات الشارات:</h6>";
                            echo "<ul class='mb-0'>";
                            echo "<li><strong>الطلبات الجديدة:</strong> $newOrdersCount طلب</li>";
                            echo "<li><strong>المخزون المنخفض:</strong> $lowStockCount منتج</li>";
                            echo "</ul>";
                            echo "</div>";
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-warning'>";
                            echo "<h6>⚠️ تحذير:</h6>";
                            echo "<p>لا يمكن حساب الشارات: " . $e->getMessage() . "</p>";
                            echo "</div>";
                        }
                        ?>
                        
                        <div class="alert alert-info">
                            <h6>📋 قائمة التحقق:</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check1" checked>
                                <label class="form-check-label" for="check1">
                                    القائمة الجانبية تظهر بشكل صحيح
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check2">
                                <label class="form-check-label" for="check2">
                                    الشارات تعرض الأرقام الصحيحة
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check3">
                                <label class="form-check-label" for="check3">
                                    جميع الروابط تعمل بشكل صحيح
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="check4">
                                <label class="form-check-label" for="check4">
                                    معلومات المستخدم تظهر في الأسفل
                                </label>
                            </div>
                        </div>
                        
                        <h6 class="text-secondary">🔗 اختبار سريع للصفحات:</h6>
                        <div class="d-flex flex-wrap gap-2">
                            <a href="index.php" class="btn btn-outline-primary btn-sm">لوحة التحكم</a>
                            <a href="products.php" class="btn btn-outline-success btn-sm">المنتجات</a>
                            <a href="categories.php" class="btn btn-outline-info btn-sm">الأقسام</a>
                            <a href="orders.php" class="btn btn-outline-warning btn-sm">الطلبات</a>
                            <a href="inventory.php" class="btn btn-outline-primary btn-sm">المخزون</a>
                            <a href="stock-movements.php" class="btn btn-outline-secondary btn-sm">حركات المخزون</a>
                            <a href="activity-logs.php" class="btn btn-outline-info btn-sm">سجل الأنشطة</a>
                            <a href="settings.php" class="btn btn-outline-dark btn-sm">الإعدادات</a>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">معلومات تقنية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>ملفات القائمة الجانبية:</h6>
                                <ul class="list-unstyled">
                                    <li><code>admin/includes/sidebar.php</code> - القائمة المشتركة</li>
                                    <li><code>assets/css/admin.css</code> - التنسيقات</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>الصفحات المحدثة:</h6>
                                <ul class="list-unstyled">
                                    <li>✅ <code>products.php</code></li>
                                    <li>✅ <code>orders.php</code></li>
                                    <li>✅ <code>inventory.php</code></li>
                                    <li>✅ <code>stock-movements.php</code></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // اختبار تفاعلي للقائمة
    document.addEventListener('DOMContentLoaded', function() {
        // تحديد الصفحة النشطة
        const currentPage = window.location.pathname.split('/').pop();
        const sidebarLinks = document.querySelectorAll('.sidebar-menu a');
        
        sidebarLinks.forEach(link => {
            if (link.getAttribute('href') === currentPage) {
                link.classList.add('active');
            }
        });
        
        // إضافة تأثيرات hover
        sidebarLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-5px)';
                this.style.transition = 'transform 0.2s ease';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
    });
    </script>
</body>
</html>
