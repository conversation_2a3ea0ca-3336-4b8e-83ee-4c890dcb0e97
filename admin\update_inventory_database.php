<?php
/**
 * تحديث قاعدة البيانات لإضافة نظام إدارة المخزون
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

echo "<h2>تحديث قاعدة البيانات لنظام إدارة المخزون</h2>";

$updates = [];
$errors = [];

// 1. إضافة حقول المخزون لجدول المنتجات
echo "<h3>1. تحديث جدول المنتجات:</h3>";

$inventoryColumns = [
    'stock_quantity' => 'INT DEFAULT 0 COMMENT "كمية المخزون"',
    'min_stock_level' => 'INT DEFAULT 0 COMMENT "الحد الأدنى للمخزون"',
    'location' => 'VARCHAR(255) NULL COMMENT "موقع المنتج في المحل"',
    'sku' => 'VARCHAR(100) NULL UNIQUE COMMENT "رمز المنتج"'
];

foreach ($inventoryColumns as $column => $definition) {
    try {
        // التحقق من وجود العمود
        $checkColumn = $database->fetchAll("SHOW COLUMNS FROM products LIKE '$column'");
        
        if (empty($checkColumn)) {
            $sql = "ALTER TABLE products ADD COLUMN $column $definition";
            $database->query($sql);
            echo "<p style='color: green;'>✅ تم إضافة العمود: $column</p>";
            $updates[] = "إضافة العمود $column لجدول products";
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $column موجود بالفعل</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في إضافة العمود $column: " . $e->getMessage() . "</p>";
        $errors[] = "خطأ في إضافة العمود $column: " . $e->getMessage();
    }
}

// 2. إنشاء جدول حركات المخزون
echo "<h3>2. إنشاء جدول حركات المخزون:</h3>";

try {
    $stockMovementsTable = "
    CREATE TABLE IF NOT EXISTS stock_movements (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        movement_type ENUM('in', 'out', 'adjustment', 'sale', 'return') NOT NULL COMMENT 'نوع الحركة',
        quantity INT NOT NULL COMMENT 'الكمية',
        notes TEXT NULL COMMENT 'ملاحظات',
        user_id INT NULL COMMENT 'المستخدم الذي قام بالحركة',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_product_id (product_id),
        INDEX idx_movement_type (movement_type),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول حركات المخزون'";
    
    $database->query($stockMovementsTable);
    echo "<p style='color: green;'>✅ تم إنشاء جدول stock_movements</p>";
    $updates[] = "إنشاء جدول stock_movements";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء جدول stock_movements: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في إنشاء جدول stock_movements: " . $e->getMessage();
}

// 3. إنشاء جدول سجل الأنشطة
echo "<h3>3. إنشاء جدول سجل الأنشطة:</h3>";

try {
    $activityLogsTable = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        action VARCHAR(100) NOT NULL COMMENT 'نوع النشاط',
        description TEXT NOT NULL COMMENT 'وصف النشاط',
        ip_address VARCHAR(45) NULL COMMENT 'عنوان IP',
        user_agent TEXT NULL COMMENT 'معلومات المتصفح',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل الأنشطة'";
    
    $database->query($activityLogsTable);
    echo "<p style='color: green;'>✅ تم إنشاء جدول activity_logs</p>";
    $updates[] = "إنشاء جدول activity_logs";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء جدول activity_logs: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في إنشاء جدول activity_logs: " . $e->getMessage();
}

// 4. تحديث المنتجات الموجودة بقيم افتراضية
echo "<h3>4. تحديث المنتجات الموجودة:</h3>";

try {
    // تحديث المنتجات التي لا تحتوي على قيم مخزون
    $updateProducts = "
    UPDATE products 
    SET stock_quantity = 10, min_stock_level = 5 
    WHERE stock_quantity IS NULL OR stock_quantity = 0";
    
    $database->query($updateProducts);
    echo "<p style='color: green;'>✅ تم تحديث المنتجات الموجودة بقيم مخزون افتراضية</p>";
    $updates[] = "تحديث المنتجات الموجودة بقيم مخزون افتراضية";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحديث المنتجات: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في تحديث المنتجات: " . $e->getMessage();
}

// 5. إنشاء أرقام SKU للمنتجات الموجودة
echo "<h3>5. إنشاء أرقام SKU للمنتجات:</h3>";

try {
    $productsWithoutSku = $database->fetchAll("SELECT id, name FROM products WHERE sku IS NULL OR sku = ''");
    
    foreach ($productsWithoutSku as $product) {
        $sku = 'G8-' . str_pad($product['id'], 4, '0', STR_PAD_LEFT);
        $database->update('products', ['sku' => $sku], 'id = :id', ['id' => $product['id']]);
    }
    
    echo "<p style='color: green;'>✅ تم إنشاء أرقام SKU لـ " . count($productsWithoutSku) . " منتج</p>";
    $updates[] = "إنشاء أرقام SKU للمنتجات";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء أرقام SKU: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في إنشاء أرقام SKU: " . $e->getMessage();
}

// 6. إضافة بيانات تجريبية لحركات المخزون
echo "<h3>6. إضافة بيانات تجريبية لحركات المخزون:</h3>";

try {
    $products = $database->fetchAll("SELECT id FROM products LIMIT 5");
    $userId = $_SESSION['user_id'];
    
    foreach ($products as $product) {
        // حركة إدخال أولية
        $stockData = [
            'product_id' => $product['id'],
            'movement_type' => 'in',
            'quantity' => rand(20, 50),
            'notes' => 'مخزون أولي',
            'user_id' => $userId
        ];
        $database->insert('stock_movements', $stockData);
        
        // حركة بيع
        $stockData = [
            'product_id' => $product['id'],
            'movement_type' => 'sale',
            'quantity' => rand(1, 5),
            'notes' => 'بيع للعملاء',
            'user_id' => $userId
        ];
        $database->insert('stock_movements', $stockData);
    }
    
    echo "<p style='color: green;'>✅ تم إضافة بيانات تجريبية لحركات المخزون</p>";
    $updates[] = "إضافة بيانات تجريبية لحركات المخزون";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إضافة البيانات التجريبية: " . $e->getMessage() . "</p>";
    $errors[] = "خطأ في إضافة البيانات التجريبية: " . $e->getMessage();
}

// عرض النتائج
echo "<h3>7. ملخص التحديثات:</h3>";

if (!empty($updates)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ التحديثات المكتملة:</h4>";
    echo "<ul>";
    foreach ($updates as $update) {
        echo "<li>$update</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// فحص النتيجة النهائية
echo "<h3>8. فحص النتيجة النهائية:</h3>";

try {
    $productsCount = $database->fetch("SELECT COUNT(*) as count FROM products")['count'];
    $movementsCount = $database->fetch("SELECT COUNT(*) as count FROM stock_movements")['count'];
    $logsCount = $database->fetch("SELECT COUNT(*) as count FROM activity_logs")['count'];
    
    echo "<ul>";
    echo "<li><strong>عدد المنتجات:</strong> $productsCount</li>";
    echo "<li><strong>عدد حركات المخزون:</strong> $movementsCount</li>";
    echo "<li><strong>عدد سجلات الأنشطة:</strong> $logsCount</li>";
    echo "</ul>";
    
    // عرض عينة من المنتجات مع بيانات المخزون
    $sampleProducts = $database->fetchAll("SELECT name, stock_quantity, min_stock_level, sku, location FROM products LIMIT 5");
    
    if (!empty($sampleProducts)) {
        echo "<h4>عينة من المنتجات مع بيانات المخزون:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>اسم المنتج</th><th>SKU</th><th>المخزون</th><th>الحد الأدنى</th><th>الموقع</th>";
        echo "</tr>";
        
        foreach ($sampleProducts as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['sku']) . "</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "<td>" . $product['min_stock_level'] . "</td>";
            echo "<td>" . htmlspecialchars($product['location'] ?? 'غير محدد') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص النتيجة: " . $e->getMessage() . "</p>";
}

if (empty($errors)) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎉 تم تحديث قاعدة البيانات بنجاح!</h4>";
    echo "<p>يمكنك الآن استخدام نظام إدارة المخزون</p>";
    echo "<h5>الصفحات الجديدة:</h5>";
    echo "<ul>";
    echo "<li><a href='inventory.php'>إدارة المخزون</a></li>";
    echo "<li><a href='stock-movements.php'>حركات المخزون</a></li>";
    echo "<li><a href='activity-logs.php'>سجل الأنشطة</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>⚠️ تم التحديث مع بعض الأخطاء</h4>";
    echo "<p>تحقق من الأخطاء أعلاه وحاول إصلاحها</p>";
    echo "</div>";
}

echo "<h3>9. روابط مفيدة:</h3>";
echo "<a href='inventory.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة المخزون</a>";
echo "<a href='stock-movements.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>حركات المخزون</a>";
echo "<a href='products.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة المنتجات</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
?>
