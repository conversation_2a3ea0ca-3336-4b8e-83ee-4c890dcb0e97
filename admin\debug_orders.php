<?php
/**
 * تشخيص مشكلة عدم ظهور الطلبات في الإدارة
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

echo "<h2>تشخيص مشكلة عدم ظهور الطلبات في الإدارة</h2>";

// فحص جدول الطلبات
echo "<h3>1. فحص جدول الطلبات:</h3>";

try {
    $ordersCount = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
    echo "<p style='color: green;'>✅ جدول orders موجود - عدد الطلبات: $ordersCount</p>";
    
    if ($ordersCount == 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>⚠️ لا توجد طلبات في قاعدة البيانات</h4>";
        echo "<p>تحتاج لإنشاء طلبات أولاً لاختبار صفحة الإدارة</p>";
        echo "<a href='../add_to_cart_test.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتجات للسلة</a>";
        echo "<a href='../checkout.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إتمام طلب</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الوصول لجدول orders: " . $e->getMessage() . "</p>";
}

// فحص جدول تفاصيل الطلبات
echo "<h3>2. فحص جدول تفاصيل الطلبات:</h3>";

try {
    $orderDetailsCount = $database->fetch("SELECT COUNT(*) as count FROM order_details")['count'];
    echo "<p style='color: green;'>✅ جدول order_details موجود - عدد التفاصيل: $orderDetailsCount</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الوصول لجدول order_details: " . $e->getMessage() . "</p>";
}

// اختبار الاستعلام المستخدم في orders.php
echo "<h3>3. اختبار استعلام orders.php:</h3>";

$page = 1;
$itemsPerPage = defined('ADMIN_ITEMS_PER_PAGE') ? ADMIN_ITEMS_PER_PAGE : 20;
$offset = ($page - 1) * $itemsPerPage;

echo "<p><strong>معاملات:</strong></p>";
echo "<ul>";
echo "<li>الصفحة: $page</li>";
echo "<li>عناصر في الصفحة: $itemsPerPage</li>";
echo "<li>الإزاحة: $offset</li>";
echo "</ul>";

// محاكاة نفس الاستعلام
$whereClause = '';
$params = [];

$sql = "SELECT * FROM orders {$whereClause} ORDER BY created_at DESC LIMIT " . intval($itemsPerPage) . " OFFSET " . intval($offset);

echo "<p><strong>الاستعلام:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$sql</pre>";

try {
    $orders = $database->fetchAll($sql, $params);
    echo "<p style='color: green;'>✅ تم تنفيذ الاستعلام بنجاح - عدد النتائج: " . count($orders) . "</p>";
    
    if (count($orders) > 0) {
        echo "<h4>عينة من الطلبات:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>رقم الطلب</th><th>اسم العميل</th><th>المبلغ</th><th>الحالة</th><th>التاريخ</th>";
        echo "</tr>";
        
        foreach (array_slice($orders, 0, 5) as $order) {
            echo "<tr>";
            echo "<td>" . $order['id'] . "</td>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td>" . $order['status'] . "</td>";
            echo "<td>" . formatDate($order['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "</p>";
}

// اختبار استعلام العد
echo "<h3>4. اختبار استعلام العد:</h3>";

$countSql = "SELECT COUNT(*) as total FROM orders {$whereClause}";
$countParams = [];

echo "<p><strong>استعلام العد:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>$countSql</pre>";

try {
    $totalResult = $database->fetch($countSql, $countParams);
    $totalOrders = $totalResult['total'];
    echo "<p style='color: green;'>✅ إجمالي الطلبات: $totalOrders</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في استعلام العد: " . $e->getMessage() . "</p>";
}

// اختبار OrderManager
echo "<h3>5. اختبار OrderManager:</h3>";

try {
    if (isset($orderManager)) {
        echo "<p style='color: green;'>✅ متغير orderManager موجود</p>";
        
        // اختبار getOrderById
        if (!empty($orders)) {
            $firstOrderId = $orders[0]['id'];
            $orderDetails = $orderManager->getOrderById($firstOrderId);
            if ($orderDetails) {
                echo "<p style='color: green;'>✅ getOrderById يعمل</p>";
            } else {
                echo "<p style='color: red;'>❌ getOrderById لا يعمل</p>";
            }
            
            // اختبار getOrderDetails
            $orderItems = $orderManager->getOrderDetails($firstOrderId);
            echo "<p style='color: green;'>✅ getOrderDetails يعمل - عدد العناصر: " . count($orderItems) . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ متغير orderManager غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في OrderManager: " . $e->getMessage() . "</p>";
}

// إنشاء طلب تجريبي إذا لم توجد طلبات
if ($ordersCount == 0) {
    echo "<h3>6. إنشاء طلب تجريبي:</h3>";
    
    $testOrderData = [
        'customer_name' => 'عميل تجريبي للإدارة',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '0501234567',
        'customer_address' => 'عنوان تجريبي للاختبار',
        'total_amount' => 150.00,
        'payment_method' => 'cash_on_delivery',
        'notes' => 'طلب تجريبي لاختبار صفحة الإدارة',
        'status' => 'pending'
    ];
    
    $testOrderItems = [
        [
            'product_id' => 1,
            'product_name' => 'منتج تجريبي 1',
            'product_price' => 50.00,
            'quantity' => 2
        ],
        [
            'product_id' => 2,
            'product_name' => 'منتج تجريبي 2',
            'product_price' => 25.00,
            'quantity' => 2
        ]
    ];
    
    try {
        $testOrderId = $orderManager->createOrder($testOrderData, $testOrderItems);
        
        if ($testOrderId) {
            echo "<p style='color: green;'>✅ تم إنشاء طلب تجريبي بنجاح - معرف الطلب: $testOrderId</p>";
            echo "<p><strong>الآن يمكنك تحديث صفحة orders.php لرؤية الطلب</strong></p>";
        } else {
            echo "<p style='color: red;'>❌ فشل في إنشاء الطلب التجريبي</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في إنشاء الطلب التجريبي: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>7. النتيجة والحلول:</h3>";

if ($ordersCount > 0 && count($orders) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ الطلبات موجودة ويمكن استرجاعها!</h4>";
    echo "<p>المشكلة قد تكون في:</p>";
    echo "<ul>";
    echo "<li>ملف CSS للإدارة</li>";
    echo "<li>JavaScript في الصفحة</li>";
    echo "<li>مشكلة في عرض HTML</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ لا توجد طلبات أو مشكلة في الاستعلام</h4>";
    echo "<p>الحلول:</p>";
    echo "<ul>";
    echo "<li>إنشاء طلبات تجريبية</li>";
    echo "<li>فحص الاستعلام SQL</li>";
    echo "<li>فحص معاملات LIMIT/OFFSET</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h3>8. روابط مفيدة:</h3>";
echo "<a href='orders.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الطلبات</a>";
echo "<a href='../add_to_cart_test.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة للسلة</a>";
echo "<a href='../checkout.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إتمام طلب</a>";
echo "<a href='../debug_checkout.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص checkout</a>";

// عرض آخر الطلبات مباشرة من قاعدة البيانات
if ($ordersCount > 0) {
    echo "<h3>9. آخر الطلبات مباشرة من قاعدة البيانات:</h3>";
    
    try {
        $latestOrders = $database->fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 5");
        
        if (count($latestOrders) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>رقم الطلب</th><th>العميل</th><th>البريد</th><th>المبلغ</th><th>الحالة</th><th>التاريخ</th>";
            echo "</tr>";
            
            foreach ($latestOrders as $order) {
                echo "<tr>";
                echo "<td>" . $order['id'] . "</td>";
                echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
                echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
                echo "<td>" . htmlspecialchars($order['customer_email']) . "</td>";
                echo "<td>" . formatPrice($order['total_amount']) . "</td>";
                echo "<td>" . $order['status'] . "</td>";
                echo "<td>" . formatDate($order['created_at']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في عرض آخر الطلبات: " . $e->getMessage() . "</p>";
    }
}
?>
