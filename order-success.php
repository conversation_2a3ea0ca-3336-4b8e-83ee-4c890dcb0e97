<?php
/**
 * صفحة تأكيد الطلب
 * Order Success Page
 */

require_once 'includes/functions.php';

// الحصول على معرف الطلب
$orderId = isset($_GET['order']) ? intval($_GET['order']) : 0;

if ($orderId <= 0) {
    header('Location: index.php');
    exit;
}

// الحصول على بيانات الطلب
$order = $orderManager->getOrderById($orderId);

if (!$order) {
    header('Location: index.php');
    exit;
}

// الحصول على تفاصيل الطلب
$orderDetails = $orderManager->getOrderDetails($orderId);

// تسجيل النشاط
logActivity('order_success_view', "عرض صفحة تأكيد الطلب رقم: {$order['order_number']}");

$pageTitle = "تم تأكيد طلبك - " . getSiteSetting('site_name');
$pageDescription = "تم تأكيد طلبك بنجاح وسيتم التواصل معك قريباً";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartManager->getCartCount(); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Success Message -->
            <div class="order-success text-center mb-5">
                <div class="success-icon mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="success-title">تم تأكيد طلبك بنجاح!</h1>
                <p class="success-message">شكراً لك على ثقتك بنا. تم استلام طلبك وسيتم التواصل معك قريباً لتأكيد التفاصيل.</p>
                
                <div class="order-number-box">
                    <h3>رقم الطلب: <span class="text-primary"><?php echo $order['order_number']; ?></span></h3>
                    <p class="text-muted">احتفظ بهذا الرقم للمراجعة</p>
                </div>
            </div>

            <div class="row">
                <!-- Order Details -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-receipt"></i> تفاصيل الطلب</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>رقم الطلب:</strong> <?php echo $order['order_number']; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>تاريخ الطلب:</strong> <?php echo formatDate($order['created_at'], 'd/m/Y H:i'); ?>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>حالة الطلب:</strong> 
                                    <span class="badge bg-warning">قيد المراجعة</span>
                                </div>
                                <div class="col-md-6">
                                    <strong>طريقة الدفع:</strong> 
                                    <?php echo ($order['payment_method'] == 'cash_on_delivery') ? 'الدفع عند الاستلام' : 'تحويل بنكي'; ?>
                                </div>
                            </div>
                            
                            <!-- Order Items -->
                            <h6 class="mt-4 mb-3">المنتجات المطلوبة:</h6>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>المجموع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orderDetails as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($item['image']): ?>
                                                    <img src="<?php echo UPLOADS_URL . '/' . $item['image']; ?>" alt="<?php echo $item['product_name']; ?>" width="50" height="50" class="rounded me-3">
                                                    <?php endif; ?>
                                                    <span><?php echo $item['product_name']; ?></span>
                                                </div>
                                            </td>
                                            <td><?php echo formatPrice($item['product_price']); ?></td>
                                            <td><?php echo $item['quantity']; ?></td>
                                            <td><?php echo formatPrice($item['total']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3">المجموع الكلي:</th>
                                            <th><?php echo formatPrice($order['total_amount']); ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-user"></i> بيانات العميل</h5>
                        </div>
                        <div class="card-body">
                            <div class="customer-info">
                                <div class="info-item mb-2">
                                    <strong>الاسم:</strong> <?php echo $order['customer_name']; ?>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>البريد الإلكتروني:</strong> <?php echo $order['customer_email']; ?>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>الهاتف:</strong> <?php echo $order['customer_phone']; ?>
                                </div>
                                <div class="info-item">
                                    <strong>العنوان:</strong><br>
                                    <?php echo nl2br($order['customer_address']); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Next Steps -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-info-circle"></i> الخطوات التالية</h5>
                        </div>
                        <div class="card-body">
                            <div class="next-steps">
                                <div class="step-item mb-3">
                                    <i class="fas fa-phone text-primary"></i>
                                    <div class="step-content">
                                        <strong>التأكيد</strong>
                                        <p class="mb-0">سيتم التواصل معك خلال 24 ساعة لتأكيد الطلب</p>
                                    </div>
                                </div>
                                <div class="step-item mb-3">
                                    <i class="fas fa-box text-warning"></i>
                                    <div class="step-content">
                                        <strong>التحضير</strong>
                                        <p class="mb-0">سيتم تحضير طلبك وتجهيزه للشحن</p>
                                    </div>
                                </div>
                                <div class="step-item">
                                    <i class="fas fa-truck text-success"></i>
                                    <div class="step-content">
                                        <strong>التوصيل</strong>
                                        <p class="mb-0">سيتم توصيل الطلب إلى عنوانك المحدد</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="order-actions text-center mt-5">
                <a href="<?php echo SITE_URL; ?>" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-home"></i> العودة للرئيسية
                </a>
                <a href="products.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-shopping-bag"></i> متابعة التسوق
                </a>
            </div>

            <!-- Contact Information -->
            <div class="contact-info mt-5 p-4 bg-light rounded">
                <div class="row text-center">
                    <div class="col-md-4">
                        <i class="fas fa-phone fa-2x text-primary mb-2"></i>
                        <h6>اتصل بنا</h6>
                        <p><?php echo getSiteSetting('contact_phone'); ?></p>
                    </div>
                    <div class="col-md-4">
                        <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                        <h6>راسلنا</h6>
                        <p><?php echo getSiteSetting('contact_email'); ?></p>
                    </div>
                    <div class="col-md-4">
                        <i class="fab fa-whatsapp fa-2x text-success mb-2"></i>
                        <h6>واتساب</h6>
                        <p>للاستفسارات السريعة</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
    
    <script>
    // إرسال إشعار للإدارة (يمكن تطويره لاحقاً)
    console.log('تم إنشاء طلب جديد: <?php echo $order['order_number']; ?>');
    
    // تتبع التحويل للإحصائيات
    if (typeof gtag !== 'undefined') {
        gtag('event', 'purchase', {
            'transaction_id': '<?php echo $order['order_number']; ?>',
            'value': <?php echo $order['total_amount']; ?>,
            'currency': 'SAR'
        });
    }
    </script>
</body>
</html>
