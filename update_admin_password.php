<?php
/**
 * تحديث كلمة مرور المدير
 * Update Admin Password
 */

require_once 'includes/functions.php';

// كلمة المرور الجديدة
$newPassword = 'password';
$hashedPassword = hashPassword($newPassword);

// تحديث كلمة المرور في قاعدة البيانات
$sql = "UPDATE users SET password = :password WHERE username = 'admin'";
$result = $database->query($sql, ['password' => $hashedPassword]);

if ($result) {
    echo "تم تحديث كلمة مرور المدير بنجاح!<br>";
    echo "اسم المستخدم: admin<br>";
    echo "كلمة المرور: password<br>";
    echo "<br><a href='admin/login.php'>انتقل لصفحة تسجيل الدخول</a>";
} else {
    echo "حدث خطأ أثناء تحديث كلمة المرور";
}
?>
