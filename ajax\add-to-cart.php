<?php
/**
 * إضافة منتج إلى السلة
 * Add Product to Cart
 */

header('Content-Type: application/json');
require_once '../includes/functions.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    $productId = intval($input['product_id'] ?? 0);
    $quantity = intval($input['quantity'] ?? 1);
    
    // التحقق من صحة البيانات
    if ($productId <= 0) {
        throw new Exception('معرف المنتج غير صحيح');
    }
    
    if ($quantity <= 0) {
        throw new Exception('الكمية يجب أن تكون أكبر من صفر');
    }
    
    // التحقق من وجود المنتج
    $product = $productManager->getProductById($productId);
    if (!$product) {
        throw new Exception('المنتج غير موجود');
    }
    
    // التحقق من حالة المنتج
    if ($product['status'] !== 'active') {
        throw new Exception('المنتج غير متاح حالياً');
    }
    
    // التحقق من المخزون
    if ($product['stock_quantity'] < $quantity) {
        throw new Exception('الكمية المطلوبة غير متوفرة في المخزون');
    }
    
    // إضافة المنتج إلى السلة
    $cartManager->addToCart($productId, $quantity);
    
    // تسجيل النشاط
    logActivity('add_to_cart', "تم إضافة منتج {$product['name']} إلى السلة");
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة المنتج إلى السلة بنجاح',
        'cart_count' => $cartManager->getCartCount(),
        'cart_total' => $cartManager->getCartTotal()
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
