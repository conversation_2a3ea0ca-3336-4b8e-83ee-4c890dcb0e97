<?php
/**
 * صفحة سلة التسوق
 * Shopping Cart Page
 */

require_once 'includes/functions.php';

// معالجة تحديث السلة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_cart'])) {
        foreach ($_POST['quantities'] as $productId => $quantity) {
            $cartManager->updateCartQuantity($productId, intval($quantity));
        }
        setMessage('تم تحديث السلة بنجاح', 'success');
        header('Location: cart.php');
        exit;
    }
    
    if (isset($_POST['remove_item'])) {
        $productId = intval($_POST['product_id']);
        $cartManager->removeFromCart($productId);
        setMessage('تم حذف المنتج من السلة', 'success');
        header('Location: cart.php');
        exit;
    }
    
    if (isset($_POST['clear_cart'])) {
        $cartManager->clearCart();
        setMessage('تم إفراغ السلة', 'info');
        header('Location: cart.php');
        exit;
    }
}

// الحصول على عناصر السلة
$cartItems = $cartManager->getCartItems();
$cartTotal = $cartManager->getCartTotal();
$cartCount = $cartManager->getCartCount();

// تسجيل النشاط
logActivity('cart_view', 'عرض سلة التسوق');

$pageTitle = "سلة التسوق - " . getSiteSetting('site_name');
$pageDescription = "راجع منتجاتك المختارة وأكمل عملية الشراء";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon active">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartCount; ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="navbar-nav d-flex flex-row">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link">الرئيسية</a>
                </li>
                <?php 
                $mainCategories = $categoryManager->getMainCategories();
                foreach ($mainCategories as $category): 
                ?>
                <li class="nav-item dropdown">
                    <a href="products.php?category=<?php echo $category['id']; ?>" class="nav-link">
                        <?php echo $category['name']; ?>
                    </a>
                </li>
                <?php endforeach; ?>
                <li class="nav-item">
                    <a href="contact.php" class="nav-link">اتصل بنا</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">سلة التسوق</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Messages -->
            <?php 
            $message = getMessage();
            if ($message): 
            ?>
            <div class="alert alert-<?php echo $message['type']; ?> alert-dismissible fade show">
                <?php echo $message['message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <h1 class="page-title">سلة التسوق</h1>
                    
                    <?php if (!empty($cartItems)): ?>
                    <!-- Cart Items -->
                    <div class="cart-content">
                        <form method="POST" action="cart.php">
                            <div class="table-responsive">
                                <table class="table cart-table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>السعر</th>
                                            <th>الكمية</th>
                                            <th>المجموع</th>
                                            <th>إجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($cartItems as $item): ?>
                                        <tr data-product-id="<?php echo $item['id']; ?>">
                                            <td class="product-info">
                                                <div class="d-flex align-items-center">
                                                    <div class="product-image me-3">
                                                        <?php if ($item['image']): ?>
                                                        <img src="<?php echo UPLOADS_URL . '/' . $item['image']; ?>" alt="<?php echo $item['name']; ?>" width="80" height="80" class="rounded">
                                                        <?php else: ?>
                                                        <img src="<?php echo ASSETS_PATH; ?>/images/no-image.jpg" alt="<?php echo $item['name']; ?>" width="80" height="80" class="rounded">
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="product-details">
                                                        <h5><a href="product-details.php?id=<?php echo $item['id']; ?>"><?php echo $item['name']; ?></a></h5>
                                                        <p class="text-muted mb-1"><?php echo $item['category_name']; ?></p>
                                                        <?php if ($item['weight']): ?>
                                                        <small class="text-muted">الوزن: <?php echo $item['weight']; ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="product-price">
                                                <?php if ($item['sale_price']): ?>
                                                <span class="current-price"><?php echo formatPrice($item['sale_price']); ?></span>
                                                <br><small class="old-price"><?php echo formatPrice($item['price']); ?></small>
                                                <?php else: ?>
                                                <span class="current-price"><?php echo formatPrice($item['price']); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="product-quantity">
                                                <div class="quantity-controls">
                                                    <div class="input-group" style="max-width: 120px;">
                                                        <button type="button" class="btn btn-outline-secondary quantity-btn" data-action="decrease">-</button>
                                                        <input type="number" class="form-control text-center" name="quantities[<?php echo $item['id']; ?>]" value="<?php echo $item['cart_quantity']; ?>" min="1" max="<?php echo $item['stock_quantity']; ?>" data-product-id="<?php echo $item['id']; ?>">
                                                        <button type="button" class="btn btn-outline-secondary quantity-btn" data-action="increase">+</button>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="product-total">
                                                <strong><?php echo formatPrice($item['cart_total']); ?></strong>
                                            </td>
                                            <td class="product-actions">
                                                <button type="submit" name="remove_item" value="1" class="btn btn-outline-danger btn-sm" onclick="return confirm('هل تريد حذف هذا المنتج؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <input type="hidden" name="product_id" value="<?php echo $item['id']; ?>">
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Cart Actions -->
                            <div class="cart-actions mt-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="submit" name="update_cart" class="btn btn-secondary">
                                            <i class="fas fa-sync"></i> تحديث السلة
                                        </button>
                                        <button type="submit" name="clear_cart" class="btn btn-outline-danger ms-2" onclick="return confirm('هل تريد إفراغ السلة؟')">
                                            <i class="fas fa-trash"></i> إفراغ السلة
                                        </button>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <a href="products.php" class="btn btn-outline-primary">
                                            <i class="fas fa-arrow-right"></i> متابعة التسوق
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <!-- Cart Summary -->
                        <div class="cart-summary mt-5">
                            <div class="row">
                                <div class="col-lg-8">
                                    <!-- Coupon Code -->
                                    <div class="coupon-section">
                                        <h5>كود الخصم</h5>
                                        <form class="coupon-form">
                                            <div class="input-group">
                                                <input type="text" class="form-control" placeholder="أدخل كود الخصم">
                                                <button type="submit" class="btn btn-outline-secondary">تطبيق</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="cart-totals">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>ملخص الطلب</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="total-row">
                                                    <span>المجموع الفرعي:</span>
                                                    <span class="cart-total"><?php echo formatPrice($cartTotal); ?></span>
                                                </div>
                                                <div class="total-row">
                                                    <span>الشحن:</span>
                                                    <span>مجاني</span>
                                                </div>
                                                <div class="total-row">
                                                    <span>الضريبة (15%):</span>
                                                    <span><?php echo formatPrice($cartTotal * 0.15); ?></span>
                                                </div>
                                                <hr>
                                                <div class="total-row final-total">
                                                    <strong>
                                                        <span>المجموع الكلي:</span>
                                                        <span><?php echo formatPrice($cartTotal * 1.15); ?></span>
                                                    </strong>
                                                </div>
                                                
                                                <div class="checkout-actions mt-3">
                                                    <a href="checkout.php" class="btn btn-primary btn-lg w-100">
                                                        <i class="fas fa-credit-card"></i> إتمام الطلب
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php else: ?>
                    <!-- Empty Cart -->
                    <div class="empty-cart text-center py-5">
                        <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
                        <h3>سلة التسوق فارغة</h3>
                        <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
                        <a href="products.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-shopping-bag"></i> ابدأ التسوق
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
    
    <script>
    // تحديث الكمية تلقائياً
    document.querySelectorAll('input[name^="quantities"]').forEach(function(input) {
        input.addEventListener('change', function() {
            const productId = this.dataset.productId;
            const quantity = parseInt(this.value);
            
            if (quantity > 0) {
                updateCartQuantity(productId, quantity);
            }
        });
    });
    
    // تحديث المجموع عند تغيير الكمية
    function updateCartTotal() {
        // يمكن إضافة كود لتحديث المجموع بدون إعادة تحميل الصفحة
        location.reload();
    }
    </script>
</body>
</html>
