/**
 * JavaScript الرئيسي للموقع
 * Main JavaScript File
 */

// متغيرات عامة
const SITE_URL = window.location.origin + '/g8';

// عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة الأحداث
    initializeEvents();
    
    // تحديث عداد السلة
    updateCartCount();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إخفاء الرسائل تلقائياً بعد 5 ثوان
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            alert.style.transition = 'opacity 0.5s';
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 500);
        });
    }, 5000);
    
    // تهيئة tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تهيئة الأحداث
 */
function initializeEvents() {
    // البحث السريع
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                performSearch();
            }
        });
    }
    
    // أزرار الكمية
    const quantityButtons = document.querySelectorAll('.quantity-btn');
    quantityButtons.forEach(function(btn) {
        btn.addEventListener('click', function() {
            updateQuantity(this);
        });
    });
    
    // تأكيد الحذف
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من الحذف؟')) {
                e.preventDefault();
            }
        });
    });
}

/**
 * إضافة منتج إلى السلة
 */
function addToCart(productId, quantity = 1) {
    // التحقق من صحة البيانات
    if (!productId || productId <= 0) {
        showMessage('خطأ في بيانات المنتج', 'error');
        return;
    }
    
    // إرسال طلب AJAX
    fetch(SITE_URL + '/ajax/add-to-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('تم إضافة المنتج إلى السلة بنجاح', 'success');
            updateCartCount();
            
            // تأثير بصري على زر السلة
            const cartIcon = document.querySelector('.cart-icon');
            if (cartIcon) {
                cartIcon.classList.add('animate-bounce');
                setTimeout(() => {
                    cartIcon.classList.remove('animate-bounce');
                }, 1000);
            }
        } else {
            showMessage(data.message || 'حدث خطأ أثناء إضافة المنتج', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
    });
}

/**
 * حذف منتج من السلة
 */
function removeFromCart(productId) {
    if (!confirm('هل تريد حذف هذا المنتج من السلة؟')) {
        return;
    }
    
    fetch(SITE_URL + '/ajax/remove-from-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('تم حذف المنتج من السلة', 'success');
            updateCartCount();
            
            // إزالة العنصر من الصفحة
            const cartItem = document.querySelector(`[data-product-id="${productId}"]`);
            if (cartItem) {
                cartItem.remove();
            }
            
            // تحديث المجموع
            updateCartTotal();
        } else {
            showMessage(data.message || 'حدث خطأ أثناء حذف المنتج', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
    });
}

/**
 * تحديث كمية المنتج في السلة
 */
function updateCartQuantity(productId, quantity) {
    if (quantity < 1) {
        removeFromCart(productId);
        return;
    }
    
    fetch(SITE_URL + '/ajax/update-cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartCount();
            updateCartTotal();
        } else {
            showMessage(data.message || 'حدث خطأ أثناء تحديث الكمية', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
    });
}

/**
 * تحديث عداد السلة
 */
function updateCartCount() {
    fetch(SITE_URL + '/ajax/get-cart-count.php')
    .then(response => response.json())
    .then(data => {
        const cartCountElement = document.querySelector('.cart-count');
        if (cartCountElement) {
            cartCountElement.textContent = data.count || 0;
        }
    })
    .catch(error => {
        console.error('Error updating cart count:', error);
    });
}

/**
 * تحديث مجموع السلة
 */
function updateCartTotal() {
    fetch(SITE_URL + '/ajax/get-cart-total.php')
    .then(response => response.json())
    .then(data => {
        const totalElement = document.querySelector('.cart-total');
        if (totalElement) {
            totalElement.textContent = data.total || '0.00';
        }
    })
    .catch(error => {
        console.error('Error updating cart total:', error);
    });
}

/**
 * تحديث الكمية
 */
function updateQuantity(button) {
    const input = button.parentElement.querySelector('input[type="number"]');
    const action = button.dataset.action;
    let currentValue = parseInt(input.value) || 1;
    
    if (action === 'increase') {
        currentValue++;
    } else if (action === 'decrease' && currentValue > 1) {
        currentValue--;
    }
    
    input.value = currentValue;
    
    // إذا كنا في صفحة السلة، تحديث الكمية
    const productId = input.dataset.productId;
    if (productId) {
        updateCartQuantity(productId, currentValue);
    }
}

/**
 * البحث
 */
function performSearch() {
    const searchInput = document.querySelector('.search-box input');
    const query = searchInput.value.trim();
    
    if (query.length < 2) {
        showMessage('يجب أن يكون البحث أكثر من حرفين', 'warning');
        return;
    }
    
    window.location.href = SITE_URL + '/products.php?search=' + encodeURIComponent(query);
}

/**
 * عرض رسالة
 */
function showMessage(message, type = 'info') {
    // إنشاء عنصر الرسالة
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إضافة الرسالة إلى أعلى الصفحة
    const container = document.querySelector('.main-content .container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
    }
    
    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

/**
 * تأكيد الطلب
 */
function confirmOrder() {
    return confirm('هل أنت متأكد من تأكيد الطلب؟');
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

/**
 * تنسيق السعر
 */
function formatPrice(price) {
    return formatNumber(price) + ' ريال';
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

/**
 * التحقق من صحة رقم الهاتف
 */
function validatePhone(phone) {
    const re = /^[0-9+\-\s()]+$/;
    return re.test(phone) && phone.length >= 10;
}

/**
 * تحميل المزيد من المنتجات (للتمرير اللانهائي)
 */
function loadMoreProducts() {
    const loadMoreBtn = document.querySelector('.load-more-btn');
    if (loadMoreBtn) {
        loadMoreBtn.textContent = 'جاري التحميل...';
        loadMoreBtn.disabled = true;
        
        const page = parseInt(loadMoreBtn.dataset.page) || 1;
        const nextPage = page + 1;
        
        fetch(SITE_URL + '/ajax/load-products.php?page=' + nextPage)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.products.length > 0) {
                const productsContainer = document.querySelector('.products-grid');
                productsContainer.insertAdjacentHTML('beforeend', data.html);
                
                loadMoreBtn.dataset.page = nextPage;
                loadMoreBtn.textContent = 'تحميل المزيد';
                loadMoreBtn.disabled = false;
                
                if (data.products.length < 12) {
                    loadMoreBtn.style.display = 'none';
                }
            } else {
                loadMoreBtn.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            loadMoreBtn.textContent = 'تحميل المزيد';
            loadMoreBtn.disabled = false;
        });
    }
}

/**
 * تبديل المفضلة
 */
function toggleWishlist(productId) {
    fetch(SITE_URL + '/ajax/toggle-wishlist.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const wishlistBtn = document.querySelector(`[data-wishlist-id="${productId}"]`);
            if (wishlistBtn) {
                wishlistBtn.classList.toggle('active');
                const icon = wishlistBtn.querySelector('i');
                if (icon) {
                    icon.classList.toggle('fas');
                    icon.classList.toggle('far');
                }
            }
            showMessage(data.message, 'success');
        } else {
            showMessage(data.message || 'حدث خطأ', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('حدث خطأ في الاتصال', 'error');
    });
}

// CSS للتأثيرات البصرية
const style = document.createElement('style');
style.textContent = `
    .animate-bounce {
        animation: bounce 1s ease-in-out;
    }
    
    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }
    
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }
`;
document.head.appendChild(style);
