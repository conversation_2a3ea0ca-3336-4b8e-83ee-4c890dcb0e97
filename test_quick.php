<?php
/**
 * اختبار سريع للتأكد من عمل النظام
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار سريع للنظام</h2>";

try {
    require_once 'includes/functions.php';
    echo "<p style='color: green;'>✅ تم تحميل functions.php بنجاح</p>";
    
    // اختبار قاعدة البيانات
    $productsCount = $database->fetch("SELECT COUNT(*) as count FROM products")['count'];
    echo "<p style='color: green;'>✅ قاعدة البيانات تعمل - عدد المنتجات: $productsCount</p>";
    
    // اختبار ProductManager
    $products = $productManager->getAllProducts(1, 5, 'active');
    echo "<p style='color: green;'>✅ ProductManager يعمل - تم استرجاع " . count($products) . " منتجات</p>";
    
    // اختبار CategoryManager
    $categories = $categoryManager->getMainCategories();
    echo "<p style='color: green;'>✅ CategoryManager يعمل - عدد الأقسام: " . count($categories) . "</p>";
    
    // اختبار الدوال المساعدة
    $testPrice = formatPrice(99.99);
    echo "<p style='color: green;'>✅ formatPrice يعمل: $testPrice</p>";
    
    $testText = truncateText('هذا نص طويل للاختبار', 20);
    echo "<p style='color: green;'>✅ truncateText يعمل: $testText</p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 جميع الاختبارات نجحت!</h3>";
    echo "<p>النظام جاهز للعمل</p>";
    echo "<h4>الروابط:</h4>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "<a href='products.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المنتجات</a>";
    echo "<a href='admin/products.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
    echo "</div>";
    
    if (count($products) > 0) {
        echo "<h3>عينة من المنتجات:</h3>";
        echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
        foreach ($products as $product) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 200px;'>";
            echo "<h4>" . htmlspecialchars($product['name']) . "</h4>";
            echo "<p>القسم: " . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</p>";
            echo "<p>السعر: " . formatPrice($product['price']) . "</p>";
            if ($product['image']) {
                $imageUrl = UPLOADS_URL . '/' . $product['image'];
                echo "<img src='$imageUrl' alt='" . htmlspecialchars($product['name']) . "' style='width: 100%; max-width: 180px; height: auto; border-radius: 3px;'>";
            }
            echo "</div>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في النظام</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
