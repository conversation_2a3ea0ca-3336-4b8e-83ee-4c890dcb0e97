<?php
/**
 * إدارة المستخدمين
 * Users Management
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $fullName = cleanInput($_POST['full_name'] ?? '');
                $username = cleanInput($_POST['username'] ?? '');
                $email = cleanInput($_POST['email'] ?? '');
                $password = $_POST['password'] ?? '';
                $role = cleanInput($_POST['role'] ?? 'customer');
                $phone = cleanInput($_POST['phone'] ?? '');
                
                $errors = [];
                
                if (empty($fullName)) $errors[] = 'الاسم الكامل مطلوب';
                if (empty($username)) $errors[] = 'اسم المستخدم مطلوب';
                if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'البريد الإلكتروني غير صحيح';
                if (empty($password) || strlen($password) < 6) $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                
                // التحقق من عدم تكرار اسم المستخدم والبريد
                $existingUser = $database->fetch("SELECT id FROM users WHERE username = :username OR email = :email", 
                    ['username' => $username, 'email' => $email]);
                if ($existingUser) $errors[] = 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل';
                
                if (empty($errors)) {
                    $data = [
                        'full_name' => $fullName,
                        'username' => $username,
                        'email' => $email,
                        'password' => hashPassword($password),
                        'role' => $role,
                        'phone' => $phone
                    ];
                    
                    if ($database->insert('users', $data)) {
                        $message = 'تم إضافة المستخدم بنجاح';
                        $messageType = 'success';
                        logActivity('user_added', "تم إضافة مستخدم جديد: {$fullName}");
                    } else {
                        $message = 'حدث خطأ أثناء إضافة المستخدم';
                        $messageType = 'danger';
                    }
                } else {
                    $message = implode('<br>', $errors);
                    $messageType = 'danger';
                }
                break;
                
            case 'toggle_status':
                $userId = intval($_POST['user_id']);
                $newStatus = $_POST['status'] === 'active' ? 'inactive' : 'active';
                if ($database->update('users', ['status' => $newStatus], 'id = :id', ['id' => $userId])) {
                    $message = 'تم تحديث حالة المستخدم بنجاح';
                    $messageType = 'success';
                    logActivity('user_status_updated', "تم تحديث حالة مستخدم رقم {$userId} إلى {$newStatus}");
                } else {
                    $message = 'حدث خطأ أثناء تحديث حالة المستخدم';
                    $messageType = 'danger';
                }
                break;
                
            case 'delete':
                $userId = intval($_POST['user_id']);
                // منع حذف المستخدم الحالي
                if ($userId == $_SESSION['user_id']) {
                    $message = 'لا يمكنك حذف حسابك الخاص';
                    $messageType = 'warning';
                } else {
                    if ($database->delete('users', 'id = :id', ['id' => $userId])) {
                        $message = 'تم حذف المستخدم بنجاح';
                        $messageType = 'success';
                        logActivity('user_deleted', "تم حذف مستخدم رقم: {$userId}");
                    } else {
                        $message = 'حدث خطأ أثناء حذف المستخدم';
                        $messageType = 'danger';
                    }
                }
                break;
        }
    }
}

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$roleFilter = isset($_GET['role']) ? cleanInput($_GET['role']) : '';
$statusFilter = isset($_GET['status']) ? cleanInput($_GET['status']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(full_name LIKE :search OR username LIKE :search OR email LIKE :search OR phone LIKE :search)";
    $params['search'] = '%' . $search . '%';
}

if ($roleFilter) {
    $whereConditions[] = "role = :role";
    $params['role'] = $roleFilter;
}

if ($statusFilter) {
    $whereConditions[] = "status = :status";
    $params['status'] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على المستخدمين
$sql = "SELECT * FROM users {$whereClause} ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
$params['limit'] = $itemsPerPage;
$params['offset'] = $offset;

$users = $database->fetchAll($sql, $params);

// حساب إجمالي المستخدمين
$countSql = "SELECT COUNT(*) as total FROM users {$whereClause}";
$countParams = array_diff_key($params, ['limit' => '', 'offset' => '']);
$totalResult = $database->fetch($countSql, $countParams);
$totalUsers = $totalResult['total'];
$totalPages = ceil($totalUsers / $itemsPerPage);

$pageTitle = "إدارة المستخدمين - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php" class="active"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إدارة المستخدمين</h1>
                <div class="header-actions">
                    <button type="button" class="btn-admin btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-plus"></i> إضافة مستخدم جديد
                    </button>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">البحث والتصفية</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="users.php" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="الاسم، اسم المستخدم، البريد، الهاتف..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="role" class="form-label">الدور</label>
                                <select class="form-select" id="role" name="role">
                                    <option value="">جميع الأدوار</option>
                                    <option value="admin" <?php echo ($roleFilter === 'admin') ? 'selected' : ''; ?>>مدير</option>
                                    <option value="customer" <?php echo ($roleFilter === 'customer') ? 'selected' : ''; ?>>عميل</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo ($statusFilter === 'active') ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($statusFilter === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn-admin btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            المستخدمين (<?php echo number_format($totalUsers); ?>)
                        </h5>
                        <div class="card-actions">
                            <button type="button" class="btn-admin btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus"></i> إضافة مستخدم
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الدور</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $user['full_name']; ?></strong>
                                        </td>
                                        <td><?php echo $user['username']; ?></td>
                                        <td>
                                            <a href="mailto:<?php echo $user['email']; ?>"><?php echo $user['email']; ?></a>
                                        </td>
                                        <td>
                                            <?php if ($user['phone']): ?>
                                            <a href="tel:<?php echo $user['phone']; ?>"><?php echo $user['phone']; ?></a>
                                            <?php else: ?>
                                            <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $roleClass = $user['role'] === 'admin' ? 'danger' : 'primary';
                                            $roleText = $user['role'] === 'admin' ? 'مدير' : 'عميل';
                                            ?>
                                            <span class="badge badge-<?php echo $roleClass; ?>"><?php echo $roleText; ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = $user['status'] === 'active' ? 'success' : 'danger';
                                            $statusText = $user['status'] === 'active' ? 'نشط' : 'غير نشط';
                                            ?>
                                            <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td><?php echo formatDate($user['created_at'], 'd/m/Y'); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button type="button" class="btn-admin btn-info btn-sm" 
                                                        onclick="editUser(<?php echo htmlspecialchars(json_encode($user)); ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل تريد تغيير حالة هذا المستخدم؟')">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <input type="hidden" name="status" value="<?php echo $user['status']; ?>">
                                                    <button type="submit" class="btn-admin btn-warning btn-sm" title="تغيير الحالة">
                                                        <i class="fas fa-toggle-on"></i>
                                                    </button>
                                                </form>
                                                
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                    <button type="submit" class="btn-admin btn-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات المستخدمين" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <!-- No Users -->
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5>لا توجد مستخدمين</h5>
                            <p class="text-muted">
                                <?php if ($search || $roleFilter || $statusFilter): ?>
                                    لم نجد أي مستخدمين يطابقون معايير البحث المحددة
                                <?php else: ?>
                                    لم يتم إضافة أي مستخدمين بعد
                                <?php endif; ?>
                            </p>
                            <button type="button" class="btn-admin btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                                <i class="fas fa-plus"></i> إضافة مستخدم جديد
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="users.php">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">الدور *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="customer">عميل</option>
                                    <option value="admin">مدير</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function editUser(user) {
        // يمكن إضافة نافذة تعديل المستخدم هنا
        alert('ميزة التعديل ستتم إضافتها قريباً');
    }
    </script>
</body>
</html>
