<?php
/**
 * صفحة تسجيل دخول الإدارة
 * Admin Login Page
 */

require_once '../includes/functions.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isAdmin()) {
    header('Location: index.php');
    exit;
}

$error = '';
$message = '';

// رسالة تسجيل الخروج
if (isset($_GET['message'])) {
    $message = cleanInput($_GET['message']);
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = cleanInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // البحث عن المستخدم
        $sql = "SELECT * FROM users WHERE (username = :username OR email = :username) AND role = 'admin' AND status = 'active'";
        $user = $database->fetch($sql, ['username' => $username]);
        
        if ($user && verifyPassword($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['full_name'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['username'] = $user['username'];
            
            // تسجيل النشاط
            logActivity('admin_login', 'تسجيل دخول الإدارة', $user['id'], 'admin');
            
            // إعادة التوجيه
            $redirect = isset($_GET['redirect']) ? $_GET['redirect'] : 'index.php';
            header('Location: ' . $redirect);
            exit;
        } else {
            $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            logActivity('admin_login_failed', "محاولة دخول فاشلة: {$username}");
        }
    }
}

$pageTitle = "تسجيل دخول الإدارة - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #2d5016, #4a7c59);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .login-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        overflow: hidden;
        max-width: 400px;
        width: 100%;
    }
    
    .login-header {
        background: linear-gradient(135deg, #2d5016, #4a7c59);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .login-header i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .login-body {
        padding: 2rem;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        font-size: 1rem;
    }
    
    .form-control:focus {
        border-color: #2d5016;
        box-shadow: 0 0 0 0.2rem rgba(45, 80, 22, 0.25);
    }
    
    .btn-login {
        background: linear-gradient(135deg, #2d5016, #4a7c59);
        border: none;
        border-radius: 10px;
        padding: 12px;
        font-weight: 600;
        color: white;
        width: 100%;
        transition: transform 0.2s;
    }
    
    .btn-login:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .alert {
        border-radius: 10px;
        border: none;
    }
    
    .input-group-text {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-left: none;
    }
    
    .form-control.with-icon {
        border-right: none;
    }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Login Header -->
        <div class="login-header">
            <i class="fas fa-shield-alt"></i>
            <h2>لوحة التحكم</h2>
            <p class="mb-0">تسجيل دخول الإدارة</p>
        </div>
        
        <!-- Login Body -->
        <div class="login-body">
            <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
            </div>
            <?php endif; ?>

            <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo $message; ?>
            </div>
            <?php endif; ?>
            
            <form method="POST" action="login.php<?php echo isset($_GET['redirect']) ? '?redirect=' . urlencode($_GET['redirect']) : ''; ?>">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control with-icon" id="username" name="username" 
                               value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                               required autofocus>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" class="form-control with-icon" id="password" name="password" required>
                        <button type="button" class="btn btn-outline-secondary" onclick="togglePassword()">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            تذكرني
                        </label>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-login">
                    <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                </button>
            </form>
            
            <div class="text-center mt-4">
                <a href="<?php echo SITE_URL; ?>" class="text-muted">
                    <i class="fas fa-arrow-right"></i> العودة للموقع الرئيسي
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function togglePassword() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
    
    // تركيز تلقائي على حقل اسم المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('username').focus();
    });
    
    // منع إرسال النموذج إذا كانت الحقول فارغة
    document.querySelector('form').addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول');
        }
    });
    </script>
</body>
</html>
