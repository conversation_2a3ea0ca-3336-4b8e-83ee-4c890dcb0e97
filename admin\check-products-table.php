<?php
/**
 * فحص جدول المنتجات وإضافة عمود الحد الأدنى
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$message = '';
$messageType = 'info';

// معالجة إضافة الأعمدة المفقودة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_columns'])) {
    try {
        $columnsAdded = [];
        
        // إضافة عمود min_stock_level
        try {
            $database->query("ALTER TABLE products ADD COLUMN min_stock_level INT DEFAULT 5 AFTER stock_quantity");
            $columnsAdded[] = "min_stock_level";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                throw $e;
            }
        }
        
        // إضافة عمود stock_quantity إذا لم يكن موجود
        try {
            $database->query("ALTER TABLE products ADD COLUMN stock_quantity INT DEFAULT 0 AFTER price");
            $columnsAdded[] = "stock_quantity";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                throw $e;
            }
        }
        
        // إضافة عمود location
        try {
            $database->query("ALTER TABLE products ADD COLUMN location VARCHAR(100) DEFAULT NULL AFTER min_stock_level");
            $columnsAdded[] = "location";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                throw $e;
            }
        }
        
        // إضافة عمود sku
        try {
            $database->query("ALTER TABLE products ADD COLUMN sku VARCHAR(50) DEFAULT NULL AFTER location");
            $columnsAdded[] = "sku";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                throw $e;
            }
        }
        
        if (!empty($columnsAdded)) {
            $message = "تم إضافة الأعمدة التالية بنجاح: " . implode(', ', $columnsAdded);
            $messageType = 'success';
            
            // تحديث قيم افتراضية للمنتجات الموجودة
            if (in_array('min_stock_level', $columnsAdded)) {
                $database->query("UPDATE products SET min_stock_level = 5 WHERE min_stock_level IS NULL OR min_stock_level = 0");
            }
            
            if (in_array('stock_quantity', $columnsAdded)) {
                $database->query("UPDATE products SET stock_quantity = 10 WHERE stock_quantity IS NULL");
            }
            
            // إنشاء SKU للمنتجات الموجودة
            if (in_array('sku', $columnsAdded)) {
                $products = $database->fetchAll("SELECT id FROM products WHERE sku IS NULL OR sku = ''");
                foreach ($products as $product) {
                    $sku = 'PRD-' . str_pad($product['id'], 4, '0', STR_PAD_LEFT);
                    $database->update('products', ['sku' => $sku], 'id = :id', ['id' => $product['id']]);
                }
            }
            
            logActivity('products_table_updated', "تم تحديث جدول المنتجات وإضافة الأعمدة: " . implode(', ', $columnsAdded));
        } else {
            $message = "جميع الأعمدة موجودة بالفعل";
            $messageType = 'info';
        }
        
    } catch (Exception $e) {
        $message = "خطأ في إضافة الأعمدة: " . $e->getMessage();
        $messageType = 'danger';
    }
}

echo "<h2>فحص وإصلاح جدول المنتجات</h2>";

// عرض الرسائل
if ($message) {
    echo "<div style='padding: 15px; margin: 20px 0; border-radius: 5px; background: " . 
         ($messageType == 'success' ? '#d4edda; color: #155724;' : 
          ($messageType == 'danger' ? '#f8d7da; color: #721c24;' : '#d1ecf1; color: #0c5460;')) . "'>";
    echo $message;
    echo "</div>";
}

// فحص بنية جدول المنتجات
echo "<h3>1. بنية جدول المنتجات الحالية:</h3>";

try {
    $columns = $database->fetchAll("SHOW COLUMNS FROM products");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . ($column['Extra'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص الأعمدة المطلوبة
    echo "<h3>2. فحص الأعمدة المطلوبة:</h3>";
    
    $requiredColumns = [
        'stock_quantity' => 'كمية المخزون',
        'min_stock_level' => 'الحد الأدنى للمخزون',
        'location' => 'موقع المنتج',
        'sku' => 'رمز المنتج'
    ];
    
    $missingColumns = [];
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($requiredColumns as $column => $description) {
        $exists = in_array($column, $existingColumns);
        
        echo "<div style='background: " . ($exists ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 8px; text-align: center;'>";
        echo "<h4 style='margin: 0; color: " . ($exists ? '#155724' : '#721c24') . ";'>" . ($exists ? '✅' : '❌') . "</h4>";
        echo "<p style='margin: 5px 0 0 0;'><strong>$description</strong></p>";
        echo "<small>($column)</small>";
        echo "</div>";
        
        if (!$exists) {
            $missingColumns[] = $column;
        }
    }
    
    echo "</div>";
    
    // عرض عينة من البيانات
    echo "<h3>3. عينة من بيانات المنتجات:</h3>";
    
    $sampleProducts = $database->fetchAll("SELECT * FROM products LIMIT 5");
    
    if (!empty($sampleProducts)) {
        echo "<div style='overflow-x: auto;'>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; font-size: 12px;'>";
        echo "<tr style='background: #f0f0f0;'>";
        
        // عرض أسماء الأعمدة
        foreach (array_keys($sampleProducts[0]) as $columnName) {
            echo "<th style='padding: 5px;'>$columnName</th>";
        }
        echo "</tr>";
        
        // عرض البيانات
        foreach ($sampleProducts as $product) {
            echo "<tr>";
            foreach ($product as $value) {
                echo "<td style='padding: 5px;'>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<p style='color: red;'>لا توجد منتجات في الجدول</p>";
    }
    
    // إظهار زر الإصلاح إذا كانت هناك أعمدة مفقودة
    if (!empty($missingColumns)) {
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>⚠️ أعمدة مفقودة:</h3>";
        echo "<p>الأعمدة التالية مفقودة من جدول المنتجات:</p>";
        echo "<ul>";
        foreach ($missingColumns as $column) {
            echo "<li><strong>$column:</strong> " . $requiredColumns[$column] . "</li>";
        }
        echo "</ul>";
        
        echo "<form method='POST' style='margin-top: 20px;'>";
        echo "<button type='submit' name='add_columns' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;' onclick='return confirm(\"هل تريد إضافة الأعمدة المفقودة؟ هذا آمن ولن يؤثر على البيانات الموجودة.\");'>";
        echo "<i class='fas fa-plus'></i> إضافة الأعمدة المفقودة";
        echo "</button>";
        echo "</form>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h3>🎉 جدول المنتجات مكتمل!</h3>";
        echo "<p>جميع الأعمدة المطلوبة موجودة في جدول المنتجات</p>";
        echo "<a href='simple-bulk-update.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تجربة التحديث المجمع</a>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ خطأ في فحص الجدول</h3>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h3>4. اختبار التحديث بعد الإصلاح:</h3>";

if (empty($missingColumns ?? [])) {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🧪 اختبار سريع:</h4>";
    
    try {
        // اختبار تحديث منتج
        $testProduct = $database->fetch("SELECT id, name, min_stock_level FROM products LIMIT 1");
        
        if ($testProduct) {
            $newValue = ($testProduct['min_stock_level'] ?? 5) + 1;
            
            $updateResult = $database->update('products', 
                ['min_stock_level' => $newValue], 
                'id = :id', 
                ['id' => $testProduct['id']]
            );
            
            if ($updateResult) {
                echo "<p style='color: green;'>✅ اختبار التحديث نجح للمنتج: " . htmlspecialchars($testProduct['name']) . "</p>";
                echo "<p>تم تحديث الحد الأدنى إلى: $newValue</p>";
                
                // إعادة القيمة الأصلية
                $database->update('products', 
                    ['min_stock_level' => $testProduct['min_stock_level'] ?? 5], 
                    'id = :id', 
                    ['id' => $testProduct['id']]
                );
                echo "<p><small>تم إعادة القيمة الأصلية</small></p>";
            } else {
                echo "<p style='color: red;'>❌ فشل اختبار التحديث</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد منتجات للاختبار</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في اختبار التحديث: " . $e->getMessage() . "</p>";
    }
    echo "</div>";
}

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='simple-bulk-update.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>التحديث المجمع المبسط</a>";
echo "<a href='test-bulk-update.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار التحديث</a>";
echo "<a href='inventory.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المخزون</a>";
echo "<a href='products.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المنتجات</a>";
echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث قاعدة البيانات</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>إضافة الأعمدة آمنة ولن تؤثر على البيانات الموجودة</li>";
echo "<li>سيتم تعيين قيم افتراضية للمنتجات الموجودة</li>";
echo "<li>يمكن تشغيل هذا الإصلاح عدة مرات بأمان</li>";
echo "<li>تأكد من عمل نسخة احتياطية قبل التعديل على قاعدة البيانات</li>";
echo "</ul>";
echo "</div>";
?>
