<?php
/**
 * نسخة تشخيص من صفحة المنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

echo "<div style='background: #fff3cd; padding: 10px; margin: 10px; border-radius: 5px;'>";
echo "<h3>🔍 تشخيص صفحة المنتجات</h3>";

$message = '';
$messageType = 'info';

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$categoryFilter = isset($_GET['category']) ? intval($_GET['category']) : 0;
$statusFilter = isset($_GET['status']) ? cleanInput($_GET['status']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

echo "<p><strong>معاملات البحث:</strong></p>";
echo "<ul>";
echo "<li>البحث: '" . $search . "'</li>";
echo "<li>فلتر القسم: " . $categoryFilter . "</li>";
echo "<li>فلتر الحالة: '" . $statusFilter . "'</li>";
echo "<li>الصفحة: " . $page . "</li>";
echo "<li>عدد العناصر في الصفحة: " . $itemsPerPage . "</li>";
echo "<li>الإزاحة: " . $offset . "</li>";
echo "</ul>";

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
    $params['search'] = '%' . $search . '%';
}

if ($categoryFilter) {
    $whereConditions[] = "p.category_id = :category";
    $params['category'] = $categoryFilter;
}

if ($statusFilter) {
    $whereConditions[] = "p.status = :status";
    $params['status'] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

echo "<p><strong>شروط WHERE:</strong> " . ($whereClause ?: 'لا توجد شروط') . "</p>";

// الحصول على المنتجات
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        {$whereClause}
        ORDER BY p.created_at DESC 
        LIMIT :limit OFFSET :offset";

$params['limit'] = $itemsPerPage;
$params['offset'] = $offset;

echo "<p><strong>الاستعلام النهائي:</strong></p>";
echo "<code style='background: #f8f9fa; padding: 10px; display: block; border-radius: 3px;'>" . $sql . "</code>";

echo "<p><strong>المعاملات:</strong></p>";
echo "<ul>";
foreach ($params as $key => $value) {
    echo "<li>$key: " . (is_null($value) ? 'NULL' : $value) . "</li>";
}
echo "</ul>";

try {
    $products = $database->fetchAll($sql, $params);
    echo "<p><strong>✅ الاستعلام نجح!</strong></p>";
    echo "<p><strong>عدد المنتجات المسترجعة:</strong> " . count($products) . "</p>";
} catch (Exception $e) {
    echo "<p><strong>❌ خطأ في الاستعلام:</strong> " . $e->getMessage() . "</p>";
    $products = [];
}

// حساب إجمالي المنتجات
$countSql = "SELECT COUNT(*) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id {$whereClause}";
$countParams = array_diff_key($params, ['limit' => '', 'offset' => '']);

try {
    $totalResult = $database->fetch($countSql, $countParams);
    $totalProducts = $totalResult['total'];
    echo "<p><strong>إجمالي المنتجات:</strong> " . $totalProducts . "</p>";
} catch (Exception $e) {
    echo "<p><strong>❌ خطأ في حساب الإجمالي:</strong> " . $e->getMessage() . "</p>";
    $totalProducts = 0;
}

$totalPages = ceil($totalProducts / $itemsPerPage);

// الحصول على الأقسام للفلترة
try {
    $categories = $categoryManager->getAllCategories();
    echo "<p><strong>عدد الأقسام:</strong> " . count($categories) . "</p>";
} catch (Exception $e) {
    echo "<p><strong>❌ خطأ في الأقسام:</strong> " . $e->getMessage() . "</p>";
    $categories = [];
}

echo "<p><strong>اختبار الشروط:</strong></p>";
echo "<ul>";
echo "<li>empty(\$products): " . (empty($products) ? 'true' : 'false') . "</li>";
echo "<li>!empty(\$products): " . (!empty($products) ? 'true' : 'false') . "</li>";
echo "<li>count(\$products): " . count($products) . "</li>";
echo "<li>is_array(\$products): " . (is_array($products) ? 'true' : 'false') . "</li>";
echo "</ul>";

echo "</div>";

$pageTitle = "تشخيص المنتجات - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php" class="active"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">تشخيص المنتجات</h1>
                <div class="header-actions">
                    <a href="products.php" class="btn-admin btn-primary">
                        <i class="fas fa-arrow-left"></i> الصفحة الأصلية
                    </a>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Products Table -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            المنتجات (<?php echo number_format($totalProducts); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-info">
                            <h6>معلومات التشخيص:</h6>
                            <ul class="mb-0">
                                <li><strong>عدد المنتجات المسترجعة:</strong> <?php echo count($products); ?></li>
                                <li><strong>إجمالي المنتجات:</strong> <?php echo $totalProducts; ?></li>
                                <li><strong>الصفحة الحالية:</strong> <?php echo $page; ?></li>
                                <li><strong>إجمالي الصفحات:</strong> <?php echo $totalPages; ?></li>
                                <li><strong>شرط !empty($products):</strong> <?php echo (!empty($products) ? 'true ✅' : 'false ❌'); ?></li>
                            </ul>
                        </div>
                        
                        <?php if (!empty($products)): ?>
                        
                        <div class="alert alert-success">
                            ✅ <strong>المنتجات موجودة وسيتم عرضها</strong>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>القسم</th>
                                        <th>السعر</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td><?php echo $product['id']; ?></td>
                                        <td>
                                            <?php if ($product['image']): ?>
                                            <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                                                 alt="<?php echo $product['name']; ?>" 
                                                 width="50" height="50" class="rounded">
                                            <?php else: ?>
                                            <div class="no-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                            <?php if ($product['sku']): ?>
                                            <br><small class="text-muted">رقم المنتج: <?php echo $product['sku']; ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo formatPrice($product['price']); ?></td>
                                        <td><?php echo $product['stock_quantity']; ?></td>
                                        <td>
                                            <span class="badge badge-success"><?php echo $product['status']; ?></span>
                                        </td>
                                        <td>
                                            <a href="edit-product.php?id=<?php echo $product['id']; ?>" 
                                               class="btn-admin btn-info btn-sm">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php else: ?>
                        
                        <div class="alert alert-danger">
                            ❌ <strong>المنتجات غير موجودة أو فارغة</strong>
                        </div>
                        
                        <div class="text-center py-5">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <h5>لا توجد منتجات</h5>
                            <p class="text-muted">
                                <?php if ($search || $categoryFilter || $statusFilter): ?>
                                    لم نجد أي منتجات تطابق معايير البحث المحددة
                                <?php else: ?>
                                    لم يتم إضافة أي منتجات بعد
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <h6>روابط مفيدة:</h6>
                            <a href="products.php" class="btn btn-primary">الصفحة الأصلية</a>
                            <a href="test_display.php" class="btn btn-info">اختبار العرض</a>
                            <a href="products_simple.php" class="btn btn-success">الصفحة المبسطة</a>
                            <a href="fix_products.php" class="btn btn-warning">إصلاح المشاكل</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
