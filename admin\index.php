<?php
/**
 * لوحة التحكم الرئيسية
 * Admin Dashboard
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// الحصول على الإحصائيات
$stats = [
    'total_products' => $database->count('products'),
    'active_products' => $database->count('products', 'status = "active"'),
    'total_categories' => $database->count('categories'),
    'total_orders' => $database->count('orders'),
    'pending_orders' => $database->count('orders', 'status = "pending"'),
    'total_users' => $database->count('users'),
    'total_revenue' => 0
];

// حساب إجمالي المبيعات
$revenueResult = $database->fetch("SELECT SUM(total_amount) as total FROM orders WHERE status IN ('confirmed', 'delivered')");
$stats['total_revenue'] = $revenueResult['total'] ?? 0;

// الحصول على آخر الطلبات
$recentOrders = $database->fetchAll("
    SELECT * FROM orders 
    ORDER BY created_at DESC 
    LIMIT 10
");

// الحصول على المنتجات منخفضة المخزون
$lowStockProducts = $database->fetchAll("
    SELECT * FROM products 
    WHERE stock_quantity <= 5 AND status = 'active'
    ORDER BY stock_quantity ASC
    LIMIT 10
");

// الحصول على آخر الأنشطة
$recentActivities = $database->fetchAll("
    SELECT al.*, u.full_name 
    FROM activity_logs al 
    LEFT JOIN users u ON al.user_id = u.id 
    ORDER BY al.created_at DESC 
    LIMIT 15
");

// تسجيل النشاط
logActivity('admin_dashboard_view', 'عرض لوحة التحكم الرئيسية');

$pageTitle = "لوحة التحكم - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php" class="active"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">لوحة التحكم</h1>
                <div class="header-actions">
                    <div class="user-menu">
                        <div class="user-info">
                            <div class="user-avatar">
                                <?php echo substr($_SESSION['user_name'], 0, 1); ?>
                            </div>
                            <div class="user-details">
                                <div class="user-name"><?php echo $_SESSION['user_name']; ?></div>
                                <small class="user-role">مدير النظام</small>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <div class="admin-main">
                <!-- Welcome Message -->
                <div class="welcome-message mb-4">
                    <h2>مرحباً، <?php echo $_SESSION['user_name']; ?>!</h2>
                    <p class="text-muted">إليك نظرة عامة على أداء متجرك اليوم</p>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon primary">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['total_products']); ?></h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['active_products']); ?></h3>
                            <p>المنتجات النشطة</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['total_orders']); ?></h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['pending_orders']); ?></h3>
                            <p>طلبات قيد الانتظار</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['total_revenue'], 2); ?> ريال</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo number_format($stats['total_users']); ?></h3>
                            <p>إجمالي المستخدمين</p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Recent Orders -->
                    <div class="col-lg-8">
                        <div class="admin-card">
                            <div class="card-header">
                                <h5 class="card-title">آخر الطلبات</h5>
                                <a href="orders.php" class="btn-admin btn-outline btn-sm">عرض الكل</a>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($recentOrders)): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOrders as $order): ?>
                                            <tr>
                                                <td><strong><?php echo $order['order_number']; ?></strong></td>
                                                <td><?php echo $order['customer_name']; ?></td>
                                                <td><?php echo formatPrice($order['total_amount']); ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    switch($order['status']) {
                                                        case 'pending':
                                                            $statusClass = 'warning';
                                                            $statusText = 'قيد الانتظار';
                                                            break;
                                                        case 'confirmed':
                                                            $statusClass = 'info';
                                                            $statusText = 'مؤكد';
                                                            break;
                                                        case 'delivered':
                                                            $statusClass = 'success';
                                                            $statusText = 'تم التوصيل';
                                                            break;
                                                        case 'cancelled':
                                                            $statusClass = 'danger';
                                                            $statusText = 'ملغي';
                                                            break;
                                                        default:
                                                            $statusClass = 'secondary';
                                                            $statusText = $order['status'];
                                                    }
                                                    ?>
                                                    <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                </td>
                                                <td><?php echo formatDate($order['created_at'], 'd/m/Y'); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد طلبات حتى الآن</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Low Stock Products -->
                    <div class="col-lg-4">
                        <div class="admin-card">
                            <div class="card-header">
                                <h5 class="card-title">منتجات منخفضة المخزون</h5>
                            </div>
                            <div class="card-body">
                                <?php if (!empty($lowStockProducts)): ?>
                                <div class="low-stock-list">
                                    <?php foreach ($lowStockProducts as $product): ?>
                                    <div class="low-stock-item d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <h6 class="mb-1"><?php echo $product['name']; ?></h6>
                                            <small class="text-muted">المخزون: <?php echo $product['stock_quantity']; ?></small>
                                        </div>
                                        <span class="badge badge-warning"><?php echo $product['stock_quantity']; ?></span>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                    <p class="text-muted">جميع المنتجات متوفرة</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">آخر الأنشطة</h5>
                        <a href="activity-logs.php" class="btn-admin btn-outline btn-sm">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($recentActivities)): ?>
                        <div class="activity-list">
                            <?php foreach ($recentActivities as $activity): ?>
                            <div class="activity-item d-flex align-items-start mb-3">
                                <div class="activity-icon me-3">
                                    <i class="fas fa-circle text-primary" style="font-size: 0.5rem;"></i>
                                </div>
                                <div class="activity-content flex-grow-1">
                                    <div class="activity-description">
                                        <?php if ($activity['full_name']): ?>
                                        <strong><?php echo $activity['full_name']; ?></strong>
                                        <?php endif; ?>
                                        <?php echo $activity['description'] ?: $activity['action']; ?>
                                    </div>
                                    <small class="text-muted"><?php echo formatDate($activity['created_at'], 'd/m/Y H:i'); ?></small>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد أنشطة حديثة</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(function() {
        // يمكن إضافة كود AJAX لتحديث الإحصائيات
        console.log('تحديث الإحصائيات...');
    }, 30000);
    
    // إضافة تأثيرات بصرية للبطاقات
    document.querySelectorAll('.stat-card').forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    </script>
</body>
</html>
