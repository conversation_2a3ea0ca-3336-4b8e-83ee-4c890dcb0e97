<?php
/**
 * تسجيل خروج العملاء
 * Customer Logout
 */

require_once 'includes/functions.php';

// تسجيل النشاط قبل تسجيل الخروج
if (isLoggedIn()) {
    $userName = $_SESSION['user_name'] ?? 'مستخدم غير معروف';
    $userRole = $_SESSION['user_role'] ?? 'customer';
    logActivity('customer_logout', "تسجيل خروج العميل: {$userName}", $_SESSION['user_id'], $userRole);
}

// تسجيل الخروج
logout();

// إعادة التوجيه إلى الصفحة الرئيسية مع رسالة
header('Location: index.php?message=' . urlencode('تم تسجيل الخروج بنجاح'));
exit;
?>
