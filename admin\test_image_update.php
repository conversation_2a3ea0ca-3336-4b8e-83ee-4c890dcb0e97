<?php
/**
 * اختبار سريع لتحديث صورة المنتج
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

$productId = isset($_GET['id']) ? intval($_GET['id']) : 3;

echo "<h2>اختبار سريع لتحديث صورة المنتج</h2>";

// الحصول على المنتج
$product = $database->fetch("SELECT * FROM products WHERE id = :id", ['id' => $productId]);

if (!$product) {
    echo "<p style='color: red;'>❌ المنتج غير موجود</p>";
    exit;
}

echo "<h3>معلومات المنتج:</h3>";
echo "<ul>";
echo "<li><strong>ID:</strong> " . $product['id'] . "</li>";
echo "<li><strong>الاسم:</strong> " . htmlspecialchars($product['name']) . "</li>";
echo "<li><strong>الصورة الحالية:</strong> " . ($product['image'] ? $product['image'] : 'لا توجد') . "</li>";
echo "</ul>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['image'])) {
    echo "<h3>نتيجة التحديث:</h3>";
    
    $file = $_FILES['image'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        // رفع الملف
        $uploadResult = uploadFile($file, 'products');
        
        if ($uploadResult['success']) {
            echo "<p style='color: green;'>✅ تم رفع الملف: " . $uploadResult['filename'] . "</p>";
            
            // تحديث قاعدة البيانات
            $updateData = [
                'image' => $uploadResult['filename'],
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            echo "<p><strong>البيانات المراد تحديثها:</strong></p>";
            echo "<pre>" . print_r($updateData, true) . "</pre>";
            
            $updateResult = $database->update('products', $updateData, 'id = :id', ['id' => $productId]);
            
            echo "<p><strong>نتيجة التحديث:</strong> " . ($updateResult ? 'نجح ✅' : 'فشل ❌') . "</p>";
            
            if ($updateResult) {
                // التحقق من التحديث
                $updatedProduct = $database->fetch("SELECT * FROM products WHERE id = :id", ['id' => $productId]);
                echo "<p><strong>الصورة الجديدة في قاعدة البيانات:</strong> " . $updatedProduct['image'] . "</p>";
                
                // عرض الصورة
                $imageUrl = UPLOADS_URL . '/products/' . $updatedProduct['image'];
                echo "<p><strong>رابط الصورة:</strong> <a href='$imageUrl' target='_blank'>$imageUrl</a></p>";
                echo "<img src='$imageUrl' alt='الصورة الجديدة' style='max-width: 300px; height: auto; border: 1px solid #ddd; padding: 10px; border-radius: 5px;'>";
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4>✅ تم تحديث الصورة بنجاح!</h4>";
                echo "<p>الآن يمكنك العودة لصفحة تعديل المنتج وستجد الصورة محدثة.</p>";
                echo "</div>";
                
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث قاعدة البيانات</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ فشل في رفع الملف: " . $uploadResult['message'] . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ خطأ في رفع الملف</p>";
    }
}

?>

<h3>رفع صورة جديدة:</h3>

<form method="POST" enctype="multipart/form-data" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <div style="margin-bottom: 15px;">
        <label for="image"><strong>اختر صورة جديدة:</strong></label><br>
        <input type="file" id="image" name="image" accept="image/*" required>
    </div>
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        تحديث الصورة
    </button>
</form>

<h3>روابط مفيدة:</h3>
<a href="edit-product.php?id=<?php echo $productId; ?>" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تعديل المنتج</a>
<a href="products.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">قائمة المنتجات</a>
<a href="debug_image_save.php?id=<?php echo $productId; ?>" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تشخيص مفصل</a>
