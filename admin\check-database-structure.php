<?php
/**
 * فحص بنية قاعدة البيانات للتأكد من وجود الجداول المطلوبة
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

echo "<h2>فحص بنية قاعدة البيانات</h2>";

// قائمة الجداول المطلوبة
$requiredTables = [
    'products' => 'جدول المنتجات',
    'categories' => 'جدول الأقسام',
    'orders' => 'جدول الطلبات',
    'order_items' => 'جدول عناصر الطلبات',
    'users' => 'جدول المستخدمين',
    'stock_movements' => 'جدول حركات المخزون',
    'activity_logs' => 'جدول سجل الأنشطة'
];

echo "<h3>1. فحص وجود الجداول:</h3>";

$missingTables = [];
foreach ($requiredTables as $table => $description) {
    try {
        $result = $database->fetch("SELECT COUNT(*) as count FROM $table");
        echo "<p style='color: green;'>✅ $description ($table) موجود - عدد السجلات: " . $result['count'] . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ $description ($table) غير موجود أو به مشكلة</p>";
        $missingTables[] = $table;
    }
}

// فحص بنية جدول order_items
echo "<h3>2. فحص بنية جدول order_items:</h3>";

if (!in_array('order_items', $missingTables)) {
    try {
        $columns = $database->fetchAll("SHOW COLUMNS FROM order_items");
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th>";
        echo "</tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // التحقق من الأعمدة المطلوبة
        $requiredColumns = ['id', 'order_id', 'product_id', 'quantity', 'price'];
        $existingColumns = array_column($columns, 'Field');
        
        echo "<h4>الأعمدة المطلوبة:</h4>";
        foreach ($requiredColumns as $col) {
            if (in_array($col, $existingColumns)) {
                echo "<p style='color: green;'>✅ العمود $col موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ العمود $col غير موجود</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص بنية جدول order_items: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ جدول order_items غير موجود</p>";
}

// فحص بيانات تجريبية
echo "<h3>3. فحص البيانات التجريبية:</h3>";

if (!in_array('order_items', $missingTables)) {
    try {
        // فحص وجود طلبات
        $ordersCount = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
        echo "<p>عدد الطلبات: $ordersCount</p>";
        
        // فحص وجود عناصر طلبات
        $orderItemsCount = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
        echo "<p>عدد عناصر الطلبات: $orderItemsCount</p>";
        
        if ($orderItemsCount == 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>⚠️ لا توجد بيانات تجريبية</h4>";
            echo "<p>تحتاج لإضافة بيانات تجريبية لاختبار تقرير المبيعات</p>";
            echo "<a href='create-sample-data.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء بيانات تجريبية</a>";
            echo "</div>";
        } else {
            // عرض عينة من البيانات
            $sampleData = $database->fetchAll("
                SELECT oi.*, o.order_number, o.created_at, p.name as product_name 
                FROM order_items oi 
                JOIN orders o ON oi.order_id = o.id 
                JOIN products p ON oi.product_id = p.id 
                LIMIT 5
            ");
            
            echo "<h4>عينة من البيانات:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>رقم الطلب</th><th>المنتج</th><th>الكمية</th><th>السعر</th><th>التاريخ</th>";
            echo "</tr>";
            
            foreach ($sampleData as $item) {
                echo "<tr>";
                echo "<td>" . $item['order_number'] . "</td>";
                echo "<td>" . htmlspecialchars($item['product_name']) . "</td>";
                echo "<td>" . $item['quantity'] . "</td>";
                echo "<td>" . formatPrice($item['price']) . "</td>";
                echo "<td>" . formatDate($item['created_at'], 'd/m/Y') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص البيانات: " . $e->getMessage() . "</p>";
    }
}

// إنشاء الجداول المفقودة
if (!empty($missingTables)) {
    echo "<h3>4. إنشاء الجداول المفقودة:</h3>";
    
    if (in_array('order_items', $missingTables)) {
        try {
            $database->query("
                CREATE TABLE IF NOT EXISTS order_items (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    product_id INT NOT NULL,
                    quantity INT NOT NULL DEFAULT 1,
                    price DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
                )
            ");
            echo "<p style='color: green;'>✅ تم إنشاء جدول order_items</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول order_items: " . $e->getMessage() . "</p>";
        }
    }
}

echo "<h3>5. النتيجة النهائية:</h3>";

if (empty($missingTables)) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎉 قاعدة البيانات جاهزة!</h4>";
    echo "<p>جميع الجداول المطلوبة موجودة ويمكن استخدام تقرير المبيعات الشهرية</p>";
    echo "<a href='monthly-sales-report.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تقرير المبيعات الشهرية</a>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>❌ قاعدة البيانات غير مكتملة</h4>";
    echo "<p>الجداول المفقودة: " . implode(', ', $missingTables) . "</p>";
    echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث قاعدة البيانات</a>";
    echo "</div>";
}

echo "<h3>روابط مفيدة:</h3>";
echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تحديث قاعدة البيانات</a>";
echo "<a href='create-sample-data.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إنشاء بيانات تجريبية</a>";
echo "<a href='monthly-sales-report.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تقرير المبيعات</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
?>
