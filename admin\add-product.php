<?php
/**
 * إضافة منتج جديد
 * Add New Product
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة إضافة المنتج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = cleanInput($_POST['name'] ?? '');
    $description = cleanInput($_POST['description'] ?? '');
    $shortDescription = cleanInput($_POST['short_description'] ?? '');
    $categoryId = intval($_POST['category_id'] ?? 0);
    $price = floatval($_POST['price'] ?? 0);
    $salePrice = !empty($_POST['sale_price']) ? floatval($_POST['sale_price']) : null;
    $sku = cleanInput($_POST['sku'] ?? '');
    $stockQuantity = intval($_POST['stock_quantity'] ?? 0);
    $weight = cleanInput($_POST['weight'] ?? '');
    $size = cleanInput($_POST['size'] ?? '');
    $status = cleanInput($_POST['status'] ?? 'active');
    $featured = isset($_POST['featured']) ? 1 : 0;
    $seoTitle = cleanInput($_POST['seo_title'] ?? '');
    $seoDescription = cleanInput($_POST['seo_description'] ?? '');
    $seoKeywords = cleanInput($_POST['seo_keywords'] ?? '');
    
    $errors = [];
    
    // التحقق من البيانات المطلوبة
    if (empty($name)) $errors[] = 'اسم المنتج مطلوب';
    if ($categoryId <= 0) $errors[] = 'يجب اختيار قسم للمنتج';
    if ($price <= 0) $errors[] = 'سعر المنتج يجب أن يكون أكبر من صفر';
    if ($stockQuantity < 0) $errors[] = 'كمية المخزون لا يمكن أن تكون سالبة';
    
    // التحقق من تكرار رقم المنتج
    if ($sku && $database->fetch("SELECT id FROM products WHERE sku = :sku", ['sku' => $sku])) {
        $errors[] = 'رقم المنتج موجود بالفعل';
    }
    
    // معالجة رفع الصورة الرئيسية
    $imageName = null;
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $uploadResult = uploadFile($_FILES['image'], 'products');
        if ($uploadResult['success']) {
            $imageName = $uploadResult['filename'];
        } else {
            $errors[] = $uploadResult['message'];
        }
    }
    
    // معالجة رفع معرض الصور
    $galleryImages = [];
    if (isset($_FILES['gallery']) && is_array($_FILES['gallery']['name'])) {
        for ($i = 0; $i < count($_FILES['gallery']['name']); $i++) {
            if ($_FILES['gallery']['error'][$i] === UPLOAD_ERR_OK) {
                $file = [
                    'name' => $_FILES['gallery']['name'][$i],
                    'type' => $_FILES['gallery']['type'][$i],
                    'tmp_name' => $_FILES['gallery']['tmp_name'][$i],
                    'error' => $_FILES['gallery']['error'][$i],
                    'size' => $_FILES['gallery']['size'][$i]
                ];
                $uploadResult = uploadFile($file, 'products');
                if ($uploadResult['success']) {
                    $galleryImages[] = $uploadResult['filename'];
                }
            }
        }
    }
    
    if (empty($errors)) {
        $data = [
            'name' => $name,
            'description' => $description,
            'short_description' => $shortDescription,
            'category_id' => $categoryId,
            'price' => $price,
            'sale_price' => $salePrice,
            'sku' => $sku,
            'stock_quantity' => $stockQuantity,
            'weight' => $weight,
            'size' => $size,
            'status' => $status,
            'featured' => $featured,
            'image' => $imageName,
            'gallery' => !empty($galleryImages) ? json_encode($galleryImages) : null,
            'seo_title' => $seoTitle,
            'seo_description' => $seoDescription,
            'seo_keywords' => $seoKeywords
        ];
        
        if ($database->insert('products', $data)) {
            $message = 'تم إضافة المنتج بنجاح';
            $messageType = 'success';
            logActivity('product_added', "تم إضافة منتج جديد: {$name}");
            
            // إعادة توجيه لصفحة المنتجات
            header('Location: products.php?message=' . urlencode($message));
            exit;
        } else {
            $message = 'حدث خطأ أثناء إضافة المنتج';
            $messageType = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// الحصول على الأقسام
$categories = $categoryManager->getAllCategories();

$pageTitle = "إضافة منتج جديد - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php" class="active"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إضافة منتج جديد</h1>
                <div class="header-actions">
                    <a href="products.php" class="btn-admin btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للمنتجات
                    </a>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <form method="POST" action="add-product.php" enctype="multipart/form-data">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-lg-8">
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">معلومات المنتج الأساسية</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">اسم المنتج *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="short_description" class="form-label">الوصف المختصر</label>
                                        <textarea class="form-control" id="short_description" name="short_description" rows="3"><?php echo isset($_POST['short_description']) ? htmlspecialchars($_POST['short_description']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">الوصف التفصيلي</label>
                                        <textarea class="form-control" id="description" name="description" rows="6"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="category_id" class="form-label">القسم *</label>
                                            <select class="form-select" id="category_id" name="category_id" required>
                                                <option value="">اختر القسم</option>
                                                <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>" 
                                                        <?php echo (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $category['name']; ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="sku" class="form-label">رقم المنتج (SKU)</label>
                                            <input type="text" class="form-control" id="sku" name="sku" 
                                                   value="<?php echo isset($_POST['sku']) ? htmlspecialchars($_POST['sku']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">التسعير والمخزون</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="price" class="form-label">السعر الأساسي *</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="price" name="price" 
                                                       step="0.01" min="0" 
                                                       value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>" required>
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="sale_price" class="form-label">سعر الخصم</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="sale_price" name="sale_price" 
                                                       step="0.01" min="0" 
                                                       value="<?php echo isset($_POST['sale_price']) ? $_POST['sale_price'] : ''; ?>">
                                                <span class="input-group-text">ريال</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="stock_quantity" class="form-label">كمية المخزون *</label>
                                            <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                                                   min="0" 
                                                   value="<?php echo isset($_POST['stock_quantity']) ? $_POST['stock_quantity'] : '0'; ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="weight" class="form-label">الوزن</label>
                                            <input type="text" class="form-control" id="weight" name="weight" 
                                                   placeholder="مثال: 500 جرام" 
                                                   value="<?php echo isset($_POST['weight']) ? htmlspecialchars($_POST['weight']) : ''; ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="size" class="form-label">الحجم</label>
                                            <input type="text" class="form-control" id="size" name="size" 
                                                   placeholder="مثال: 250 مل" 
                                                   value="<?php echo isset($_POST['size']) ? htmlspecialchars($_POST['size']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Images -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">الصور</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="image" class="form-label">الصورة الرئيسية</label>
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <div class="form-text">الحد الأقصى: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="gallery" class="form-label">معرض الصور</label>
                                        <input type="file" class="form-control" id="gallery" name="gallery[]" multiple accept="image/*">
                                        <div class="form-text">يمكنك اختيار عدة صور للمعرض</div>
                                    </div>
                                </div>
                            </div>

                            <!-- SEO Settings -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">إعدادات SEO</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="seo_title" class="form-label">عنوان SEO</label>
                                        <input type="text" class="form-control" id="seo_title" name="seo_title" 
                                               value="<?php echo isset($_POST['seo_title']) ? htmlspecialchars($_POST['seo_title']) : ''; ?>">
                                        <div class="form-text">إذا تُرك فارغاً، سيتم استخدام اسم المنتج</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="seo_description" class="form-label">وصف SEO</label>
                                        <textarea class="form-control" id="seo_description" name="seo_description" rows="3"><?php echo isset($_POST['seo_description']) ? htmlspecialchars($_POST['seo_description']) : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="seo_keywords" class="form-label">الكلمات المفتاحية</label>
                                        <input type="text" class="form-control" id="seo_keywords" name="seo_keywords" 
                                               placeholder="كلمة1, كلمة2, كلمة3" 
                                               value="<?php echo isset($_POST['seo_keywords']) ? htmlspecialchars($_POST['seo_keywords']) : ''; ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Sidebar -->
                        <div class="col-lg-4">
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">إعدادات المنتج</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">حالة المنتج</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] === 'active') ? 'selected' : 'selected'; ?>>نشط</option>
                                            <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                            <option value="out_of_stock" <?php echo (isset($_POST['status']) && $_POST['status'] === 'out_of_stock') ? 'selected' : ''; ?>>نفد المخزون</option>
                                        </select>
                                    </div>
                                    
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="featured" name="featured" 
                                               <?php echo (isset($_POST['featured'])) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="featured">
                                            منتج مميز
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="admin-card">
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn-admin btn-primary btn-lg">
                                            <i class="fas fa-save"></i> حفظ المنتج
                                        </button>
                                        <a href="products.php" class="btn-admin btn-secondary">
                                            <i class="fas fa-times"></i> إلغاء
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // تحديث عنوان SEO تلقائياً من اسم المنتج
    document.getElementById('name').addEventListener('input', function() {
        const seoTitle = document.getElementById('seo_title');
        if (!seoTitle.value) {
            seoTitle.value = this.value;
        }
    });
    
    // التحقق من سعر الخصم
    document.getElementById('sale_price').addEventListener('input', function() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const salePrice = parseFloat(this.value) || 0;
        
        if (salePrice > 0 && salePrice >= price) {
            alert('سعر الخصم يجب أن يكون أقل من السعر الأساسي');
            this.value = '';
        }
    });
    
    // معاينة الصورة
    document.getElementById('image').addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // يمكن إضافة معاينة الصورة هنا
                console.log('تم اختيار صورة:', file.name);
            };
            reader.readAsDataURL(file);
        }
    });
    </script>
</body>
</html>
