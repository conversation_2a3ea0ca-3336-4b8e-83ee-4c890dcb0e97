<?php
/**
 * الدوال المساعدة
 * Helper Functions
 */

require_once __DIR__ . '/../config/config.php';

/**
 * دوال إدارة الأقسام
 */
class CategoryManager {
    private $db;
    
    public function __construct() {
        global $database;
        $this->db = $database;
    }
    
    public function getAllCategories($status = 'active') {
        $sql = "SELECT * FROM categories WHERE status = :status ORDER BY sort_order, name";
        return $this->db->fetchAll($sql, ['status' => $status]);
    }
    
    public function getCategoryById($id) {
        $sql = "SELECT * FROM categories WHERE id = :id";
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    public function getMainCategories() {
        $sql = "SELECT * FROM categories WHERE parent_id IS NULL AND status = 'active' ORDER BY sort_order, name";
        return $this->db->fetchAll($sql);
    }
    
    public function getSubCategories($parent_id) {
        $sql = "SELECT * FROM categories WHERE parent_id = :parent_id AND status = 'active' ORDER BY sort_order, name";
        return $this->db->fetchAll($sql, ['parent_id' => $parent_id]);
    }
}

/**
 * دوال إدارة المنتجات
 */
class ProductManager {
    private $db;
    
    public function __construct() {
        global $database;
        $this->db = $database;
    }
    
    public function getAllProducts($status = 'active', $limit = null, $offset = 0) {
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.status = :status 
                ORDER BY p.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            return $this->db->fetchAll($sql, [
                'status' => $status,
                'limit' => $limit,
                'offset' => $offset
            ]);
        }
        
        return $this->db->fetchAll($sql, ['status' => $status]);
    }
    
    public function getProductById($id) {
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.id = :id";
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    public function getProductsByCategory($category_id, $limit = null, $offset = 0) {
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.category_id = :category_id AND p.status = 'active' 
                ORDER BY p.created_at DESC";
        
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            return $this->db->fetchAll($sql, [
                'category_id' => $category_id,
                'limit' => $limit,
                'offset' => $offset
            ]);
        }
        
        return $this->db->fetchAll($sql, ['category_id' => $category_id]);
    }
    
    public function getFeaturedProducts($limit = 8) {
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE p.featured = 1 AND p.status = 'active' 
                ORDER BY p.created_at DESC 
                LIMIT :limit";
        return $this->db->fetchAll($sql, ['limit' => $limit]);
    }
    
    public function searchProducts($query, $limit = null, $offset = 0) {
        $sql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                WHERE (p.name LIKE :query OR p.description LIKE :query OR p.short_description LIKE :query) 
                AND p.status = 'active' 
                ORDER BY p.created_at DESC";
        
        $searchQuery = '%' . $query . '%';
        
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
            return $this->db->fetchAll($sql, [
                'query' => $searchQuery,
                'limit' => $limit,
                'offset' => $offset
            ]);
        }
        
        return $this->db->fetchAll($sql, ['query' => $searchQuery]);
    }
    
    public function getProductsCount($category_id = null) {
        if ($category_id) {
            return $this->db->count('products', 'category_id = :category_id AND status = "active"', ['category_id' => $category_id]);
        }
        return $this->db->count('products', 'status = "active"');
    }
}

/**
 * دوال إدارة الطلبات
 */
class OrderManager {
    private $db;
    
    public function __construct() {
        global $database;
        $this->db = $database;
    }
    
    public function createOrder($orderData, $orderItems) {
        try {
            $this->db->conn->beginTransaction();
            
            // إنشاء الطلب
            $orderData['order_number'] = generateOrderNumber();
            $orderId = $this->db->insert('orders', $orderData);
            
            if (!$orderId) {
                throw new Exception('فشل في إنشاء الطلب');
            }
            
            // إضافة عناصر الطلب
            foreach ($orderItems as $item) {
                $item['order_id'] = $orderId;
                $item['total'] = $item['product_price'] * $item['quantity'];
                
                if (!$this->db->insert('order_details', $item)) {
                    throw new Exception('فشل في إضافة عناصر الطلب');
                }
                
                // تحديث المخزون
                $this->updateProductStock($item['product_id'], $item['quantity']);
            }
            
            $this->db->conn->commit();
            
            // تسجيل النشاط
            logActivity('order_created', 'تم إنشاء طلب جديد رقم: ' . $orderData['order_number']);
            
            return $orderId;
            
        } catch (Exception $e) {
            $this->db->conn->rollback();
            error_log('Order creation error: ' . $e->getMessage());
            return false;
        }
    }
    
    public function getOrderById($id) {
        $sql = "SELECT * FROM orders WHERE id = :id";
        return $this->db->fetch($sql, ['id' => $id]);
    }
    
    public function getOrderDetails($orderId) {
        $sql = "SELECT od.*, p.image 
                FROM order_details od 
                LEFT JOIN products p ON od.product_id = p.id 
                WHERE od.order_id = :order_id";
        return $this->db->fetchAll($sql, ['order_id' => $orderId]);
    }
    
    public function updateOrderStatus($orderId, $status) {
        $data = ['status' => $status, 'updated_at' => date('Y-m-d H:i:s')];
        $result = $this->db->update('orders', $data, 'id = :id', ['id' => $orderId]);
        
        if ($result) {
            logActivity('order_status_updated', "تم تحديث حالة الطلب رقم {$orderId} إلى {$status}");
        }
        
        return $result;
    }
    
    private function updateProductStock($productId, $quantity) {
        $sql = "UPDATE products SET stock_quantity = stock_quantity - :quantity WHERE id = :id";
        return $this->db->query($sql, ['quantity' => $quantity, 'id' => $productId]);
    }
}

/**
 * دوال إدارة سلة التسوق
 */
class CartManager {
    
    public function addToCart($productId, $quantity = 1) {
        if (!isset($_SESSION['cart'])) {
            $_SESSION['cart'] = [];
        }
        
        if (isset($_SESSION['cart'][$productId])) {
            $_SESSION['cart'][$productId] += $quantity;
        } else {
            $_SESSION['cart'][$productId] = $quantity;
        }
        
        logActivity('add_to_cart', "تم إضافة منتج رقم {$productId} إلى السلة");
    }
    
    public function removeFromCart($productId) {
        if (isset($_SESSION['cart'][$productId])) {
            unset($_SESSION['cart'][$productId]);
            logActivity('remove_from_cart', "تم حذف منتج رقم {$productId} من السلة");
        }
    }
    
    public function updateCartQuantity($productId, $quantity) {
        if ($quantity <= 0) {
            $this->removeFromCart($productId);
        } else {
            $_SESSION['cart'][$productId] = $quantity;
        }
    }
    
    public function getCartItems() {
        if (!isset($_SESSION['cart']) || empty($_SESSION['cart'])) {
            return [];
        }
        
        $productManager = new ProductManager();
        $cartItems = [];
        
        foreach ($_SESSION['cart'] as $productId => $quantity) {
            $product = $productManager->getProductById($productId);
            if ($product) {
                $product['cart_quantity'] = $quantity;
                $product['cart_total'] = ($product['sale_price'] ?: $product['price']) * $quantity;
                $cartItems[] = $product;
            }
        }
        
        return $cartItems;
    }
    
    public function getCartTotal() {
        $items = $this->getCartItems();
        $total = 0;
        
        foreach ($items as $item) {
            $total += $item['cart_total'];
        }
        
        return $total;
    }
    
    public function getCartCount() {
        if (!isset($_SESSION['cart'])) {
            return 0;
        }
        
        return array_sum($_SESSION['cart']);
    }
    
    public function clearCart() {
        unset($_SESSION['cart']);
    }
}

/**
 * دالة لرفع الملفات
 */
function uploadFile($file, $directory = 'products', $allowedTypes = null) {
    if (!$allowedTypes) {
        $allowedTypes = ALLOWED_IMAGE_TYPES;
    }
    
    $uploadDir = UPLOADS_PATH . '/' . $directory . '/';
    
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileError = $file['error'];
    
    if ($fileError !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($fileSize > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    if (!in_array($fileExt, $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مسموح'];
    }
    
    $newFileName = uniqid() . '.' . $fileExt;
    $destination = $uploadDir . $newFileName;
    
    if (move_uploaded_file($fileTmp, $destination)) {
        return ['success' => true, 'filename' => $newFileName, 'path' => $directory . '/' . $newFileName];
    }
    
    return ['success' => false, 'message' => 'فشل في رفع الملف'];
}

// إنشاء كائنات عامة
$categoryManager = new CategoryManager();
$productManager = new ProductManager();
$orderManager = new OrderManager();
$cartManager = new CartManager();
?>
