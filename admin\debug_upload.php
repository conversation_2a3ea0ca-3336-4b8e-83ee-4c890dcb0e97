<?php
/**
 * تشخيص مشاكل رفع الصور
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

echo "<h2>تشخيص مشاكل رفع الصور</h2>";

// التحقق من الثوابت
echo "<h3>1. فحص الثوابت:</h3>";
echo "<ul>";
echo "<li><strong>UPLOADS_PATH:</strong> " . (defined('UPLOADS_PATH') ? UPLOADS_PATH : 'غير معرف') . "</li>";
echo "<li><strong>UPLOADS_URL:</strong> " . (defined('UPLOADS_URL') ? UPLOADS_URL : 'غير معرف') . "</li>";
echo "<li><strong>SITE_URL:</strong> " . (defined('SITE_URL') ? SITE_URL : 'غير معرف') . "</li>";
echo "</ul>";

// التحقق من وجود مجلد uploads
echo "<h3>2. فحص مجلد uploads:</h3>";

$uploadsPath = defined('UPLOADS_PATH') ? UPLOADS_PATH : '../uploads';
$uploadsDir = realpath($uploadsPath);

echo "<ul>";
echo "<li><strong>مسار uploads:</strong> " . $uploadsPath . "</li>";
echo "<li><strong>المسار الحقيقي:</strong> " . ($uploadsDir ? $uploadsDir : 'غير موجود') . "</li>";
echo "<li><strong>المجلد موجود:</strong> " . (is_dir($uploadsPath) ? 'نعم ✅' : 'لا ❌') . "</li>";

if (is_dir($uploadsPath)) {
    echo "<li><strong>قابل للكتابة:</strong> " . (is_writable($uploadsPath) ? 'نعم ✅' : 'لا ❌') . "</li>";
    echo "<li><strong>الصلاحيات:</strong> " . substr(sprintf('%o', fileperms($uploadsPath)), -4) . "</li>";
    
    // فحص المجلدات الفرعية
    $subDirs = ['products', 'categories', 'users'];
    foreach ($subDirs as $subDir) {
        $subPath = $uploadsPath . '/' . $subDir;
        echo "<li><strong>مجلد $subDir:</strong> " . (is_dir($subPath) ? 'موجود ✅' : 'غير موجود ❌') . "</li>";
        if (is_dir($subPath)) {
            echo "<li><strong>$subDir قابل للكتابة:</strong> " . (is_writable($subPath) ? 'نعم ✅' : 'لا ❌') . "</li>";
        }
    }
} else {
    echo "<li style='color: red;'>❌ مجلد uploads غير موجود!</li>";
}
echo "</ul>";

// فحص إعدادات PHP
echo "<h3>3. فحص إعدادات PHP:</h3>";
echo "<ul>";
echo "<li><strong>file_uploads:</strong> " . (ini_get('file_uploads') ? 'مفعل ✅' : 'معطل ❌') . "</li>";
echo "<li><strong>upload_max_filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>post_max_size:</strong> " . ini_get('post_max_size') . "</li>";
echo "<li><strong>max_file_uploads:</strong> " . ini_get('max_file_uploads') . "</li>";
echo "<li><strong>memory_limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "</ul>";

// فحص دالة uploadFile
echo "<h3>4. فحص دالة uploadFile:</h3>";
if (function_exists('uploadFile')) {
    echo "✅ دالة uploadFile موجودة<br>";
} else {
    echo "❌ دالة uploadFile غير موجودة<br>";
}

// اختبار رفع ملف
echo "<h3>5. اختبار رفع ملف:</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])) {
    echo "<h4>نتيجة اختبار الرفع:</h4>";
    
    $file = $_FILES['test_image'];
    echo "<ul>";
    echo "<li><strong>اسم الملف:</strong> " . $file['name'] . "</li>";
    echo "<li><strong>نوع الملف:</strong> " . $file['type'] . "</li>";
    echo "<li><strong>حجم الملف:</strong> " . number_format($file['size']) . " بايت</li>";
    echo "<li><strong>رمز الخطأ:</strong> " . $file['error'] . "</li>";
    echo "<li><strong>الملف المؤقت:</strong> " . $file['tmp_name'] . "</li>";
    echo "</ul>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "<p style='color: green;'>✅ تم رفع الملف بنجاح إلى المجلد المؤقت</p>";
        
        // اختبار دالة uploadFile
        if (function_exists('uploadFile')) {
            try {
                $result = uploadFile($file, 'products');
                if ($result['success']) {
                    echo "<p style='color: green;'>✅ تم حفظ الملف بنجاح: " . $result['filename'] . "</p>";
                    echo "<p><strong>مسار الملف:</strong> " . $uploadsPath . '/products/' . $result['filename'] . "</p>";
                    
                    // عرض الصورة
                    $imageUrl = UPLOADS_URL . '/products/' . $result['filename'];
                    echo "<p><strong>رابط الصورة:</strong> <a href='$imageUrl' target='_blank'>$imageUrl</a></p>";
                    echo "<img src='$imageUrl' alt='اختبار' style='max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;'>";
                } else {
                    echo "<p style='color: red;'>❌ فشل في حفظ الملف: " . $result['message'] . "</p>";
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ خطأ في دالة uploadFile: " . $e->getMessage() . "</p>";
            }
        } else {
            // اختبار رفع يدوي
            $targetDir = $uploadsPath . '/products/';
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            
            $fileName = time() . '_' . basename($file['name']);
            $targetFile = $targetDir . $fileName;
            
            if (move_uploaded_file($file['tmp_name'], $targetFile)) {
                echo "<p style='color: green;'>✅ تم حفظ الملف يدوياً: $fileName</p>";
                
                $imageUrl = UPLOADS_URL . '/products/' . $fileName;
                echo "<p><strong>رابط الصورة:</strong> <a href='$imageUrl' target='_blank'>$imageUrl</a></p>";
                echo "<img src='$imageUrl' alt='اختبار' style='max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;'>";
            } else {
                echo "<p style='color: red;'>❌ فشل في نقل الملف</p>";
            }
        }
    } else {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'الملف أكبر من الحد المسموح في PHP',
            UPLOAD_ERR_FORM_SIZE => 'الملف أكبر من الحد المسموح في النموذج',
            UPLOAD_ERR_PARTIAL => 'تم رفع جزء من الملف فقط',
            UPLOAD_ERR_NO_FILE => 'لم يتم رفع أي ملف',
            UPLOAD_ERR_NO_TMP_DIR => 'مجلد الملفات المؤقتة غير موجود',
            UPLOAD_ERR_CANT_WRITE => 'فشل في كتابة الملف',
            UPLOAD_ERR_EXTENSION => 'امتداد PHP أوقف رفع الملف'
        ];
        
        $errorMsg = isset($errorMessages[$file['error']]) ? $errorMessages[$file['error']] : 'خطأ غير معروف';
        echo "<p style='color: red;'>❌ خطأ في رفع الملف: $errorMsg</p>";
    }
}

// نموذج اختبار الرفع
echo "<h4>نموذج اختبار رفع الصور:</h4>";
?>

<form method="POST" enctype="multipart/form-data" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <div style="margin-bottom: 15px;">
        <label for="test_image"><strong>اختر صورة للاختبار:</strong></label><br>
        <input type="file" id="test_image" name="test_image" accept="image/*" required>
    </div>
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        اختبار رفع الصورة
    </button>
</form>

<?php
// الحلول المقترحة
echo "<h3>6. الحلول المقترحة:</h3>";

if (!is_dir($uploadsPath)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ مجلد uploads غير موجود</h4>";
    echo "<p><strong>الحل:</strong></p>";
    echo "<ol>";
    echo "<li>إنشاء مجلد uploads في المسار: $uploadsPath</li>";
    echo "<li>إنشاء المجلدات الفرعية: products, categories, users</li>";
    echo "<li>تعيين صلاحيات 755 أو 777</li>";
    echo "</ol>";
    echo "<a href='?create_dirs=1' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>إنشاء المجلدات تلقائياً</a>";
    echo "</div>";
}

if (is_dir($uploadsPath) && !is_writable($uploadsPath)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ مجلد uploads غير قابل للكتابة</h4>";
    echo "<p><strong>الحل:</strong> تغيير صلاحيات المجلد إلى 755 أو 777</p>";
    echo "</div>";
}

// إنشاء المجلدات تلقائياً
if (isset($_GET['create_dirs']) && $_GET['create_dirs'] == '1') {
    echo "<h3>7. إنشاء المجلدات:</h3>";
    
    $dirsToCreate = [
        $uploadsPath,
        $uploadsPath . '/products',
        $uploadsPath . '/categories',
        $uploadsPath . '/users'
    ];
    
    foreach ($dirsToCreate as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                echo "<p style='color: green;'>✅ تم إنشاء مجلد: $dir</p>";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء مجلد: $dir</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ مجلد موجود بالفعل: $dir</p>";
        }
    }
    
    // إنشاء ملف .htaccess لحماية المجلد
    $htaccessContent = "Options -Indexes\n<Files *.php>\nDeny from all\n</Files>";
    file_put_contents($uploadsPath . '/.htaccess', $htaccessContent);
    echo "<p style='color: green;'>✅ تم إنشاء ملف .htaccess للحماية</p>";
}

echo "<h3>8. روابط مفيدة:</h3>";
echo "<a href='edit-product.php?id=3' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>العودة لتعديل المنتج</a>";
echo "<a href='add-product.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتج جديد</a>";
?>
