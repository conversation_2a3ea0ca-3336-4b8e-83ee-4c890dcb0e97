<?php
/**
 * استبدال صفحة المنتجات بالنسخة المُصلحة
 */

echo "<h2>استبدال صفحة المنتجات</h2>";

try {
    // التحقق من وجود الملفات
    $originalFile = 'products.php';
    $fixedFile = 'products_fixed.php';
    $backupFile = 'products_backup.php';
    
    if (!file_exists($originalFile)) {
        throw new Exception("الملف الأصلي غير موجود: $originalFile");
    }
    
    if (!file_exists($fixedFile)) {
        throw new Exception("الملف المُصلح غير موجود: $fixedFile");
    }
    
    echo "<h3>1. إنشاء نسخة احتياطية:</h3>";
    
    // إنشاء نسخة احتياطية
    if (copy($originalFile, $backupFile)) {
        echo "✅ تم إنشاء نسخة احتياطية: $backupFile<br>";
    } else {
        throw new Exception("فشل في إنشاء نسخة احتياطية");
    }
    
    echo "<h3>2. قراءة الملف المُصلح:</h3>";
    
    // قراءة محتوى الملف المُصلح
    $fixedContent = file_get_contents($fixedFile);
    if ($fixedContent === false) {
        throw new Exception("فشل في قراءة الملف المُصلح");
    }
    
    // تعديل المحتوى لإزالة معلومات التشخيص
    $cleanContent = str_replace(
        'products_fixed.php',
        'products.php',
        $fixedContent
    );
    
    // إزالة معلومات التشخيص المفصلة
    $cleanContent = preg_replace(
        '/<!-- Debug Info -->.*?<\/div>/s',
        '<!-- تم إصلاح مشكلة عرض المنتجات -->',
        $cleanContent
    );
    
    echo "✅ تم تنظيف المحتوى<br>";
    
    echo "<h3>3. استبدال الملف الأصلي:</h3>";
    
    // كتابة المحتوى المُصلح في الملف الأصلي
    if (file_put_contents($originalFile, $cleanContent)) {
        echo "✅ تم استبدال الملف الأصلي بنجاح<br>";
    } else {
        throw new Exception("فشل في كتابة الملف الأصلي");
    }
    
    echo "<h3>4. التحقق من النتيجة:</h3>";
    
    // التحقق من حجم الملف
    $originalSize = filesize($originalFile);
    $fixedSize = filesize($fixedFile);
    
    echo "حجم الملف الأصلي الجديد: " . number_format($originalSize) . " بايت<br>";
    echo "حجم الملف المُصلح: " . number_format($fixedSize) . " بايت<br>";
    
    if ($originalSize > 1000) {
        echo "✅ الملف يبدو صحيحاً<br>";
    } else {
        echo "⚠️ الملف قد يكون صغيراً جداً<br>";
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ تم الاستبدال بنجاح!</h3>";
    echo "<p><strong>ما تم عمله:</strong></p>";
    echo "<ul>";
    echo "<li>✅ إنشاء نسخة احتياطية من الملف الأصلي</li>";
    echo "<li>✅ استبدال products.php بالنسخة المُصلحة</li>";
    echo "<li>✅ تنظيف معلومات التشخيص</li>";
    echo "<li>✅ الحفاظ على جميع الوظائف</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>5. اختبار النتيجة:</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>اختبر الآن:</strong></p>";
    echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;' target='_blank'>صفحة المنتجات الأصلية</a>";
    echo "<p style='margin-top: 10px;'><strong>يجب أن تظهر المنتجات الآن!</strong></p>";
    echo "</div>";
    
    echo "<h3>6. في حالة وجود مشاكل:</h3>";
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>لاستعادة الملف الأصلي:</strong></p>";
    echo "<code>copy('$backupFile', '$originalFile')</code>";
    echo "<br><br>";
    echo "<a href='?restore=1' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>استعادة النسخة الاحتياطية</a>";
    echo "</div>";
    
    echo "<h3>7. ملفات أخرى مفيدة:</h3>";
    echo "<ul>";
    echo "<li><a href='products_fixed.php'>products_fixed.php</a> - النسخة المُصلحة الأصلية</li>";
    echo "<li><a href='products_backup.php'>products_backup.php</a> - النسخة الاحتياطية</li>";
    echo "<li><a href='products_simple.php'>products_simple.php</a> - النسخة المبسطة</li>";
    echo "<li><a href='test_display.php'>test_display.php</a> - اختبار العرض</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ حدث خطأ:</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// استعادة النسخة الاحتياطية
if (isset($_GET['restore']) && $_GET['restore'] == '1') {
    echo "<h3>استعادة النسخة الاحتياطية:</h3>";
    
    if (file_exists('products_backup.php')) {
        if (copy('products_backup.php', 'products.php')) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "✅ تم استعادة النسخة الاحتياطية بنجاح";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "❌ فشل في استعادة النسخة الاحتياطية";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "⚠️ النسخة الاحتياطية غير موجودة";
        echo "</div>";
    }
}
?>
