<?php
/**
 * إدارة المخزون
 * Inventory Management
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_stock':
                $productId = intval($_POST['product_id']);
                $newStock = intval($_POST['stock_quantity']);
                $minStock = intval($_POST['min_stock_level']);
                $location = cleanInput($_POST['location'] ?? '');
                $notes = cleanInput($_POST['notes'] ?? '');
                
                $updateData = [
                    'stock_quantity' => $newStock,
                    'min_stock_level' => $minStock,
                    'location' => $location,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                if ($database->update('products', $updateData, 'id = :id', ['id' => $productId])) {
                    // تسجيل حركة المخزون
                    $stockData = [
                        'product_id' => $productId,
                        'movement_type' => 'adjustment',
                        'quantity' => $newStock,
                        'notes' => $notes ?: 'تحديث المخزون من الإدارة',
                        'user_id' => $_SESSION['user_id']
                    ];
                    $database->insert('stock_movements', $stockData);
                    
                    $message = 'تم تحديث المخزون بنجاح';
                    $messageType = 'success';
                    logActivity('inventory_updated', "تم تحديث مخزون المنتج رقم: {$productId}");
                } else {
                    $message = 'حدث خطأ أثناء تحديث المخزون';
                    $messageType = 'danger';
                }
                break;
                
            case 'add_stock':
                $productId = intval($_POST['product_id']);
                $addQuantity = intval($_POST['add_quantity']);
                $notes = cleanInput($_POST['notes'] ?? '');
                
                // الحصول على المخزون الحالي
                $product = $database->fetch("SELECT stock_quantity FROM products WHERE id = :id", ['id' => $productId]);
                if ($product) {
                    $newStock = $product['stock_quantity'] + $addQuantity;
                    
                    if ($database->update('products', ['stock_quantity' => $newStock, 'updated_at' => date('Y-m-d H:i:s')], 'id = :id', ['id' => $productId])) {
                        // تسجيل حركة المخزون
                        $stockData = [
                            'product_id' => $productId,
                            'movement_type' => 'in',
                            'quantity' => $addQuantity,
                            'notes' => $notes ?: 'إضافة مخزون',
                            'user_id' => $_SESSION['user_id']
                        ];
                        $database->insert('stock_movements', $stockData);
                        
                        $message = "تم إضافة {$addQuantity} قطعة للمخزون";
                        $messageType = 'success';
                    }
                }
                break;
                
            case 'remove_stock':
                $productId = intval($_POST['product_id']);
                $removeQuantity = intval($_POST['remove_quantity']);
                $notes = cleanInput($_POST['notes'] ?? '');
                
                // الحصول على المخزون الحالي
                $product = $database->fetch("SELECT stock_quantity FROM products WHERE id = :id", ['id' => $productId]);
                if ($product) {
                    $newStock = max(0, $product['stock_quantity'] - $removeQuantity);
                    
                    if ($database->update('products', ['stock_quantity' => $newStock, 'updated_at' => date('Y-m-d H:i:s')], 'id = :id', ['id' => $productId])) {
                        // تسجيل حركة المخزون
                        $stockData = [
                            'product_id' => $productId,
                            'movement_type' => 'out',
                            'quantity' => $removeQuantity,
                            'notes' => $notes ?: 'خصم مخزون',
                            'user_id' => $_SESSION['user_id']
                        ];
                        $database->insert('stock_movements', $stockData);
                        
                        $message = "تم خصم {$removeQuantity} قطعة من المخزون";
                        $messageType = 'success';
                    }
                }
                break;
        }
    }
}

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$categoryFilter = isset($_GET['category']) ? intval($_GET['category']) : 0;
$stockFilter = isset($_GET['stock_filter']) ? cleanInput($_GET['stock_filter']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

// بناء استعلام البحث
$whereConditions = ['p.status = "active"'];
$params = [];

if ($search) {
    $whereConditions[] = "(p.name LIKE :search OR p.sku LIKE :search)";
    $params['search'] = '%' . $search . '%';
}

if ($categoryFilter) {
    $whereConditions[] = "p.category_id = :category";
    $params['category'] = $categoryFilter;
}

if ($stockFilter) {
    switch ($stockFilter) {
        case 'low':
            $whereConditions[] = "p.stock_quantity <= p.min_stock_level";
            break;
        case 'out':
            $whereConditions[] = "p.stock_quantity = 0";
            break;
        case 'available':
            $whereConditions[] = "p.stock_quantity > 0";
            break;
    }
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

// الحصول على المنتجات
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        {$whereClause} 
        ORDER BY p.name ASC 
        LIMIT " . intval($itemsPerPage) . " OFFSET " . intval($offset);

$products = $database->fetchAll($sql, $params);

// حساب إجمالي المنتجات
$countSql = "SELECT COUNT(*) as total FROM products p {$whereClause}";
$totalResult = $database->fetch($countSql, $params);
$totalProducts = $totalResult['total'];
$totalPages = ceil($totalProducts / $itemsPerPage);

// الحصول على الأقسام للفلتر
$categories = $categoryManager->getAllCategories();

// إحصائيات المخزون
$stockStats = [
    'total_products' => $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'],
    'low_stock' => $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND stock_quantity <= min_stock_level")['count'],
    'out_of_stock' => $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND stock_quantity = 0")['count'],
    'total_value' => $database->fetch("SELECT SUM(stock_quantity * price) as total FROM products WHERE status = 'active'")['total'] ?? 0
];

$pageTitle = "إدارة المخزون - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إدارة المخزون</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Stock Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="admin-card text-center">
                            <div class="card-body">
                                <i class="fas fa-boxes fa-2x text-primary mb-2"></i>
                                <h5><?php echo number_format($stockStats['total_products']); ?></h5>
                                <p class="text-muted mb-0">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="admin-card text-center">
                            <div class="card-body">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                                <h5><?php echo number_format($stockStats['low_stock']); ?></h5>
                                <p class="text-muted mb-0">مخزون منخفض</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="admin-card text-center">
                            <div class="card-body">
                                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                <h5><?php echo number_format($stockStats['out_of_stock']); ?></h5>
                                <p class="text-muted mb-0">نفد المخزون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="admin-card text-center">
                            <div class="card-body">
                                <i class="fas fa-dollar-sign fa-2x text-success mb-2"></i>
                                <h5><?php echo formatPrice($stockStats['total_value']); ?></h5>
                                <p class="text-muted mb-0">قيمة المخزون</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">البحث والتصفية</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="inventory.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="اسم المنتج أو رمز SKU..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="category" class="form-label">القسم</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">جميع الأقسام</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo ($categoryFilter == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category['name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="stock_filter" class="form-label">حالة المخزون</label>
                                <select class="form-select" id="stock_filter" name="stock_filter">
                                    <option value="">جميع المنتجات</option>
                                    <option value="available" <?php echo ($stockFilter === 'available') ? 'selected' : ''; ?>>متوفر</option>
                                    <option value="low" <?php echo ($stockFilter === 'low') ? 'selected' : ''; ?>>مخزون منخفض</option>
                                    <option value="out" <?php echo ($stockFilter === 'out') ? 'selected' : ''; ?>>نفد المخزون</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn-admin btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="admin-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title">
                            المنتجات (<?php echo number_format($totalProducts); ?>)
                        </h5>
                        <div>
                            <a href="bulk-update-stock.php" class="btn-admin btn-warning btn-sm">
                                <i class="fas fa-edit"></i> تحديث مجمع
                            </a>
                            <a href="stock-movements.php" class="btn-admin btn-info btn-sm">
                                <i class="fas fa-history"></i> حركات المخزون
                            </a>
                            <button type="button" class="btn-admin btn-success btn-sm" onclick="exportInventory()">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($products)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>القسم</th>
                                        <th>المخزون الحالي</th>
                                        <th>الحد الأدنى</th>
                                        <th>الموقع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if ($product['image']): ?>
                                                <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                                                     alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                                     class="me-3" style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;">
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                                    <?php if ($product['sku']): ?>
                                                    <br><small class="text-muted">SKU: <?php echo htmlspecialchars($product['sku']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo ($product['stock_quantity'] <= $product['min_stock_level']) ? 'danger' : 'success'; ?> fs-6">
                                                <?php echo number_format($product['stock_quantity']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo number_format($product['min_stock_level']); ?></td>
                                        <td><?php echo htmlspecialchars($product['location'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <?php if ($product['stock_quantity'] == 0): ?>
                                                <span class="badge badge-danger">نفد المخزون</span>
                                            <?php elseif ($product['stock_quantity'] <= $product['min_stock_level']): ?>
                                                <span class="badge badge-warning">مخزون منخفض</span>
                                            <?php else: ?>
                                                <span class="badge badge-success">متوفر</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button type="button" class="btn-admin btn-primary btn-sm" 
                                                        onclick="manageStock(<?php echo $product['id']; ?>)" title="إدارة المخزون">
                                                    <i class="fas fa-warehouse"></i>
                                                </button>
                                                <button type="button" class="btn-admin btn-success btn-sm" 
                                                        onclick="addStock(<?php echo $product['id']; ?>)" title="إضافة مخزون">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button type="button" class="btn-admin btn-warning btn-sm" 
                                                        onclick="removeStock(<?php echo $product['id']; ?>)" title="خصم مخزون">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                                <a href="products.php?action=edit&id=<?php echo $product['id']; ?>" 
                                                   class="btn-admin btn-info btn-sm" title="تعديل المنتج">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات المنتجات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <!-- No Products -->
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h5>لا توجد منتجات</h5>
                            <p class="text-muted">
                                <?php if ($search || $categoryFilter || $stockFilter): ?>
                                    لم نجد أي منتجات تطابق معايير البحث المحددة
                                <?php else: ?>
                                    لا توجد منتجات في المخزون
                                <?php endif; ?>
                            </p>
                            <a href="products.php?action=add" class="btn-admin btn-primary">
                                <i class="fas fa-plus"></i> إضافة منتج جديد
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Stock Management Modal -->
    <div class="modal fade" id="stockModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إدارة المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="stockModalContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function manageStock(productId) {
        fetch('stock-modal.php?id=' + productId)
        .then(response => response.text())
        .then(data => {
            document.getElementById('stockModalContent').innerHTML = data;
            new bootstrap.Modal(document.getElementById('stockModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل بيانات المخزون');
        });
    }
    
    function addStock(productId) {
        const quantity = prompt('كم قطعة تريد إضافتها؟');
        if (quantity && !isNaN(quantity) && quantity > 0) {
            const notes = prompt('ملاحظات (اختياري):') || '';
            
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="add_stock">
                <input type="hidden" name="product_id" value="${productId}">
                <input type="hidden" name="add_quantity" value="${quantity}">
                <input type="hidden" name="notes" value="${notes}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function removeStock(productId) {
        const quantity = prompt('كم قطعة تريد خصمها؟');
        if (quantity && !isNaN(quantity) && quantity > 0) {
            const notes = prompt('ملاحظات (اختياري):') || '';
            
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="remove_stock">
                <input type="hidden" name="product_id" value="${productId}">
                <input type="hidden" name="remove_quantity" value="${quantity}">
                <input type="hidden" name="notes" value="${notes}">
            `;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    function exportInventory() {
        window.open('export-inventory.php', '_blank');
    }
    </script>
</body>
</html>
