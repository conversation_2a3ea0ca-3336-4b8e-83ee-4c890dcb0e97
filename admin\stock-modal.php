<?php
/**
 * مودال إدارة المخزون
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    http_response_code(403);
    echo '<div class="alert alert-danger">غير مصرح لك بالوصول</div>';
    exit;
}

$productId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$productId) {
    echo '<div class="alert alert-danger">معرف المنتج غير صحيح</div>';
    exit;
}

// الحصول على بيانات المنتج
$product = $database->fetch("SELECT * FROM products WHERE id = :id", ['id' => $productId]);

if (!$product) {
    echo '<div class="alert alert-danger">المنتج غير موجود</div>';
    exit;
}

// الحصول على آخر حركات المخزون للمنتج
$recentMovements = $database->fetchAll(
    "SELECT sm.*, u.username 
     FROM stock_movements sm 
     LEFT JOIN users u ON sm.user_id = u.id 
     WHERE sm.product_id = :product_id 
     ORDER BY sm.created_at DESC 
     LIMIT 10", 
    ['product_id' => $productId]
);
?>

<div class="stock-management">
    <!-- Product Info -->
    <div class="row mb-4">
        <div class="col-md-4">
            <?php if ($product['image']): ?>
            <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                 class="img-fluid rounded">
            <?php else: ?>
            <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="fas fa-image fa-3x text-muted"></i>
            </div>
            <?php endif; ?>
        </div>
        <div class="col-md-8">
            <h5><?php echo htmlspecialchars($product['name']); ?></h5>
            <?php if ($product['sku']): ?>
            <p class="text-muted mb-2">SKU: <?php echo htmlspecialchars($product['sku']); ?></p>
            <?php endif; ?>
            <div class="row">
                <div class="col-6">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">المخزون الحالي</h6>
                            <h4 class="text-<?php echo ($product['stock_quantity'] <= $product['min_stock_level']) ? 'danger' : 'success'; ?>">
                                <?php echo number_format($product['stock_quantity']); ?>
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h6 class="card-title">الحد الأدنى</h6>
                            <h4 class="text-warning"><?php echo number_format($product['min_stock_level']); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Management Form -->
    <form method="POST" action="inventory.php">
        <input type="hidden" name="action" value="update_stock">
        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
        
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="stock_quantity" class="form-label">المخزون الجديد</label>
                <input type="number" class="form-control" id="stock_quantity" name="stock_quantity" 
                       value="<?php echo $product['stock_quantity']; ?>" min="0" required>
            </div>
            <div class="col-md-6">
                <label for="min_stock_level" class="form-label">الحد الأدنى للمخزون</label>
                <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" 
                       value="<?php echo $product['min_stock_level']; ?>" min="0" required>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="location" class="form-label">موقع المنتج في المحل</label>
            <input type="text" class="form-control" id="location" name="location" 
                   value="<?php echo htmlspecialchars($product['location'] ?? ''); ?>" 
                   placeholder="مثال: رف A - مستوى 2">
        </div>
        
        <div class="mb-3">
            <label for="notes" class="form-label">ملاحظات</label>
            <textarea class="form-control" id="notes" name="notes" rows="3" 
                      placeholder="أي ملاحظات حول تحديث المخزون..."></textarea>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> حفظ التحديثات
            </button>
        </div>
    </form>

    <!-- Quick Actions -->
    <hr class="my-4">
    <h6>إجراءات سريعة</h6>
    <div class="row g-2 mb-4">
        <div class="col-md-4">
            <button type="button" class="btn btn-success w-100" onclick="quickAddStock()">
                <i class="fas fa-plus"></i> إضافة مخزون
            </button>
        </div>
        <div class="col-md-4">
            <button type="button" class="btn btn-warning w-100" onclick="quickRemoveStock()">
                <i class="fas fa-minus"></i> خصم مخزون
            </button>
        </div>
        <div class="col-md-4">
            <button type="button" class="btn btn-info w-100" onclick="viewMovements()">
                <i class="fas fa-history"></i> عرض الحركات
            </button>
        </div>
    </div>

    <!-- Recent Movements -->
    <?php if (!empty($recentMovements)): ?>
    <h6>آخر حركات المخزون</h6>
    <div class="table-responsive">
        <table class="table table-sm">
            <thead>
                <tr>
                    <th>النوع</th>
                    <th>الكمية</th>
                    <th>المستخدم</th>
                    <th>التاريخ</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($recentMovements as $movement): ?>
                <tr>
                    <td>
                        <?php
                        $typeClass = '';
                        $typeText = '';
                        switch($movement['movement_type']) {
                            case 'in':
                                $typeClass = 'success';
                                $typeText = 'إدخال';
                                break;
                            case 'out':
                                $typeClass = 'danger';
                                $typeText = 'إخراج';
                                break;
                            case 'adjustment':
                                $typeClass = 'warning';
                                $typeText = 'تعديل';
                                break;
                            case 'sale':
                                $typeClass = 'primary';
                                $typeText = 'بيع';
                                break;
                            case 'return':
                                $typeClass = 'info';
                                $typeText = 'إرجاع';
                                break;
                            default:
                                $typeClass = 'secondary';
                                $typeText = $movement['movement_type'];
                        }
                        ?>
                        <span class="badge bg-<?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                    </td>
                    <td>
                        <span class="badge bg-<?php echo ($movement['movement_type'] == 'in' || $movement['movement_type'] == 'return') ? 'success' : 'danger'; ?>">
                            <?php echo ($movement['movement_type'] == 'in' || $movement['movement_type'] == 'return') ? '+' : '-'; ?>
                            <?php echo number_format($movement['quantity']); ?>
                        </span>
                    </td>
                    <td><?php echo htmlspecialchars($movement['username'] ?? 'غير محدد'); ?></td>
                    <td><?php echo formatDate($movement['created_at'], 'd/m H:i'); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endif; ?>
</div>

<script>
function quickAddStock() {
    const quantity = prompt('كم قطعة تريد إضافتها؟');
    if (quantity && !isNaN(quantity) && quantity > 0) {
        const currentStock = parseInt(document.getElementById('stock_quantity').value);
        document.getElementById('stock_quantity').value = currentStock + parseInt(quantity);
        document.getElementById('notes').value = `إضافة ${quantity} قطعة للمخزون`;
    }
}

function quickRemoveStock() {
    const quantity = prompt('كم قطعة تريد خصمها؟');
    if (quantity && !isNaN(quantity) && quantity > 0) {
        const currentStock = parseInt(document.getElementById('stock_quantity').value);
        const newStock = Math.max(0, currentStock - parseInt(quantity));
        document.getElementById('stock_quantity').value = newStock;
        document.getElementById('notes').value = `خصم ${quantity} قطعة من المخزون`;
    }
}

function viewMovements() {
    window.open('stock-movements.php?search=<?php echo urlencode($product['name']); ?>', '_blank');
}
</script>
