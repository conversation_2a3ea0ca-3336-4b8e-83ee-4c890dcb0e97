<?php
/**
 * صفحة المنتجات المُصلحة
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../includes/functions.php';
    
    // التحقق من صلاحيات الإدارة
    if (!isAdmin()) {
        header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
        exit;
    }
    
    $message = '';
    $messageType = 'info';
    
    // معالجة الإجراءات
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'delete':
                    $productId = intval($_POST['product_id']);
                    if ($database->delete('products', 'id = :id', ['id' => $productId])) {
                        $message = 'تم حذف المنتج بنجاح';
                        $messageType = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء حذف المنتج';
                        $messageType = 'danger';
                    }
                    break;
                    
                case 'toggle_status':
                    $productId = intval($_POST['product_id']);
                    $newStatus = $_POST['status'] === 'active' ? 'inactive' : 'active';
                    if ($database->update('products', ['status' => $newStatus], 'id = :id', ['id' => $productId])) {
                        $message = 'تم تحديث حالة المنتج بنجاح';
                        $messageType = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء تحديث حالة المنتج';
                        $messageType = 'danger';
                    }
                    break;
            }
        }
    }
    
    // معاملات البحث والتصفية
    $search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
    $categoryFilter = isset($_GET['category']) ? intval($_GET['category']) : 0;
    $statusFilter = isset($_GET['status']) ? cleanInput($_GET['status']) : '';
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $itemsPerPage = defined('ADMIN_ITEMS_PER_PAGE') ? ADMIN_ITEMS_PER_PAGE : 20;
    $offset = ($page - 1) * $itemsPerPage;
    
    // بناء استعلام البحث
    $whereConditions = [];
    $params = [];
    
    if ($search) {
        $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
        $params['search'] = '%' . $search . '%';
    }
    
    if ($categoryFilter) {
        $whereConditions[] = "p.category_id = :category";
        $params['category'] = $categoryFilter;
    }
    
    if ($statusFilter) {
        $whereConditions[] = "p.status = :status";
        $params['status'] = $statusFilter;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // الحصول على المنتجات - إصلاح مشكلة LIMIT/OFFSET
    $sql = "SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            {$whereClause}
            ORDER BY p.created_at DESC";

    // إضافة LIMIT بطريقة آمنة
    if ($itemsPerPage > 0) {
        $sql .= " LIMIT " . intval($itemsPerPage);
        if ($offset > 0) {
            $sql .= " OFFSET " . intval($offset);
        }
    }

    $products = $database->fetchAll($sql, $params);
    
    // حساب إجمالي المنتجات
    $countSql = "SELECT COUNT(*) as total FROM products p LEFT JOIN categories c ON p.category_id = c.id {$whereClause}";
    $countParams = array_diff_key($params, ['limit' => '', 'offset' => '']);
    $totalResult = $database->fetch($countSql, $countParams);
    $totalProducts = $totalResult['total'];
    $totalPages = ceil($totalProducts / $itemsPerPage);
    
    // الحصول على الأقسام للفلترة
    $categories = $categoryManager->getAllCategories();
    
    $pageTitle = "إدارة المنتجات - " . getSiteSetting('site_name');
    
} catch (Exception $e) {
    die("خطأ في تحميل الصفحة: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إدارة المنتجات</h1>
                <div class="header-actions">
                    <a href="add-product.php" class="btn-admin btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </a>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- تم إصلاح مشكلة عرض المنتجات -->

                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">البحث والتصفية</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="products.php" class="row g-3">
                            <div class="col-md-4">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="اسم المنتج، الوصف، أو رقم المنتج..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="category" class="form-label">القسم</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">جميع الأقسام</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo $category['id']; ?>" <?php echo ($categoryFilter == $category['id']) ? 'selected' : ''; ?>>
                                        <?php echo $category['name']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="active" <?php echo ($statusFilter === 'active') ? 'selected' : ''; ?>>نشط</option>
                                    <option value="inactive" <?php echo ($statusFilter === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                                    <option value="out_of_stock" <?php echo ($statusFilter === 'out_of_stock') ? 'selected' : ''; ?>>نفد المخزون</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn-admin btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            المنتجات (<?php echo number_format($totalProducts); ?>)
                        </h5>
                        <div class="card-actions">
                            <a href="add-product.php" class="btn-admin btn-success btn-sm">
                                <i class="fas fa-plus"></i> إضافة منتج
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($products) > 0): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>القسم</th>
                                        <th>السعر</th>
                                        <th>المخزون</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td><?php echo $product['id']; ?></td>
                                        <td>
                                            <?php if (!empty($product['image'])): ?>
                                            <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                                 width="50" height="50" class="rounded">
                                            <?php else: ?>
                                            <div class="no-image-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                                <?php if (!empty($product['sku'])): ?>
                                                <br><small class="text-muted">رقم المنتج: <?php echo htmlspecialchars($product['sku']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <?php if (!empty($product['sale_price'])): ?>
                                            <span class="text-success"><?php echo formatPrice($product['sale_price']); ?></span>
                                            <br><small class="text-muted text-decoration-line-through"><?php echo formatPrice($product['price']); ?></small>
                                            <?php else: ?>
                                            <?php echo formatPrice($product['price']); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="<?php echo ($product['stock_quantity'] <= 5) ? 'text-danger' : 'text-success'; ?>">
                                                <?php echo $product['stock_quantity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = 'secondary';
                                            $statusText = $product['status'];
                                            switch($product['status']) {
                                                case 'active':
                                                    $statusClass = 'success';
                                                    $statusText = 'نشط';
                                                    break;
                                                case 'inactive':
                                                    $statusClass = 'danger';
                                                    $statusText = 'غير نشط';
                                                    break;
                                                case 'out_of_stock':
                                                    $statusClass = 'warning';
                                                    $statusText = 'نفد المخزون';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <a href="edit-product.php?id=<?php echo $product['id']; ?>" 
                                                   class="btn-admin btn-info btn-sm" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل تريد تغيير حالة هذا المنتج؟')">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                    <input type="hidden" name="status" value="<?php echo $product['status']; ?>">
                                                    <button type="submit" class="btn-admin btn-warning btn-sm" title="تغيير الحالة">
                                                        <i class="fas fa-toggle-on"></i>
                                                    </button>
                                                </form>
                                                
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                    <button type="submit" class="btn-admin btn-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات المنتجات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <!-- No Products -->
                        <div class="text-center py-5">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <h5>لا توجد منتجات</h5>
                            <p class="text-muted">
                                <?php if ($search || $categoryFilter || $statusFilter): ?>
                                    لم نجد أي منتجات تطابق معايير البحث المحددة
                                <?php else: ?>
                                    لم يتم إضافة أي منتجات بعد
                                <?php endif; ?>
                            </p>
                            <a href="add-product.php" class="btn-admin btn-primary">
                                <i class="fas fa-plus"></i> إضافة منتج جديد
                            </a>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <h6>روابط مفيدة:</h6>
                            <a href="products.php" class="btn btn-secondary btn-sm">الصفحة الأصلية</a>
                            <a href="products_debug.php" class="btn btn-info btn-sm">تشخيص مفصل</a>
                            <a href="test_display.php" class="btn btn-warning btn-sm">اختبار العرض</a>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
