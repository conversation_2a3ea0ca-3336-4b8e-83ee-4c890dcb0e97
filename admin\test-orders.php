<?php
/**
 * اختبار نظام الطلبات
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$message = '';
$messageType = 'info';

// اختبار إنشاء طلب تجريبي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_order'])) {
    try {
        // إنشاء طلب تجريبي
        $orderNumber = 'TEST-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
        
        $orderData = [
            'order_number' => $orderNumber,
            'customer_name' => 'عميل تجريبي ' . rand(1, 100),
            'customer_email' => 'test' . rand(1, 100) . '@example.com',
            'customer_phone' => '05' . rand(********, ********),
            'customer_address' => 'عنوان تجريبي، الرياض، المملكة العربية السعودية',
            'total_amount' => rand(100, 500),
            'payment_method' => (rand(0, 1) ? 'cash_on_delivery' : 'bank_transfer'),
            'payment_status' => 'pending',
            'status' => 'pending',
            'notes' => 'طلب تجريبي للاختبار',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $orderId = $database->insert('orders', $orderData);
        
        if ($orderId) {
            logActivity('test_order_created', "تم إنشاء طلب تجريبي: {$orderNumber}");
            $message = "تم إنشاء طلب تجريبي بنجاح: {$orderNumber} (ID: {$orderId})";
            $messageType = 'success';
        } else {
            throw new Exception('فشل في إنشاء الطلب');
        }
        
    } catch (Exception $e) {
        $message = "خطأ في إنشاء الطلب: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// اختبار تحديث حالة طلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_update_status'])) {
    try {
        $orderId = intval($_POST['order_id']);
        $newStatus = $_POST['new_status'];
        
        $order = $database->fetch("SELECT order_number FROM orders WHERE id = :id", ['id' => $orderId]);
        if (!$order) {
            throw new Exception('الطلب غير موجود');
        }
        
        $updateResult = $database->update('orders', 
            ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')], 
            'id = :id', 
            ['id' => $orderId]
        );
        
        if ($updateResult) {
            logActivity('test_order_status_updated', "تم تحديث حالة الطلب {$order['order_number']} إلى {$newStatus}");
            $message = "تم تحديث حالة الطلب {$order['order_number']} بنجاح";
            $messageType = 'success';
        } else {
            throw new Exception('فشل في تحديث الحالة');
        }
        
    } catch (Exception $e) {
        $message = "خطأ في تحديث الحالة: " . $e->getMessage();
        $messageType = 'danger';
    }
}

echo "<h2>🧪 اختبار نظام الطلبات</h2>";

// عرض الرسائل
if ($message) {
    echo "<div style='padding: 15px; margin: 20px 0; border-radius: 5px; background: " . 
         ($messageType == 'success' ? '#d4edda; color: #155724;' : '#f8d7da; color: #721c24;') . "'>";
    echo $message;
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔧 اختبارات النظام:</h3>";

// فحص جدول الطلبات
echo "<h4>1. فحص جدول الطلبات:</h4>";
try {
    $ordersCount = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
    echo "<p style='color: green;'>✅ جدول الطلبات موجود - عدد الطلبات: $ordersCount</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ مشكلة في جدول الطلبات: " . $e->getMessage() . "</p>";
}

// فحص جدول عناصر الطلبات
echo "<h4>2. فحص جدول عناصر الطلبات:</h4>";
try {
    $orderItemsCount = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
    echo "<p style='color: green;'>✅ جدول عناصر الطلبات موجود - عدد العناصر: $orderItemsCount</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ جدول عناصر الطلبات غير موجود: " . $e->getMessage() . "</p>";
}

// فحص دوال النظام
echo "<h4>3. فحص دوال النظام:</h4>";
$functions = ['cleanInput', 'formatPrice', 'formatDate', 'logActivity'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ دالة $func موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة $func غير موجودة</p>";
    }
}

echo "</div>";

// إنشاء طلب تجريبي
echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🆕 إنشاء طلب تجريبي:</h3>";
echo "<p>اختبر إنشاء طلب جديد للتأكد من عمل النظام</p>";

echo "<form method='POST' style='margin: 20px 0;'>";
echo "<button type='submit' name='create_test_order' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;'>";
echo "<i class='fas fa-plus'></i> إنشاء طلب تجريبي";
echo "</button>";
echo "</form>";
echo "</div>";

// عرض الطلبات الحالية
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📋 الطلبات الحالية:</h3>";

try {
    $recentOrders = $database->fetchAll("SELECT * FROM orders ORDER BY created_at DESC LIMIT 10");
    
    if (!empty($recentOrders)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>رقم الطلب</th><th>العميل</th><th>المبلغ</th><th>الحالة</th><th>التاريخ</th><th>اختبار</th>";
        echo "</tr>";
        
        foreach ($recentOrders as $order) {
            $statusColor = 'black';
            switch($order['status']) {
                case 'pending': $statusColor = 'orange'; break;
                case 'confirmed': $statusColor = 'blue'; break;
                case 'delivered': $statusColor = 'green'; break;
                case 'cancelled': $statusColor = 'red'; break;
            }
            
            echo "<tr>";
            echo "<td>{$order['id']}</td>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td style='color: $statusColor; font-weight: bold;'>" . htmlspecialchars($order['status']) . "</td>";
            echo "<td>" . formatDate($order['created_at'], 'd/m/Y H:i') . "</td>";
            echo "<td>";
            
            // نموذج اختبار تحديث الحالة
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='order_id' value='{$order['id']}'>";
            echo "<select name='new_status' style='padding: 2px;'>";
            echo "<option value='pending'" . ($order['status'] == 'pending' ? ' selected' : '') . ">قيد الانتظار</option>";
            echo "<option value='confirmed'" . ($order['status'] == 'confirmed' ? ' selected' : '') . ">مؤكد</option>";
            echo "<option value='processing'" . ($order['status'] == 'processing' ? ' selected' : '') . ">قيد التحضير</option>";
            echo "<option value='shipped'" . ($order['status'] == 'shipped' ? ' selected' : '') . ">تم الشحن</option>";
            echo "<option value='delivered'" . ($order['status'] == 'delivered' ? ' selected' : '') . ">تم التوصيل</option>";
            echo "<option value='cancelled'" . ($order['status'] == 'cancelled' ? ' selected' : '') . ">ملغي</option>";
            echo "</select>";
            echo "<button type='submit' name='test_update_status' style='background: #007bff; color: white; padding: 2px 8px; border: none; border-radius: 3px; margin-left: 5px;'>تحديث</button>";
            echo "</form>";
            
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>لا توجد طلبات حالياً</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في استرجاع الطلبات: " . $e->getMessage() . "</p>";
}

echo "</div>";

// اختبار تفاصيل الطلب
echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔍 اختبار تفاصيل الطلب:</h3>";

if (!empty($recentOrders)) {
    $firstOrder = $recentOrders[0];
    echo "<p>اختبر عرض تفاصيل الطلب: {$firstOrder['order_number']}</p>";
    echo "<button onclick=\"testOrderDetails({$firstOrder['id']})\" style='background: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
    echo "<i class='fas fa-eye'></i> عرض تفاصيل الطلب";
    echo "</button>";
    
    echo "<div id='orderDetailsTest' style='margin-top: 20px; padding: 15px; background: white; border-radius: 5px; display: none;'></div>";
} else {
    echo "<p>أنشئ طلب تجريبي أولاً لاختبار عرض التفاصيل</p>";
}

echo "</div>";

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='orders.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة الطلبات</a>";
echo "<a href='create-sample-data.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء بيانات تجريبية</a>";
echo "<a href='activity-logs.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>سجل الأنشطة</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات:</h4>";
echo "<ul>";
echo "<li>الطلبات التجريبية تبدأ بـ TEST- لسهولة التمييز</li>";
echo "<li>يمكن حذف الطلبات التجريبية لاحقاً</li>";
echo "<li>اختبر جميع حالات الطلبات للتأكد من عمل النظام</li>";
echo "<li>تحقق من سجل الأنشطة لرؤية العمليات</li>";
echo "</ul>";
echo "</div>";
?>

<script>
function testOrderDetails(orderId) {
    const container = document.getElementById('orderDetailsTest');
    container.style.display = 'block';
    container.innerHTML = '<p>جاري تحميل التفاصيل...</p>';
    
    fetch('order-details-ajax.php?id=' + orderId)
    .then(response => response.text())
    .then(data => {
        container.innerHTML = data;
    })
    .catch(error => {
        console.error('Error:', error);
        container.innerHTML = '<p style="color: red;">خطأ في تحميل التفاصيل: ' + error.message + '</p>';
    });
}
</script>
