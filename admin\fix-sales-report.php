<?php
/**
 * إصلاح مشاكل تقرير المبيعات الشهرية
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$message = '';
$messageType = 'info';

// معالجة الإصلاح التلقائي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['auto_fix'])) {
    try {
        // 1. إنشاء جدول order_items إذا لم يكن موجود
        $database->query("
            CREATE TABLE IF NOT EXISTS order_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_order_id (order_id),
                INDEX idx_product_id (product_id),
                INDEX idx_created_at (created_at)
            )
        ");
        
        // 2. التحقق من وجود طلبات بدون عناصر
        $ordersWithoutItems = $database->fetchAll("
            SELECT o.* FROM orders o 
            LEFT JOIN order_items oi ON o.id = oi.order_id 
            WHERE oi.id IS NULL 
            LIMIT 10
        ");
        
        $itemsCreated = 0;
        
        // 3. إنشاء عناصر للطلبات الموجودة
        if (!empty($ordersWithoutItems)) {
            $products = $database->fetchAll("SELECT id, name, price FROM products WHERE status = 'active' LIMIT 5");
            
            if (!empty($products)) {
                foreach ($ordersWithoutItems as $order) {
                    // إضافة 1-3 منتجات عشوائية لكل طلب
                    $itemsCount = rand(1, 3);
                    $orderTotal = 0;
                    
                    for ($i = 0; $i < $itemsCount; $i++) {
                        $product = $products[array_rand($products)];
                        $quantity = rand(1, 2);
                        $price = $product['price'];
                        
                        $itemData = [
                            'order_id' => $order['id'],
                            'product_id' => $product['id'],
                            'quantity' => $quantity,
                            'price' => $price,
                            'created_at' => $order['created_at'],
                            'updated_at' => $order['created_at']
                        ];
                        
                        $database->insert('order_items', $itemData);
                        $itemsCreated++;
                        $orderTotal += ($price * $quantity);
                    }
                    
                    // تحديث إجمالي الطلب
                    $database->update('orders', 
                        ['total_amount' => $orderTotal], 
                        'id = :id', 
                        ['id' => $order['id']]
                    );
                }
            }
        }
        
        // 4. إنشاء بيانات تجريبية إضافية إذا لزم الأمر
        $totalItems = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
        
        if ($totalItems < 10) {
            // إنشاء طلبات إضافية
            $products = $database->fetchAll("SELECT id, name, price FROM products WHERE status = 'active' LIMIT 10");
            
            if (!empty($products)) {
                for ($i = 0; $i < 5; $i++) {
                    // إنشاء طلب جديد
                    $orderDate = date('Y-m-d H:i:s', strtotime('-' . rand(1, 30) . ' days'));
                    
                    $orderData = [
                        'order_number' => 'ORD-' . date('Ymd', strtotime($orderDate)) . '-' . strtoupper(substr(uniqid(), -6)),
                        'customer_name' => 'عميل تجريبي ' . ($i + 1),
                        'customer_email' => 'test' . ($i + 1) . '@example.com',
                        'customer_phone' => '05' . rand(10000000, 99999999),
                        'total_amount' => 0,
                        'status' => 'confirmed',
                        'payment_method' => 'cash_on_delivery',
                        'created_at' => $orderDate,
                        'updated_at' => $orderDate
                    ];
                    
                    $orderId = $database->insert('orders', $orderData);
                    
                    // إضافة عناصر للطلب
                    $itemsCount = rand(1, 3);
                    $orderTotal = 0;
                    
                    for ($j = 0; $j < $itemsCount; $j++) {
                        $product = $products[array_rand($products)];
                        $quantity = rand(1, 2);
                        $price = $product['price'];
                        
                        $itemData = [
                            'order_id' => $orderId,
                            'product_id' => $product['id'],
                            'quantity' => $quantity,
                            'price' => $price,
                            'created_at' => $orderDate,
                            'updated_at' => $orderDate
                        ];
                        
                        $database->insert('order_items', $itemData);
                        $itemsCreated++;
                        $orderTotal += ($price * $quantity);
                    }
                    
                    // تحديث إجمالي الطلب
                    $database->update('orders', 
                        ['total_amount' => $orderTotal], 
                        'id = :id', 
                        ['id' => $orderId]
                    );
                }
            }
        }
        
        logActivity('sales_report_fixed', "تم إصلاح تقرير المبيعات وإنشاء $itemsCreated عنصر");
        
        $message = "تم إصلاح تقرير المبيعات بنجاح! تم إنشاء $itemsCreated عنصر طلب.";
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في الإصلاح: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// فحص الحالة الحالية
$diagnostics = [
    'orders_table' => false,
    'order_items_table' => false,
    'products_table' => false,
    'orders_count' => 0,
    'order_items_count' => 0,
    'products_count' => 0,
    'orders_without_items' => 0
];

try {
    $diagnostics['orders_count'] = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
    $diagnostics['orders_table'] = true;
} catch (Exception $e) {}

try {
    $diagnostics['order_items_count'] = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
    $diagnostics['order_items_table'] = true;
} catch (Exception $e) {}

try {
    $diagnostics['products_count'] = $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
    $diagnostics['products_table'] = true;
} catch (Exception $e) {}

if ($diagnostics['orders_table'] && $diagnostics['order_items_table']) {
    try {
        $diagnostics['orders_without_items'] = $database->fetch("
            SELECT COUNT(*) as count FROM orders o 
            LEFT JOIN order_items oi ON o.id = oi.order_id 
            WHERE oi.id IS NULL
        ")['count'];
    } catch (Exception $e) {}
}

echo "<h2>🔧 إصلاح تقرير المبيعات الشهرية</h2>";

// عرض الرسائل
if ($message) {
    echo "<div style='padding: 15px; margin: 20px 0; border-radius: 5px; background: " . 
         ($messageType == 'success' ? '#d4edda; color: #155724;' : '#f8d7da; color: #721c24;') . "'>";
    echo $message;
    echo "</div>";
}

// عرض التشخيص
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📊 تشخيص الحالة الحالية:</h3>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

// جدول الطلبات
echo "<div style='background: " . ($diagnostics['orders_table'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h4 style='margin: 0; color: " . ($diagnostics['orders_table'] ? '#155724' : '#721c24') . ";'>" . $diagnostics['orders_count'] . "</h4>";
echo "<p style='margin: 5px 0 0 0;'>طلبات موجودة</p>";
echo "<small>" . ($diagnostics['orders_table'] ? '✅ الجدول موجود' : '❌ الجدول غير موجود') . "</small>";
echo "</div>";

// جدول عناصر الطلبات
echo "<div style='background: " . ($diagnostics['order_items_table'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h4 style='margin: 0; color: " . ($diagnostics['order_items_table'] ? '#155724' : '#721c24') . ";'>" . $diagnostics['order_items_count'] . "</h4>";
echo "<p style='margin: 5px 0 0 0;'>عناصر الطلبات</p>";
echo "<small>" . ($diagnostics['order_items_table'] ? '✅ الجدول موجود' : '❌ الجدول غير موجود') . "</small>";
echo "</div>";

// جدول المنتجات
echo "<div style='background: " . ($diagnostics['products_table'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h4 style='margin: 0; color: " . ($diagnostics['products_table'] ? '#155724' : '#721c24') . ";'>" . $diagnostics['products_count'] . "</h4>";
echo "<p style='margin: 5px 0 0 0;'>منتجات نشطة</p>";
echo "<small>" . ($diagnostics['products_table'] ? '✅ الجدول موجود' : '❌ الجدول غير موجود') . "</small>";
echo "</div>";

// طلبات بدون عناصر
echo "<div style='background: " . ($diagnostics['orders_without_items'] == 0 ? '#d4edda' : '#fff3cd') . "; padding: 15px; border-radius: 8px; text-align: center;'>";
echo "<h4 style='margin: 0; color: " . ($diagnostics['orders_without_items'] == 0 ? '#155724' : '#856404') . ";'>" . $diagnostics['orders_without_items'] . "</h4>";
echo "<p style='margin: 5px 0 0 0;'>طلبات بدون عناصر</p>";
echo "<small>" . ($diagnostics['orders_without_items'] == 0 ? '✅ جميع الطلبات لها عناصر' : '⚠️ تحتاج إصلاح') . "</small>";
echo "</div>";

echo "</div>";
echo "</div>";

// تحديد المشاكل والحلول
$problems = [];
$canAutoFix = true;

if (!$diagnostics['order_items_table']) {
    $problems[] = "جدول order_items غير موجود";
}

if ($diagnostics['order_items_count'] == 0) {
    $problems[] = "لا توجد عناصر طلبات";
}

if ($diagnostics['orders_without_items'] > 0) {
    $problems[] = "يوجد {$diagnostics['orders_without_items']} طلب بدون عناصر";
}

if ($diagnostics['products_count'] == 0) {
    $problems[] = "لا توجد منتجات نشطة";
    $canAutoFix = false;
}

if (!empty($problems)) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ المشاكل المكتشفة:</h3>";
    echo "<ul>";
    foreach ($problems as $problem) {
        echo "<li>$problem</li>";
    }
    echo "</ul>";
    
    if ($canAutoFix) {
        echo "<form method='POST' style='margin-top: 20px;'>";
        echo "<button type='submit' name='auto_fix' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;' onclick='return confirm(\"هل تريد إصلاح المشاكل تلقائياً؟ سيتم إنشاء بيانات تجريبية.\");'>";
        echo "<i class='fas fa-magic'></i> إصلاح تلقائي";
        echo "</button>";
        echo "</form>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin-top: 15px;'>";
        echo "<p><strong>لا يمكن الإصلاح التلقائي:</strong> تحتاج لإضافة منتجات أولاً</p>";
        echo "<a href='add-product.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة منتج</a>";
        echo "</div>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>🎉 لا توجد مشاكل!</h3>";
    echo "<p>تقرير المبيعات الشهرية جاهز للاستخدام</p>";
    echo "<a href='monthly-sales-report.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تقرير المبيعات الشهرية</a>";
    echo "</div>";
}

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='check-database-structure.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فحص قاعدة البيانات</a>";
echo "<a href='create-sample-data.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء بيانات تجريبية</a>";
echo "<a href='monthly-sales-report.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تقرير المبيعات</a>";
echo "<a href='inventory.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المخزون</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات:</h4>";
echo "<ul>";
echo "<li>الإصلاح التلقائي ينشئ بيانات تجريبية للاختبار</li>";
echo "<li>يمكن حذف البيانات التجريبية لاحقاً</li>";
echo "<li>تأكد من وجود منتجات نشطة قبل الإصلاح</li>";
echo "<li>النظام يحتاج جدول order_items لعمل تقرير المبيعات</li>";
echo "</ul>";
echo "</div>";
?>
