<?php
/**
 * اختبار إجراءات المودال
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

// الحصول على طلب للاختبار
$testOrder = null;
try {
    $testOrder = $database->fetch("SELECT * FROM orders ORDER BY created_at DESC LIMIT 1");
} catch (Exception $e) {
    // إنشاء طلب تجريبي إذا لم توجد طلبات
}

if (!$testOrder) {
    // إنشاء طلب تجريبي
    $orderData = [
        'order_number' => 'TEST-MODAL-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -4)),
        'customer_name' => 'عميل اختبار المودال',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '0512345678',
        'customer_address' => 'عنوان اختبار المودال',
        'total_amount' => 150.00,
        'payment_method' => 'cash_on_delivery',
        'payment_status' => 'pending',
        'status' => 'pending',
        'notes' => 'طلب تجريبي لاختبار المودال',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    try {
        $orderId = $database->insert('orders', $orderData);
        $testOrder = $database->fetch("SELECT * FROM orders WHERE id = :id", ['id' => $orderId]);
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
        echo "<h3>❌ خطأ في إنشاء طلب تجريبي</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
        exit;
    }
}

$pageTitle = "اختبار إجراءات المودال - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    .test-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;
        border: 1px solid #dee2e6;
    }
    .test-result {
        background: #e7f3ff;
        padding: 15px;
        border-radius: 5px;
        margin: 10px 0;
        border-left: 4px solid #007bff;
    }
    .console-log {
        background: #2d3748;
        color: #e2e8f0;
        padding: 10px;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
        margin: 10px 0;
    }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="mt-4 mb-4">🧪 اختبار إجراءات المودال</h1>
                
                <!-- معلومات الطلب التجريبي -->
                <div class="test-section">
                    <h3><i class="fas fa-info-circle"></i> طلب الاختبار</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr><td><strong>رقم الطلب:</strong></td><td><?php echo $testOrder['order_number']; ?></td></tr>
                                <tr><td><strong>العميل:</strong></td><td><?php echo $testOrder['customer_name']; ?></td></tr>
                                <tr><td><strong>الحالة:</strong></td><td><span class="badge bg-warning"><?php echo $testOrder['status']; ?></span></td></tr>
                                <tr><td><strong>المبلغ:</strong></td><td><?php echo formatPrice($testOrder['total_amount']); ?></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>اختبارات متاحة:</h5>
                            <ul>
                                <li>فتح المودال وعرض التفاصيل</li>
                                <li>تحديث الحالة من القائمة المنسدلة</li>
                                <li>استخدام الأزرار السريعة</li>
                                <li>مراقبة console.log</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- أزرار الاختبار -->
                <div class="test-section">
                    <h3><i class="fas fa-play"></i> اختبارات المودال</h3>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <h5>1. فتح المودال:</h5>
                            <button type="button" class="btn btn-primary btn-lg w-100" 
                                    onclick="showOrderDetails(<?php echo $testOrder['id']; ?>)">
                                <i class="fas fa-eye"></i> عرض تفاصيل الطلب
                            </button>
                            <small class="text-muted">سيفتح المودال مع تفاصيل الطلب</small>
                        </div>
                        
                        <div class="col-md-4">
                            <h5>2. اختبار JavaScript:</h5>
                            <button type="button" class="btn btn-info btn-lg w-100" 
                                    onclick="testJavaScriptFunctions()">
                                <i class="fas fa-code"></i> اختبار الدوال
                            </button>
                            <small class="text-muted">اختبار دوال JavaScript المستخدمة</small>
                        </div>
                        
                        <div class="col-md-4">
                            <h5>3. اختبار AJAX:</h5>
                            <button type="button" class="btn btn-success btn-lg w-100" 
                                    onclick="testAjaxLoad()">
                                <i class="fas fa-sync"></i> اختبار AJAX
                            </button>
                            <small class="text-muted">اختبار تحميل المحتوى عبر AJAX</small>
                        </div>
                    </div>
                </div>
                
                <!-- نتائج الاختبار -->
                <div class="test-section">
                    <h3><i class="fas fa-clipboard-list"></i> نتائج الاختبار</h3>
                    <div id="testResults" class="test-result" style="display: none;">
                        <h5>نتيجة الاختبار:</h5>
                        <div id="testOutput"></div>
                    </div>
                </div>
                
                <!-- سجل Console -->
                <div class="test-section">
                    <h3><i class="fas fa-terminal"></i> سجل Console</h3>
                    <p>افتح أدوات المطور (F12) ولاحظ رسائل console.log أثناء الاختبار</p>
                    <div id="consoleLog" class="console-log">
                        <div>جاهز للاختبار...</div>
                    </div>
                    <button type="button" class="btn btn-secondary btn-sm mt-2" onclick="clearConsoleLog()">
                        <i class="fas fa-trash"></i> مسح السجل
                    </button>
                </div>
                
                <!-- تعليمات الاختبار -->
                <div class="test-section">
                    <h3><i class="fas fa-question-circle"></i> تعليمات الاختبار</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>خطوات الاختبار:</h5>
                            <ol>
                                <li><strong>افتح أدوات المطور:</strong> اضغط F12</li>
                                <li><strong>اذهب لتبويب Console</strong></li>
                                <li><strong>انقر "عرض تفاصيل الطلب"</strong></li>
                                <li><strong>جرب تحديث الحالة</strong> من المودال</li>
                                <li><strong>لاحظ الرسائل</strong> في Console</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>ما يجب ملاحظته:</h5>
                            <ul>
                                <li>فتح المودال بنجاح</li>
                                <li>تحميل المحتوى عبر AJAX</li>
                                <li>عمل أزرار تحديث الحالة</li>
                                <li>رسائل console.log واضحة</li>
                                <li>إغلاق المودال بعد التحديث</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- روابط سريعة -->
                <div class="test-section">
                    <h3><i class="fas fa-link"></i> روابط سريعة</h3>
                    <div class="btn-group" role="group">
                        <a href="orders.php" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> صفحة الطلبات الأصلية
                        </a>
                        <a href="test-admin-actions.php" class="btn btn-outline-info">
                            <i class="fas fa-flask"></i> اختبار إجراءات الإدارة
                        </a>
                        <a href="test-orders.php" class="btn btn-outline-success">
                            <i class="fas fa-shopping-cart"></i> اختبار نظام الطلبات
                        </a>
                        <a href="activity-logs.php" class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> سجل الأنشطة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderDetailsContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // دالة لإضافة رسالة للسجل المحلي
    function addToConsoleLog(message) {
        const consoleLog = document.getElementById('consoleLog');
        const timestamp = new Date().toLocaleTimeString('ar-SA');
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `[${timestamp}] ${message}`;
        consoleLog.appendChild(logEntry);
        consoleLog.scrollTop = consoleLog.scrollHeight;
    }
    
    // دالة لمسح السجل
    function clearConsoleLog() {
        document.getElementById('consoleLog').innerHTML = '<div>تم مسح السجل...</div>';
    }
    
    // دالة لعرض نتائج الاختبار
    function showTestResult(title, content, type = 'info') {
        const resultsDiv = document.getElementById('testResults');
        const outputDiv = document.getElementById('testOutput');
        
        const colorClass = type === 'success' ? 'text-success' : type === 'error' ? 'text-danger' : 'text-info';
        
        outputDiv.innerHTML = `
            <h6 class="${colorClass}">${title}</h6>
            <p>${content}</p>
            <small class="text-muted">الوقت: ${new Date().toLocaleString('ar-SA')}</small>
        `;
        
        resultsDiv.style.display = 'block';
    }
    
    // دالة عرض تفاصيل الطلب
    function showOrderDetails(orderId) {
        console.log('🔍 فتح تفاصيل الطلب:', orderId);
        addToConsoleLog(`🔍 فتح تفاصيل الطلب: ${orderId}`);
        
        showTestResult('بدء تحميل المودال', `جاري تحميل تفاصيل الطلب رقم: ${orderId}`, 'info');
        
        // تحميل تفاصيل الطلب عبر AJAX
        fetch('order-details-ajax.php?id=' + orderId)
        .then(response => {
            console.log('📡 استجابة AJAX:', response.status);
            addToConsoleLog(`📡 استجابة AJAX: ${response.status}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            return response.text();
        })
        .then(data => {
            console.log('✅ تم تحميل المحتوى بنجاح');
            addToConsoleLog('✅ تم تحميل المحتوى بنجاح');
            
            document.getElementById('orderDetailsContent').innerHTML = data;
            
            const modal = new bootstrap.Modal(document.getElementById('orderDetailsModal'));
            modal.show();
            
            showTestResult('نجح تحميل المودال', 'تم تحميل تفاصيل الطلب وفتح المودال بنجاح', 'success');
            
            console.log('🎉 تم فتح المودال بنجاح');
            addToConsoleLog('🎉 تم فتح المودال بنجاح');
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل المودال:', error);
            addToConsoleLog(`❌ خطأ في تحميل المودال: ${error.message}`);
            
            showTestResult('فشل تحميل المودال', `خطأ: ${error.message}`, 'error');
            alert('حدث خطأ أثناء تحميل تفاصيل الطلب: ' + error.message);
        });
    }
    
    // دالة اختبار دوال JavaScript
    function testJavaScriptFunctions() {
        console.log('🧪 بدء اختبار دوال JavaScript');
        addToConsoleLog('🧪 بدء اختبار دوال JavaScript');
        
        let results = [];
        
        // اختبار وجود Bootstrap
        if (typeof bootstrap !== 'undefined') {
            results.push('✅ Bootstrap محمل بنجاح');
            console.log('✅ Bootstrap متاح');
        } else {
            results.push('❌ Bootstrap غير محمل');
            console.error('❌ Bootstrap غير متاح');
        }
        
        // اختبار دالة fetch
        if (typeof fetch !== 'undefined') {
            results.push('✅ دالة fetch متاحة');
            console.log('✅ دالة fetch متاحة');
        } else {
            results.push('❌ دالة fetch غير متاحة');
            console.error('❌ دالة fetch غير متاحة');
        }
        
        // اختبار console
        if (typeof console !== 'undefined') {
            results.push('✅ Console متاح');
            console.log('✅ Console يعمل بشكل صحيح');
        }
        
        addToConsoleLog('📋 نتائج اختبار JavaScript: ' + results.length + ' اختبار');
        
        showTestResult('نتائج اختبار JavaScript', results.join('<br>'), 'success');
    }
    
    // دالة اختبار AJAX
    function testAjaxLoad() {
        console.log('🌐 اختبار AJAX');
        addToConsoleLog('🌐 بدء اختبار AJAX');
        
        showTestResult('اختبار AJAX', 'جاري اختبار الاتصال...', 'info');
        
        fetch('order-details-ajax.php?id=<?php echo $testOrder['id']; ?>')
        .then(response => {
            const status = response.status;
            const statusText = response.statusText;
            
            console.log(`📊 حالة الاستجابة: ${status} ${statusText}`);
            addToConsoleLog(`📊 حالة الاستجابة: ${status} ${statusText}`);
            
            if (response.ok) {
                showTestResult('نجح اختبار AJAX', `الاتصال ناجح - الحالة: ${status}`, 'success');
                return response.text();
            } else {
                throw new Error(`${status} ${statusText}`);
            }
        })
        .then(data => {
            const dataLength = data.length;
            console.log(`📦 حجم البيانات المستلمة: ${dataLength} حرف`);
            addToConsoleLog(`📦 حجم البيانات: ${dataLength} حرف`);
            
            showTestResult('اكتمل اختبار AJAX', `تم استلام ${dataLength} حرف من البيانات`, 'success');
        })
        .catch(error => {
            console.error('❌ فشل اختبار AJAX:', error);
            addToConsoleLog(`❌ فشل اختبار AJAX: ${error.message}`);
            
            showTestResult('فشل اختبار AJAX', `خطأ: ${error.message}`, 'error');
        });
    }
    
    // تسجيل تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 تم تحميل صفحة اختبار المودال');
        addToConsoleLog('🚀 تم تحميل صفحة اختبار المودال');
        
        showTestResult('جاهز للاختبار', 'تم تحميل الصفحة بنجاح. يمكنك الآن بدء الاختبارات.', 'success');
    });
    </script>
</body>
</html>
