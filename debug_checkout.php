<?php
/**
 * تشخيص مشكلة عدم تسجيل الطلبات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/functions.php';

echo "<h2>تشخيص مشكلة عدم تسجيل الطلبات</h2>";

// التحقق من وجود جداول الطلبات
echo "<h3>1. فحص جداول الطلبات:</h3>";

$tables = ['orders', 'order_details'];
foreach ($tables as $table) {
    try {
        $result = $database->fetch("SELECT COUNT(*) as count FROM $table");
        echo "✅ جدول $table موجود - عدد السجلات: " . $result['count'] . "<br>";
    } catch (Exception $e) {
        echo "❌ جدول $table غير موجود أو به مشكلة: " . $e->getMessage() . "<br>";
    }
}

// فحص بنية جدول orders
echo "<h3>2. فحص بنية جدول orders:</h3>";
try {
    $columns = $database->fetchAll("DESCRIBE orders");
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "❌ خطأ في فحص بنية جدول orders: " . $e->getMessage() . "<br>";
}

// اختبار OrderManager
echo "<h3>3. اختبار OrderManager:</h3>";

if (isset($orderManager)) {
    echo "✅ متغير orderManager موجود<br>";
} else {
    echo "❌ متغير orderManager غير موجود<br>";
    echo "إنشاء OrderManager جديد...<br>";
    $orderManager = new OrderManager();
}

// اختبار دالة generateOrderNumber
echo "<h4>اختبار دالة generateOrderNumber:</h4>";
try {
    $orderNumber = generateOrderNumber();
    echo "✅ رقم الطلب التجريبي: $orderNumber<br>";
} catch (Exception $e) {
    echo "❌ خطأ في generateOrderNumber: " . $e->getMessage() . "<br>";
}

// اختبار إنشاء طلب تجريبي
echo "<h3>4. اختبار إنشاء طلب تجريبي:</h3>";

$testOrderData = [
    'customer_name' => 'عميل تجريبي',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '0501234567',
    'customer_address' => 'عنوان تجريبي',
    'total_amount' => 100.00,
    'payment_method' => 'cash_on_delivery',
    'notes' => 'طلب تجريبي للاختبار'
];

$testOrderItems = [
    [
        'product_id' => 1,
        'product_name' => 'منتج تجريبي',
        'product_price' => 50.00,
        'quantity' => 2
    ]
];

echo "<h4>بيانات الطلب التجريبي:</h4>";
echo "<pre>" . print_r($testOrderData, true) . "</pre>";

echo "<h4>عناصر الطلب التجريبي:</h4>";
echo "<pre>" . print_r($testOrderItems, true) . "</pre>";

try {
    echo "<h4>محاولة إنشاء الطلب:</h4>";
    $testOrderId = $orderManager->createOrder($testOrderData, $testOrderItems);
    
    if ($testOrderId) {
        echo "<p style='color: green;'>✅ تم إنشاء الطلب التجريبي بنجاح - معرف الطلب: $testOrderId</p>";
        
        // التحقق من الطلب في قاعدة البيانات
        $createdOrder = $database->fetch("SELECT * FROM orders WHERE id = :id", ['id' => $testOrderId]);
        if ($createdOrder) {
            echo "<h5>بيانات الطلب المحفوظ:</h5>";
            echo "<ul>";
            echo "<li><strong>رقم الطلب:</strong> " . $createdOrder['order_number'] . "</li>";
            echo "<li><strong>اسم العميل:</strong> " . $createdOrder['customer_name'] . "</li>";
            echo "<li><strong>المبلغ الإجمالي:</strong> " . formatPrice($createdOrder['total_amount']) . "</li>";
            echo "<li><strong>الحالة:</strong> " . $createdOrder['status'] . "</li>";
            echo "<li><strong>تاريخ الإنشاء:</strong> " . $createdOrder['created_at'] . "</li>";
            echo "</ul>";
            
            // التحقق من تفاصيل الطلب
            $orderDetails = $database->fetchAll("SELECT * FROM order_details WHERE order_id = :order_id", ['order_id' => $testOrderId]);
            echo "<h5>تفاصيل الطلب:</h5>";
            if (count($orderDetails) > 0) {
                echo "<ul>";
                foreach ($orderDetails as $detail) {
                    echo "<li>" . $detail['product_name'] . " - الكمية: " . $detail['quantity'] . " - السعر: " . formatPrice($detail['product_price']) . "</li>";
                }
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>❌ لا توجد تفاصيل للطلب</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ فشل في إنشاء الطلب التجريبي</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في إنشاء الطلب: " . $e->getMessage() . "</p>";
    echo "<p><strong>تفاصيل الخطأ:</strong></p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// اختبار السلة
echo "<h3>5. اختبار السلة:</h3>";

$cartItems = $cartManager->getCartItems();
$cartTotal = $cartManager->getCartTotal();
$cartCount = $cartManager->getCartCount();

echo "<ul>";
echo "<li><strong>عدد عناصر السلة:</strong> $cartCount</li>";
echo "<li><strong>إجمالي السلة:</strong> " . formatPrice($cartTotal) . "</li>";
echo "<li><strong>عناصر السلة:</strong> " . count($cartItems) . "</li>";
echo "</ul>";

if (empty($cartItems)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>⚠️ السلة فارغة</h4>";
    echo "<p>لاختبار checkout.php تحتاج لإضافة منتجات للسلة أولاً</p>";
    echo "<p><strong>خطوات الاختبار:</strong></p>";
    echo "<ol>";
    echo "<li>اذهب إلى <a href='products.php'>صفحة المنتجات</a></li>";
    echo "<li>أضف بعض المنتجات للسلة</li>";
    echo "<li>اذهب إلى <a href='cart.php'>سلة التسوق</a></li>";
    echo "<li>انقر 'إتمام الطلب'</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<h4>عناصر السلة الحالية:</h4>";
    echo "<ul>";
    foreach ($cartItems as $item) {
        echo "<li>" . $item['name'] . " - الكمية: " . $item['cart_quantity'] . " - المجموع: " . formatPrice($item['cart_total']) . "</li>";
    }
    echo "</ul>";
}

// اختبار دالة insert في قاعدة البيانات
echo "<h3>6. اختبار دالة insert:</h3>";

try {
    $testData = [
        'customer_name' => 'اختبار insert',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '0501111111',
        'customer_address' => 'عنوان اختبار',
        'total_amount' => 50.00,
        'payment_method' => 'cash_on_delivery',
        'status' => 'pending',
        'order_number' => 'TEST-' . time()
    ];
    
    $insertResult = $database->insert('orders', $testData);
    
    if ($insertResult) {
        $insertedId = $database->lastInsertId();
        echo "<p style='color: green;'>✅ تم إدراج سجل اختبار بنجاح - المعرف: $insertedId</p>";
        
        // حذف السجل التجريبي
        $database->delete('orders', 'id = :id', ['id' => $insertedId]);
        echo "<p style='color: blue;'>ℹ️ تم حذف السجل التجريبي</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في إدراج السجل التجريبي</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار insert: " . $e->getMessage() . "</p>";
}

echo "<h3>7. الحلول المقترحة:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>إذا كان الطلب التجريبي نجح:</h4>";
echo "<ul>";
echo "<li>المشكلة قد تكون في checkout.php نفسه</li>";
echo "<li>تحقق من أن السلة تحتوي على منتجات</li>";
echo "<li>تحقق من صحة البيانات المرسلة</li>";
echo "<li>تحقق من رسائل الخطأ في checkout.php</li>";
echo "</ul>";

echo "<h4>إذا فشل الطلب التجريبي:</h4>";
echo "<ul>";
echo "<li>مشكلة في دالة createOrder</li>";
echo "<li>مشكلة في بنية جداول قاعدة البيانات</li>";
echo "<li>مشكلة في دالة insert</li>";
echo "</ul>";
echo "</div>";

echo "<h3>8. روابط الاختبار:</h3>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتجات للسلة</a>";
echo "<a href='cart.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>عرض السلة</a>";
echo "<a href='checkout.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة الدفع</a>";
?>
