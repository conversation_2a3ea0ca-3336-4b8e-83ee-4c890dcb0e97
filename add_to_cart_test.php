<?php
/**
 * إضافة منتجات للسلة لاختبار checkout
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/functions.php';

echo "<h2>إضافة منتجات للسلة لاختبار checkout</h2>";

// الحصول على بعض المنتجات
$products = $productManager->getAllProducts(1, 5, 'active');

if (empty($products)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ لا توجد منتجات</h4>";
    echo "<p>تحتاج لإضافة منتجات أولاً</p>";
    echo "<a href='add_sample_data.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة بيانات تجريبية</a>";
    echo "</div>";
    exit;
}

// معالجة إضافة منتج للسلة
if (isset($_GET['add']) && is_numeric($_GET['add'])) {
    $productId = intval($_GET['add']);
    $quantity = isset($_GET['qty']) ? intval($_GET['qty']) : 1;
    
    $cartManager->addToCart($productId, $quantity);
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ تم إضافة المنتج للسلة</h4>";
    echo "<p>تم إضافة المنتج رقم $productId بكمية $quantity</p>";
    echo "</div>";
}

// معالجة مسح السلة
if (isset($_GET['clear'])) {
    $cartManager->clearCart();
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🗑️ تم مسح السلة</h4>";
    echo "</div>";
}

// عرض حالة السلة الحالية
$cartItems = $cartManager->getCartItems();
$cartTotal = $cartManager->getCartTotal();
$cartCount = $cartManager->getCartCount();

echo "<h3>حالة السلة الحالية:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<ul>";
echo "<li><strong>عدد العناصر:</strong> $cartCount</li>";
echo "<li><strong>إجمالي السعر:</strong> " . formatPrice($cartTotal) . "</li>";
echo "<li><strong>عدد المنتجات المختلفة:</strong> " . count($cartItems) . "</li>";
echo "</ul>";

if (!empty($cartItems)) {
    echo "<h4>المنتجات في السلة:</h4>";
    echo "<ul>";
    foreach ($cartItems as $item) {
        echo "<li>" . htmlspecialchars($item['name']) . " - الكمية: " . $item['cart_quantity'] . " - المجموع: " . formatPrice($item['cart_total']) . "</li>";
    }
    echo "</ul>";
    
    echo "<div style='margin-top: 15px;'>";
    echo "<a href='cart.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>عرض السلة</a>";
    echo "<a href='checkout.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إتمام الطلب</a>";
    echo "<a href='?clear=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>مسح السلة</a>";
    echo "</div>";
} else {
    echo "<p><strong>السلة فارغة</strong></p>";
}
echo "</div>";

// عرض المنتجات المتاحة
echo "<h3>المنتجات المتاحة:</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";

foreach ($products as $product) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: white;'>";
    echo "<h4 style='margin: 0 0 10px 0; color: #333;'>" . htmlspecialchars($product['name']) . "</h4>";
    
    if ($product['image']) {
        $imageUrl = UPLOADS_URL . '/' . $product['image'];
        echo "<img src='$imageUrl' alt='" . htmlspecialchars($product['name']) . "' style='width: 100%; height: 200px; object-fit: cover; border-radius: 5px; margin-bottom: 10px;'>";
    } else {
        echo "<div style='width: 100%; height: 200px; background: #f8f9fa; border-radius: 5px; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; color: #999;'>لا توجد صورة</div>";
    }
    
    echo "<p style='margin: 5px 0; color: #666; font-size: 14px;'>القسم: " . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</p>";
    
    if ($product['sale_price']) {
        echo "<p style='margin: 5px 0;'>";
        echo "<span style='color: #28a745; font-weight: bold; font-size: 18px;'>" . formatPrice($product['sale_price']) . "</span> ";
        echo "<span style='color: #999; text-decoration: line-through; font-size: 14px;'>" . formatPrice($product['price']) . "</span>";
        echo "</p>";
    } else {
        echo "<p style='margin: 5px 0; color: #333; font-weight: bold; font-size: 18px;'>" . formatPrice($product['price']) . "</p>";
    }
    
    echo "<p style='margin: 10px 0; color: #666; font-size: 13px;'>" . truncateText($product['short_description'] ?? $product['description'], 100) . "</p>";
    
    echo "<div style='margin-top: 15px; display: flex; gap: 10px; align-items: center;'>";
    echo "<a href='?add=" . $product['id'] . "&qty=1' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; font-size: 14px;'>إضافة 1</a>";
    echo "<a href='?add=" . $product['id'] . "&qty=2' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; font-size: 14px;'>إضافة 2</a>";
    echo "<a href='?add=" . $product['id'] . "&qty=3' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; font-size: 14px;'>إضافة 3</a>";
    echo "</div>";
    echo "</div>";
}

echo "</div>";

echo "<h3>خطوات اختبار checkout:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<ol>";
echo "<li>أضف بعض المنتجات للسلة باستخدام الأزرار أعلاه</li>";
echo "<li>اذهب إلى <a href='cart.php'>سلة التسوق</a> للتحقق من المنتجات</li>";
echo "<li>انقر 'إتمام الطلب' أو اذهب مباشرة إلى <a href='checkout.php'>صفحة الدفع</a></li>";
echo "<li>املأ البيانات المطلوبة</li>";
echo "<li>انقر 'تأكيد الطلب'</li>";
echo "<li>يجب أن تتم إعادة التوجيه لصفحة تأكيد الطلب</li>";
echo "</ol>";
echo "</div>";

echo "<h3>روابط مفيدة:</h3>";
echo "<a href='debug_checkout.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص checkout</a>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المنتجات</a>";
echo "<a href='admin/orders.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>طلبات الإدارة</a>";
?>
