<?php
/**
 * تشخيص مشكلة عرض المنتجات في لوحة التحكم
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص مشكلة عرض المنتجات</h2>";

try {
    require_once '../includes/functions.php';
    
    // التحقق من تسجيل الدخول
    echo "<h3>1. التحقق من تسجيل الدخول:</h3>";
    if (!isLoggedIn()) {
        echo "❌ غير مسجل دخول - <a href='login.php'>تسجيل الدخول</a><br>";
        exit;
    }
    
    if (!isAdmin()) {
        echo "❌ ليس مدير - <a href='login.php'>تسجيل دخول الإدارة</a><br>";
        exit;
    }
    
    echo "✅ مسجل دخول كمدير<br>";
    echo "المستخدم: " . $_SESSION['user_name'] . "<br>";
    
    // عدد المنتجات في قاعدة البيانات
    echo "<h3>2. عدد المنتجات في قاعدة البيانات:</h3>";
    $totalProducts = $database->fetch("SELECT COUNT(*) as count FROM products")['count'];
    echo "إجمالي المنتجات: " . $totalProducts . "<br>";
    
    if ($totalProducts == 0) {
        echo "❌ لا توجد منتجات في قاعدة البيانات<br>";
        echo "<a href='../add_sample_data.php'>إضافة بيانات تجريبية</a><br>";
        exit;
    }
    
    // عرض المنتجات الموجودة
    echo "<h3>3. المنتجات الموجودة:</h3>";
    $allProducts = $database->fetchAll("SELECT * FROM products ORDER BY id DESC");
    foreach ($allProducts as $product) {
        echo "- " . $product['name'] . " (ID: " . $product['id'] . ", الحالة: " . $product['status'] . ")<br>";
    }
    
    // اختبار الاستعلام المستخدم في admin/products.php
    echo "<h3>4. اختبار استعلام admin/products.php:</h3>";
    
    // نسخ نفس المعاملات من admin/products.php
    $search = '';
    $categoryFilter = 0;
    $statusFilter = '';
    $page = 1;
    $itemsPerPage = ADMIN_ITEMS_PER_PAGE;
    $offset = ($page - 1) * $itemsPerPage;
    
    echo "المعاملات:<br>";
    echo "- البحث: '" . $search . "'<br>";
    echo "- فلتر القسم: " . $categoryFilter . "<br>";
    echo "- فلتر الحالة: '" . $statusFilter . "'<br>";
    echo "- الصفحة: " . $page . "<br>";
    echo "- عدد العناصر في الصفحة: " . $itemsPerPage . "<br>";
    echo "- الإزاحة: " . $offset . "<br>";
    
    // بناء الاستعلام
    $whereConditions = [];
    $params = [];
    
    if ($search) {
        $whereConditions[] = "(p.name LIKE :search OR p.description LIKE :search OR p.sku LIKE :search)";
        $params['search'] = '%' . $search . '%';
    }
    
    if ($categoryFilter) {
        $whereConditions[] = "p.category_id = :category";
        $params['category'] = $categoryFilter;
    }
    
    if ($statusFilter) {
        $whereConditions[] = "p.status = :status";
        $params['status'] = $statusFilter;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    $sql = "SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            {$whereClause}
            ORDER BY p.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $params['limit'] = $itemsPerPage;
    $params['offset'] = $offset;
    
    echo "<br><strong>الاستعلام النهائي:</strong><br>";
    echo "<code>" . $sql . "</code><br>";
    
    echo "<br><strong>المعاملات:</strong><br>";
    foreach ($params as $key => $value) {
        echo "- $key: " . (is_null($value) ? 'NULL' : $value) . "<br>";
    }
    
    // تنفيذ الاستعلام
    echo "<h3>5. نتيجة الاستعلام:</h3>";
    try {
        $products = $database->fetchAll($sql, $params);
        echo "✅ الاستعلام نجح<br>";
        echo "عدد المنتجات المسترجعة: " . count($products) . "<br>";
        
        if (count($products) > 0) {
            echo "<br><strong>المنتجات المسترجعة:</strong><br>";
            foreach ($products as $product) {
                echo "- " . $product['name'] . " (القسم: " . ($product['category_name'] ?? 'غير محدد') . ")<br>";
            }
        } else {
            echo "❌ لم يتم استرجاع أي منتجات من الاستعلام<br>";
            
            // اختبار استعلام مبسط
            echo "<br><h4>اختبار استعلام مبسط:</h4>";
            $simpleProducts = $database->fetchAll("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id LIMIT 10");
            echo "عدد المنتجات من الاستعلام المبسط: " . count($simpleProducts) . "<br>";
            
            if (count($simpleProducts) > 0) {
                foreach ($simpleProducts as $product) {
                    echo "- " . $product['name'] . "<br>";
                }
            }
        }
    } catch (Exception $e) {
        echo "❌ خطأ في الاستعلام: " . $e->getMessage() . "<br>";
    }
    
    // اختبار CategoryManager
    echo "<h3>6. اختبار CategoryManager:</h3>";
    try {
        $categories = $categoryManager->getAllCategories();
        echo "✅ CategoryManager يعمل - عدد الأقسام: " . count($categories) . "<br>";
        
        if (count($categories) > 0) {
            foreach ($categories as $category) {
                echo "- " . $category['name'] . " (ID: " . $category['id'] . ")<br>";
            }
        }
    } catch (Exception $e) {
        echo "❌ خطأ في CategoryManager: " . $e->getMessage() . "<br>";
    }
    
    // اختبار الثوابت
    echo "<h3>7. اختبار الثوابت:</h3>";
    echo "ADMIN_ITEMS_PER_PAGE: " . (defined('ADMIN_ITEMS_PER_PAGE') ? ADMIN_ITEMS_PER_PAGE : 'غير معرف') . "<br>";
    echo "ASSETS_PATH: " . (defined('ASSETS_PATH') ? ASSETS_PATH : 'غير معرف') . "<br>";
    echo "UPLOADS_URL: " . (defined('UPLOADS_URL') ? UPLOADS_URL : 'غير معرف') . "<br>";
    
    // اختبار الدوال
    echo "<h3>8. اختبار الدوال:</h3>";
    try {
        $testPrice = formatPrice(99.99);
        echo "✅ formatPrice: " . $testPrice . "<br>";
    } catch (Exception $e) {
        echo "❌ formatPrice: " . $e->getMessage() . "<br>";
    }
    
    try {
        $testDate = formatDate(date('Y-m-d H:i:s'));
        echo "✅ formatDate: " . $testDate . "<br>";
    } catch (Exception $e) {
        echo "❌ formatDate: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>الحلول المقترحة:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    
    if (count($products) == 0 && $totalProducts > 0) {
        echo "<p><strong>المشكلة:</strong> المنتجات موجودة في قاعدة البيانات لكن الاستعلام لا يسترجعها</p>";
        echo "<p><strong>الحلول:</strong></p>";
        echo "<ol>";
        echo "<li>تحقق من أن المنتجات لها أقسام صحيحة</li>";
        echo "<li>تحقق من حالة المنتجات (active/inactive)</li>";
        echo "<li>تحقق من تاريخ created_at للمنتجات</li>";
        echo "</ol>";
        
        // إصلاح تلقائي
        echo "<br><p><strong>إصلاح تلقائي:</strong></p>";
        echo "<a href='?fix=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إصلاح المنتجات تلقائياً</a>";
    }
    
    echo "<br><br><p><strong>روابط الاختبار:</strong></p>";
    echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المنتجات</a>";
    echo "<a href='add-product.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتج</a>";
    echo "</div>";
    
    // إصلاح تلقائي
    if (isset($_GET['fix']) && $_GET['fix'] == '1') {
        echo "<h3>9. إصلاح تلقائي:</h3>";
        
        // تحديث المنتجات بدون تاريخ created_at
        $fixedProducts = $database->query("UPDATE products SET created_at = NOW() WHERE created_at IS NULL OR created_at = '0000-00-00 00:00:00'");
        echo "✅ تم إصلاح تواريخ المنتجات<br>";
        
        // تحديث المنتجات بدون أقسام
        $defaultCategory = $database->fetch("SELECT id FROM categories LIMIT 1");
        if ($defaultCategory) {
            $fixedCategories = $database->query("UPDATE products SET category_id = :cat_id WHERE category_id IS NULL OR category_id = 0", 
                ['cat_id' => $defaultCategory['id']]);
            echo "✅ تم إصلاح أقسام المنتجات<br>";
        }
        
        // تحديث حالة المنتجات
        $fixedStatus = $database->query("UPDATE products SET status = 'active' WHERE status IS NULL OR status = ''");
        echo "✅ تم إصلاح حالة المنتجات<br>";
        
        echo "<br><a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحقق من النتيجة</a>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
