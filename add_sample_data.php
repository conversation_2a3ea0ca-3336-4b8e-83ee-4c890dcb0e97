<?php
/**
 * إضافة بيانات تجريبية
 * Add Sample Data
 */

require_once 'includes/functions.php';

echo "<h2>إضافة بيانات تجريبية</h2>";

try {
    // التحقق من وجود أقسام أولاً
    $existingCategories = $database->fetchAll("SELECT * FROM categories");

    if (empty($existingCategories)) {
        echo "<h3>إضافة الأقسام...</h3>";

        $categories = [
            ['name' => 'الأعشاب الطبيعية', 'description' => 'مجموعة متنوعة من الأعشاب الطبيعية عالية الجودة', 'status' => 'active'],
            ['name' => 'العسل الطبيعي', 'description' => 'أنواع مختلفة من العسل الطبيعي الخالص', 'status' => 'active'],
            ['name' => 'الجينسنج', 'description' => 'منتجات الجينسنج الأصلية والمفيدة للصحة', 'status' => 'active'],
            ['name' => 'الزيوت الطبيعية', 'description' => 'زيوت طبيعية مستخرجة من أفضل المصادر', 'status' => 'active'],
            ['name' => 'المكملات الغذائية', 'description' => 'مكملات غذائية طبيعية لصحة أفضل', 'status' => 'active']
        ];

        $categoryIds = [];
        foreach ($categories as $category) {
            $result = $database->insert('categories', $category);
            if ($result) {
                $categoryId = $database->lastInsertId();
                $categoryIds[] = $categoryId;
                echo "✅ تم إضافة قسم: " . $category['name'] . " (ID: $categoryId)<br>";
            } else {
                echo "❌ فشل في إضافة قسم: " . $category['name'] . "<br>";
            }
        }
    } else {
        echo "<h3>الأقسام موجودة بالفعل...</h3>";
        $categoryIds = array_column($existingCategories, 'id');
        foreach ($existingCategories as $category) {
            echo "✅ قسم موجود: " . $category['name'] . " (ID: " . $category['id'] . ")<br>";
        }
    }
    
    // التحقق من وجود منتجات
    $existingProducts = $database->fetchAll("SELECT * FROM products");

    if (empty($existingProducts)) {
        echo "<br><h3>إضافة المنتجات...</h3>";

        if (empty($categoryIds)) {
            echo "❌ لا توجد أقسام لربط المنتجات بها<br>";
            throw new Exception("يجب إضافة أقسام أولاً");
        }

        $products = [
        // أعشاب طبيعية
        [
            'name' => 'عشبة الجنكة',
            'description' => 'عشبة الجنكة الطبيعية تساعد على تحسين الذاكرة والتركيز. منتج طبيعي 100% بدون إضافات كيميائية.',
            'short_description' => 'عشبة طبيعية لتحسين الذاكرة والتركيز',
            'category_id' => $categoryIds[0],
            'price' => 45.00,
            'sale_price' => 39.99,
            'sku' => 'HERB001',
            'stock_quantity' => 50,
            'weight' => '100 جرام',
            'status' => 'active',
            'featured' => 1,
            'seo_title' => 'عشبة الجنكة الطبيعية - تحسين الذاكرة',
            'seo_description' => 'اشتري عشبة الجنكة الطبيعية عالية الجودة لتحسين الذاكرة والتركيز'
        ],
        [
            'name' => 'البابونج الألماني',
            'description' => 'البابونج الألماني الأصلي معروف بخصائصه المهدئة والمضادة للالتهابات. مثالي للاسترخاء وتهدئة الأعصاب.',
            'short_description' => 'بابونج ألماني أصلي مهدئ ومضاد للالتهابات',
            'category_id' => $categoryIds[0],
            'price' => 25.00,
            'sku' => 'HERB002',
            'stock_quantity' => 75,
            'weight' => '50 جرام',
            'status' => 'active',
            'featured' => 0
        ],
        [
            'name' => 'الكركم الطبيعي',
            'description' => 'كركم طبيعي عالي الجودة غني بالكركمين. له خصائص مضادة للأكسدة والالتهابات ومفيد للصحة العامة.',
            'short_description' => 'كركم طبيعي غني بالكركمين المضاد للأكسدة',
            'category_id' => $categoryIds[0],
            'price' => 30.00,
            'sale_price' => 25.00,
            'sku' => 'HERB003',
            'stock_quantity' => 60,
            'weight' => '200 جرام',
            'status' => 'active',
            'featured' => 1
        ],
        
        // عسل طبيعي
        [
            'name' => 'عسل السدر الجبلي',
            'description' => 'عسل السدر الجبلي الطبيعي من أفضل أنواع العسل. يتميز بطعمه المميز وفوائده الصحية العديدة للجسم.',
            'short_description' => 'عسل سدر جبلي طبيعي عالي الجودة',
            'category_id' => $categoryIds[1],
            'price' => 120.00,
            'sale_price' => 99.99,
            'sku' => 'HONEY001',
            'stock_quantity' => 30,
            'weight' => '500 جرام',
            'status' => 'active',
            'featured' => 1
        ],
        [
            'name' => 'عسل الزهور البرية',
            'description' => 'عسل طبيعي من رحيق الزهور البرية المتنوعة. غني بالفيتامينات والمعادن الطبيعية ومفيد للصحة العامة.',
            'short_description' => 'عسل طبيعي من الزهور البرية المتنوعة',
            'category_id' => $categoryIds[1],
            'price' => 65.00,
            'sku' => 'HONEY002',
            'stock_quantity' => 45,
            'weight' => '250 جرام',
            'status' => 'active',
            'featured' => 0
        ],
        [
            'name' => 'عسل المانوكا',
            'description' => 'عسل المانوكا النيوزيلندي الأصلي معروف بخصائصه العلاجية الفريدة. يحتوي على مضادات حيوية طبيعية قوية.',
            'short_description' => 'عسل مانوكا نيوزيلندي أصلي بخصائص علاجية',
            'category_id' => $categoryIds[1],
            'price' => 180.00,
            'sale_price' => 159.99,
            'sku' => 'HONEY003',
            'stock_quantity' => 20,
            'weight' => '250 جرام',
            'status' => 'active',
            'featured' => 1
        ],
        
        // جينسنج
        [
            'name' => 'جينسنج كوري أحمر',
            'description' => 'جينسنج كوري أحمر أصلي عمره 6 سنوات. معروف بفوائده في تعزيز الطاقة وتقوية جهاز المناعة وتحسين الأداء الذهني.',
            'short_description' => 'جينسنج كوري أحمر أصلي لتعزيز الطاقة',
            'category_id' => $categoryIds[2],
            'price' => 250.00,
            'sale_price' => 220.00,
            'sku' => 'GINSENG001',
            'stock_quantity' => 25,
            'weight' => '100 جرام',
            'status' => 'active',
            'featured' => 1
        ],
        [
            'name' => 'جينسنج أمريكي',
            'description' => 'جينسنج أمريكي طبيعي عالي الجودة. يساعد على تقليل التوتر وتحسين التركيز والذاكرة بطريقة طبيعية.',
            'short_description' => 'جينسنج أمريكي لتقليل التوتر وتحسين التركيز',
            'category_id' => $categoryIds[2],
            'price' => 180.00,
            'sku' => 'GINSENG002',
            'stock_quantity' => 35,
            'weight' => '75 جرام',
            'status' => 'active',
            'featured' => 0
        ],
        
        // زيوت طبيعية
        [
            'name' => 'زيت الزيتون البكر',
            'description' => 'زيت زيتون بكر ممتاز من أفضل أشجار الزيتون. غني بمضادات الأكسدة والأحماض الدهنية المفيدة للصحة.',
            'short_description' => 'زيت زيتون بكر ممتاز غني بمضادات الأكسدة',
            'category_id' => $categoryIds[3],
            'price' => 55.00,
            'sale_price' => 49.99,
            'sku' => 'OIL001',
            'stock_quantity' => 40,
            'size' => '500 مل',
            'status' => 'active',
            'featured' => 1
        ],
        [
            'name' => 'زيت الأرغان المغربي',
            'description' => 'زيت الأرغان المغربي الأصلي المستخرج من ثمار شجرة الأرغان. مفيد للبشرة والشعر ويحتوي على فيتامين E.',
            'short_description' => 'زيت أرغان مغربي أصلي للبشرة والشعر',
            'category_id' => $categoryIds[3],
            'price' => 85.00,
            'sku' => 'OIL002',
            'stock_quantity' => 30,
            'size' => '100 مل',
            'status' => 'active',
            'featured' => 0
        ]
    ];
    
        foreach ($products as $product) {
            $result = $database->insert('products', $product);
            if ($result) {
                $productId = $database->lastInsertId();
                echo "✅ تم إضافة منتج: " . $product['name'] . " (ID: $productId)<br>";
            } else {
                echo "❌ فشل في إضافة منتج: " . $product['name'] . "<br>";
            }
        }

        echo "<br><h3>✅ تم إضافة جميع المنتجات بنجاح!</h3>";
        echo "<p><strong>تم إضافة " . count($products) . " منتجات</strong></p>";

    } else {
        echo "<br><h3>المنتجات موجودة بالفعل...</h3>";
        echo "عدد المنتجات الموجودة: " . count($existingProducts) . "<br>";
        foreach ($existingProducts as $product) {
            echo "✅ منتج موجود: " . $product['name'] . " (ID: " . $product['id'] . ")<br>";
        }
    }

    // إحصائيات نهائية
    $finalCategoriesCount = $database->fetch("SELECT COUNT(*) as count FROM categories")['count'];
    $finalProductsCount = $database->fetch("SELECT COUNT(*) as count FROM products")['count'];

    echo "<br><h3>✅ الإحصائيات النهائية:</h3>";
    echo "<ul>";
    echo "<li><strong>الأقسام:</strong> " . $finalCategoriesCount . "</li>";
    echo "<li><strong>المنتجات:</strong> " . $finalProductsCount . "</li>";
    echo "</ul>";

    echo "<br><h3>اختبار النتائج:</h3>";
    echo "<a href='admin/test_simple.php' style='background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار بسيط</a>";
    echo "<a href='debug_products.php' style='background: #fd7e14; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص شامل</a>";

    echo "<br><br><h3>عرض النتائج:</h3>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "<a href='products.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المنتجات</a>";
    echo "<a href='admin/products.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "<br><strong>تفاصيل الخطأ:</strong><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";

    echo "<br><h3>خطوات الإصلاح:</h3>";
    echo "<ol>";
    echo "<li>تأكد من استيراد ملف database.sql</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li><a href='test_connection.php'>اختبار الاتصال العام</a></li>";
    echo "</ol>";
}
?>
