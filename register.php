<?php
/**
 * صفحة تسجيل العملاء الجدد
 * Customer Registration Page
 */

require_once 'includes/functions.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// التحقق من السماح بالتسجيل
if (!getSiteSetting('allow_registration', 1)) {
    header('Location: login.php?message=' . urlencode('التسجيل غير متاح حالياً'));
    exit;
}

$error = '';
$success = '';

// معالجة التسجيل
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $fullName = cleanInput($_POST['full_name'] ?? '');
    $username = cleanInput($_POST['username'] ?? '');
    $email = cleanInput($_POST['email'] ?? '');
    $phone = cleanInput($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $agreeTerms = isset($_POST['agree_terms']);
    
    $errors = [];
    
    // التحقق من البيانات
    if (empty($fullName)) $errors[] = 'الاسم الكامل مطلوب';
    if (empty($username)) $errors[] = 'اسم المستخدم مطلوب';
    if (strlen($username) < 3) $errors[] = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'البريد الإلكتروني غير صحيح';
    if (empty($password)) $errors[] = 'كلمة المرور مطلوبة';
    if (strlen($password) < 6) $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    if ($password !== $confirmPassword) $errors[] = 'كلمة المرور وتأكيدها غير متطابقتين';
    if (!$agreeTerms) $errors[] = 'يجب الموافقة على الشروط والأحكام';
    
    // التحقق من عدم تكرار البيانات
    if (empty($errors)) {
        $existingUser = $database->fetch("SELECT id FROM users WHERE username = :username OR email = :email", 
            ['username' => $username, 'email' => $email]);
        if ($existingUser) {
            $errors[] = 'اسم المستخدم أو البريد الإلكتروني موجود بالفعل';
        }
    }
    
    if (empty($errors)) {
        // إنشاء الحساب
        $data = [
            'full_name' => $fullName,
            'username' => $username,
            'email' => $email,
            'phone' => $phone,
            'password' => hashPassword($password),
            'role' => 'customer',
            'status' => 'active'
        ];
        
        if ($database->insert('users', $data)) {
            // تسجيل النشاط
            $userId = $database->lastInsertId();
            logActivity('customer_registered', "تسجيل عميل جديد: {$fullName}", $userId, 'customer');
            
            // تسجيل دخول تلقائي
            $_SESSION['user_id'] = $userId;
            $_SESSION['user_name'] = $fullName;
            $_SESSION['user_role'] = 'customer';
            $_SESSION['username'] = $username;
            
            // إعادة التوجيه
            header('Location: index.php?message=' . urlencode('مرحباً بك! تم إنشاء حسابك بنجاح'));
            exit;
        } else {
            $error = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى';
        }
    } else {
        $error = implode('<br>', $errors);
    }
}

$pageTitle = "إنشاء حساب جديد - " . getSiteSetting('site_name');
$pageDescription = "انضم إلينا واستمتع بتجربة تسوق مميزة";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
    
    <style>
    .register-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 2rem 0;
    }
    
    .register-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        max-width: 500px;
        width: 100%;
        margin: 2rem;
    }
    
    .register-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .register-header i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .register-body {
        padding: 2rem;
    }
    
    .form-control {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        font-size: 1rem;
    }
    
    .form-control:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(45, 80, 22, 0.25);
    }
    
    .btn-register {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        border-radius: 10px;
        padding: 12px;
        font-weight: 600;
        color: white;
        width: 100%;
        transition: transform 0.2s;
    }
    
    .btn-register:hover {
        transform: translateY(-2px);
        color: white;
    }
    
    .input-group-text {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-left: none;
    }
    
    .form-control.with-icon {
        border-right: none;
    }
    
    .password-strength {
        height: 5px;
        border-radius: 3px;
        margin-top: 5px;
        transition: all 0.3s;
    }
    
    .strength-weak { background: #dc3545; }
    .strength-medium { background: #ffc107; }
    .strength-strong { background: #28a745; }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="login.php" class="text-white">تسجيل الدخول</a>
                        <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartManager->getCartCount(); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="navbar-nav d-flex flex-row">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link">الرئيسية</a>
                </li>
                <?php 
                $mainCategories = $categoryManager->getMainCategories();
                foreach ($mainCategories as $category): 
                ?>
                <li class="nav-item dropdown">
                    <a href="products.php?category=<?php echo $category['id']; ?>" class="nav-link">
                        <?php echo $category['name']; ?>
                    </a>
                </li>
                <?php endforeach; ?>
                <li class="nav-item">
                    <a href="contact.php" class="nav-link">اتصل بنا</a>
                </li>
                <li class="nav-item">
                    <a href="about.php" class="nav-link">من نحن</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">إنشاء حساب جديد</li>
            </ol>
        </nav>
    </div>

    <!-- Register Section -->
    <section class="register-container">
        <div class="register-card">
            <!-- Register Header -->
            <div class="register-header">
                <i class="fas fa-user-plus"></i>
                <h2>إنشاء حساب جديد</h2>
                <p class="mb-0">انضم إلى عائلتنا الكبيرة</p>
            </div>
            
            <!-- Register Body -->
            <div class="register-body">
                <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
                </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                </div>
                <?php endif; ?>
                
                <form method="POST" action="register.php" id="registerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control with-icon" id="full_name" name="full_name" 
                                       value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>" 
                                       required autofocus>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-at"></i>
                                </span>
                                <input type="text" class="form-control with-icon" id="username" name="username" 
                                       value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                                       required minlength="3">
                            </div>
                            <div class="form-text">3 أحرف على الأقل</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control with-icon" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control with-icon" id="phone" name="phone" 
                                       value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control with-icon" id="password" name="password" 
                                       required minlength="6" onkeyup="checkPasswordStrength()">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="passwordStrength"></div>
                            <div class="form-text">6 أحرف على الأقل</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control with-icon" id="confirm_password" name="confirm_password" 
                                       required onkeyup="checkPasswordMatch()">
                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword('confirm_password')">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </button>
                            </div>
                            <div id="passwordMatch" class="form-text"></div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                            <label class="form-check-label" for="agree_terms">
                                أوافق على <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">الشروط والأحكام</a>
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-register">
                        <i class="fas fa-user-plus"></i> إنشاء الحساب
                    </button>
                </form>
                
                <div class="text-center mt-4">
                    <p class="mb-2">لديك حساب بالفعل؟</p>
                    <a href="login.php" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Terms Modal -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">الشروط والأحكام</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. قبول الشروط</h6>
                    <p>باستخدام هذا الموقع، فإنك توافق على الالتزام بهذه الشروط والأحكام.</p>
                    
                    <h6>2. استخدام الموقع</h6>
                    <p>يجب استخدام الموقع للأغراض القانونية فقط وبطريقة لا تنتهك حقوق الآخرين.</p>
                    
                    <h6>3. حماية البيانات</h6>
                    <p>نحن ملتزمون بحماية خصوصيتك وبياناتك الشخصية وفقاً لسياسة الخصوصية.</p>
                    
                    <h6>4. المسؤولية</h6>
                    <p>الموقع غير مسؤول عن أي أضرار قد تنتج عن استخدام الموقع أو المنتجات.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="acceptTerms()">أوافق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>عن المتجر</h3>
                    <p>متجر متخصص في بيع أفضل المنتجات الطبيعية من الأعشاب والعسل والجينسنج بأعلى معايير الجودة.</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                        <li><a href="products.php">المنتجات</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <ul>
                        <li><i class="fas fa-map-marker-alt"></i> <?php echo getSiteSetting('contact_address'); ?></li>
                        <li><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></li>
                        <li><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>تابعنا</h3>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-whatsapp fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> جميع الحقوق محفوظة - متجر المنتجات الطبيعية</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
    
    <script>
    function togglePassword(fieldId) {
        const passwordField = document.getElementById(fieldId);
        const toggleIcon = fieldId === 'password' ? document.getElementById('toggleIcon1') : document.getElementById('toggleIcon2');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }
    
    function checkPasswordStrength() {
        const password = document.getElementById('password').value;
        const strengthBar = document.getElementById('passwordStrength');
        
        let strength = 0;
        if (password.length >= 6) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        strengthBar.className = 'password-strength';
        if (strength < 2) {
            strengthBar.classList.add('strength-weak');
        } else if (strength < 4) {
            strengthBar.classList.add('strength-medium');
        } else {
            strengthBar.classList.add('strength-strong');
        }
    }
    
    function checkPasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const matchDiv = document.getElementById('passwordMatch');
        
        if (confirmPassword === '') {
            matchDiv.textContent = '';
            return;
        }
        
        if (password === confirmPassword) {
            matchDiv.textContent = '✓ كلمة المرور متطابقة';
            matchDiv.style.color = '#28a745';
        } else {
            matchDiv.textContent = '✗ كلمة المرور غير متطابقة';
            matchDiv.style.color = '#dc3545';
        }
    }
    
    function acceptTerms() {
        document.getElementById('agree_terms').checked = true;
        const modal = bootstrap.Modal.getInstance(document.getElementById('termsModal'));
        modal.hide();
    }
    
    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (password !== confirmPassword) {
            e.preventDefault();
            alert('كلمة المرور وتأكيدها غير متطابقتين');
            return;
        }
        
        if (!document.getElementById('agree_terms').checked) {
            e.preventDefault();
            alert('يجب الموافقة على الشروط والأحكام');
            return;
        }
    });
    </script>
</body>
</html>
