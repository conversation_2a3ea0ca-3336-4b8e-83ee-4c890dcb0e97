<?php
/**
 * صفحة إتمام الطلب
 * Checkout Page
 */

require_once 'includes/functions.php';

// التحقق من وجود عناصر في السلة
$cartItems = $cartManager->getCartItems();
$cartTotal = $cartManager->getCartTotal();

if (empty($cartItems)) {
    header('Location: cart.php');
    exit;
}

$errors = [];
$success = false;

// معالجة إرسال الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات
    $customerName = cleanInput($_POST['customer_name'] ?? '');
    $customerEmail = cleanInput($_POST['customer_email'] ?? '');
    $customerPhone = cleanInput($_POST['customer_phone'] ?? '');
    $customerAddress = cleanInput($_POST['customer_address'] ?? '');
    $paymentMethod = cleanInput($_POST['payment_method'] ?? 'cash_on_delivery');
    $notes = cleanInput($_POST['notes'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($customerName)) {
        $errors[] = 'الاسم الكامل مطلوب';
    }
    
    if (empty($customerEmail) || !filter_var($customerEmail, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($customerPhone)) {
        $errors[] = 'رقم الهاتف مطلوب';
    }
    
    if (empty($customerAddress)) {
        $errors[] = 'العنوان مطلوب';
    }
    
    // إذا لم توجد أخطاء، إنشاء الطلب
    if (empty($errors)) {
        $orderData = [
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_phone' => $customerPhone,
            'customer_address' => $customerAddress,
            'total_amount' => $cartTotal * 1.15, // مع الضريبة
            'payment_method' => $paymentMethod,
            'notes' => $notes
        ];
        
        // تحضير عناصر الطلب
        $orderItems = [];
        foreach ($cartItems as $item) {
            $orderItems[] = [
                'product_id' => $item['id'],
                'product_name' => $item['name'],
                'product_price' => $item['sale_price'] ?: $item['price'],
                'quantity' => $item['cart_quantity']
            ];
        }
        
        // إنشاء الطلب
        $orderId = $orderManager->createOrder($orderData, $orderItems);
        
        if ($orderId) {
            // مسح السلة
            $cartManager->clearCart();
            
            // تسجيل النشاط
            logActivity('order_placed', "تم إنشاء طلب جديد رقم: {$orderId}");
            
            // إعادة التوجيه لصفحة تأكيد الطلب
            header('Location: order-success.php?order=' . $orderId);
            exit;
        } else {
            $errors[] = 'حدث خطأ أثناء إنشاء الطلب. يرجى المحاولة مرة أخرى.';
        }
    }
}

$pageTitle = "إتمام الطلب - " . getSiteSetting('site_name');
$pageDescription = "أكمل بياناتك لإتمام عملية الشراء";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo count($cartItems); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="cart.php">سلة التسوق</a></li>
                <li class="breadcrumb-item active">إتمام الطلب</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <h1 class="page-title">إتمام الطلب</h1>
            
            <!-- Checkout Steps -->
            <div class="checkout-steps mb-4">
                <div class="row">
                    <div class="col-md-4">
                        <div class="step completed">
                            <i class="fas fa-shopping-cart"></i>
                            <span>السلة</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="step active">
                            <i class="fas fa-user"></i>
                            <span>بيانات العميل</span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="step">
                            <i class="fas fa-check"></i>
                            <span>تأكيد الطلب</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Messages -->
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <form method="POST" action="checkout.php" class="checkout-form">
                <div class="row">
                    <!-- Customer Information -->
                    <div class="col-lg-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-user"></i> بيانات العميل</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                               value="<?php echo isset($_POST['customer_name']) ? htmlspecialchars($_POST['customer_name']) : ''; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="customer_email" name="customer_email" 
                                               value="<?php echo isset($_POST['customer_email']) ? htmlspecialchars($_POST['customer_email']) : ''; ?>" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_phone" class="form-label">رقم الهاتف *</label>
                                        <input type="tel" class="form-control" id="customer_phone" name="customer_phone" 
                                               value="<?php echo isset($_POST['customer_phone']) ? htmlspecialchars($_POST['customer_phone']) : ''; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="customer_address" class="form-label">العنوان *</label>
                                        <textarea class="form-control" id="customer_address" name="customer_address" rows="3" required><?php echo isset($_POST['customer_address']) ? htmlspecialchars($_POST['customer_address']) : ''; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-credit-card"></i> طريقة الدفع</h5>
                            </div>
                            <div class="card-body">
                                <div class="payment-methods">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="cash_on_delivery" value="cash_on_delivery" checked>
                                        <label class="form-check-label" for="cash_on_delivery">
                                            <i class="fas fa-money-bill-wave"></i> الدفع عند الاستلام
                                            <small class="text-muted d-block">ادفع نقداً عند وصول الطلب</small>
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="payment_method" id="bank_transfer" value="bank_transfer">
                                        <label class="form-check-label" for="bank_transfer">
                                            <i class="fas fa-university"></i> تحويل بنكي
                                            <small class="text-muted d-block">تحويل إلى حساب البنك</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Notes -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-sticky-note"></i> ملاحظات الطلب</h5>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" name="notes" rows="3" placeholder="أي ملاحظات إضافية حول طلبك..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-receipt"></i> ملخص الطلب</h5>
                            </div>
                            <div class="card-body">
                                <!-- Order Items -->
                                <div class="order-items mb-3">
                                    <?php foreach ($cartItems as $item): ?>
                                    <div class="order-item d-flex justify-content-between align-items-center mb-2">
                                        <div class="item-info">
                                            <h6 class="mb-0"><?php echo $item['name']; ?></h6>
                                            <small class="text-muted">الكمية: <?php echo $item['cart_quantity']; ?></small>
                                        </div>
                                        <div class="item-price">
                                            <strong><?php echo formatPrice($item['cart_total']); ?></strong>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <hr>
                                
                                <!-- Order Totals -->
                                <div class="order-totals">
                                    <div class="total-row d-flex justify-content-between">
                                        <span>المجموع الفرعي:</span>
                                        <span><?php echo formatPrice($cartTotal); ?></span>
                                    </div>
                                    <div class="total-row d-flex justify-content-between">
                                        <span>الشحن:</span>
                                        <span>مجاني</span>
                                    </div>
                                    <div class="total-row d-flex justify-content-between">
                                        <span>الضريبة (15%):</span>
                                        <span><?php echo formatPrice($cartTotal * 0.15); ?></span>
                                    </div>
                                    <hr>
                                    <div class="total-row d-flex justify-content-between">
                                        <strong>المجموع الكلي:</strong>
                                        <strong><?php echo formatPrice($cartTotal * 1.15); ?></strong>
                                    </div>
                                </div>
                                
                                <!-- Place Order Button -->
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg w-100" onclick="return confirmOrder()">
                                        <i class="fas fa-check"></i> تأكيد الطلب
                                    </button>
                                </div>
                                
                                <!-- Security Notice -->
                                <div class="security-notice mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-lock"></i> معلوماتك محمية وآمنة
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
    
    <script>
    // التحقق من صحة النموذج
    document.querySelector('.checkout-form').addEventListener('submit', function(e) {
        const name = document.getElementById('customer_name').value.trim();
        const email = document.getElementById('customer_email').value.trim();
        const phone = document.getElementById('customer_phone').value.trim();
        const address = document.getElementById('customer_address').value.trim();
        
        if (!name || !email || !phone || !address) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        if (!validateEmail(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }
        
        return confirmOrder();
    });
    </script>
</body>
</html>
