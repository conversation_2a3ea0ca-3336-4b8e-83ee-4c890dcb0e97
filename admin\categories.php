<?php
/**
 * إدارة الأقسام
 * Categories Management
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = cleanInput($_POST['name'] ?? '');
                $description = cleanInput($_POST['description'] ?? '');
                $parentId = !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null;
                $sortOrder = intval($_POST['sort_order'] ?? 0);
                $seoTitle = cleanInput($_POST['seo_title'] ?? '');
                $seoDescription = cleanInput($_POST['seo_description'] ?? '');
                $seoKeywords = cleanInput($_POST['seo_keywords'] ?? '');
                
                if (empty($name)) {
                    $message = 'اسم القسم مطلوب';
                    $messageType = 'danger';
                } else {
                    $data = [
                        'name' => $name,
                        'description' => $description,
                        'parent_id' => $parentId,
                        'sort_order' => $sortOrder,
                        'seo_title' => $seoTitle,
                        'seo_description' => $seoDescription,
                        'seo_keywords' => $seoKeywords
                    ];
                    
                    if ($database->insert('categories', $data)) {
                        $message = 'تم إضافة القسم بنجاح';
                        $messageType = 'success';
                        logActivity('category_added', "تم إضافة قسم جديد: {$name}");
                    } else {
                        $message = 'حدث خطأ أثناء إضافة القسم';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'delete':
                $categoryId = intval($_POST['category_id']);
                // التحقق من وجود منتجات في هذا القسم
                $productCount = $database->count('products', 'category_id = :id', ['id' => $categoryId]);
                if ($productCount > 0) {
                    $message = 'لا يمكن حذف القسم لأنه يحتوي على منتجات';
                    $messageType = 'warning';
                } else {
                    if ($database->delete('categories', 'id = :id', ['id' => $categoryId])) {
                        $message = 'تم حذف القسم بنجاح';
                        $messageType = 'success';
                        logActivity('category_deleted', "تم حذف قسم رقم: {$categoryId}");
                    } else {
                        $message = 'حدث خطأ أثناء حذف القسم';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'toggle_status':
                $categoryId = intval($_POST['category_id']);
                $newStatus = $_POST['status'] === 'active' ? 'inactive' : 'active';
                if ($database->update('categories', ['status' => $newStatus], 'id = :id', ['id' => $categoryId])) {
                    $message = 'تم تحديث حالة القسم بنجاح';
                    $messageType = 'success';
                    logActivity('category_status_updated', "تم تحديث حالة قسم رقم {$categoryId} إلى {$newStatus}");
                } else {
                    $message = 'حدث خطأ أثناء تحديث حالة القسم';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// الحصول على الأقسام
$categories = $database->fetchAll("
    SELECT c.*, 
           COUNT(p.id) as products_count,
           parent.name as parent_name
    FROM categories c 
    LEFT JOIN products p ON c.id = p.category_id 
    LEFT JOIN categories parent ON c.parent_id = parent.id
    GROUP BY c.id 
    ORDER BY c.sort_order, c.name
");

// الحصول على الأقسام الرئيسية للقائمة المنسدلة
$mainCategories = $database->fetchAll("SELECT * FROM categories WHERE parent_id IS NULL ORDER BY sort_order, name");

$pageTitle = "إدارة الأقسام - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php" class="active"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إدارة الأقسام</h1>
                <div class="header-actions">
                    <button type="button" class="btn-admin btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus"></i> إضافة قسم جديد
                    </button>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Categories Table -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            الأقسام (<?php echo count($categories); ?>)
                        </h5>
                        <div class="card-actions">
                            <button type="button" class="btn-admin btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus"></i> إضافة قسم
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($categories)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>اسم القسم</th>
                                        <th>القسم الرئيسي</th>
                                        <th>عدد المنتجات</th>
                                        <th>ترتيب العرض</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($categories as $category): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $category['name']; ?></strong>
                                            <?php if ($category['description']): ?>
                                            <br><small class="text-muted"><?php echo truncateText($category['description'], 50); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $category['parent_name'] ?: 'قسم رئيسي'; ?>
                                        </td>
                                        <td>
                                            <span class="badge badge-info"><?php echo $category['products_count']; ?></span>
                                        </td>
                                        <td><?php echo $category['sort_order']; ?></td>
                                        <td>
                                            <?php
                                            $statusClass = $category['status'] === 'active' ? 'success' : 'danger';
                                            $statusText = $category['status'] === 'active' ? 'نشط' : 'غير نشط';
                                            ?>
                                            <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td><?php echo formatDate($category['created_at'], 'd/m/Y'); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button type="button" class="btn-admin btn-info btn-sm" 
                                                        onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)" 
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل تريد تغيير حالة هذا القسم؟')">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                    <input type="hidden" name="status" value="<?php echo $category['status']; ?>">
                                                    <button type="submit" class="btn-admin btn-warning btn-sm" title="تغيير الحالة">
                                                        <i class="fas fa-toggle-on"></i>
                                                    </button>
                                                </form>
                                                
                                                <?php if ($category['products_count'] == 0): ?>
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا القسم؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                    <button type="submit" class="btn-admin btn-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <!-- No Categories -->
                        <div class="text-center py-5">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h5>لا توجد أقسام</h5>
                            <p class="text-muted">لم يتم إضافة أي أقسام بعد</p>
                            <button type="button" class="btn-admin btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                                <i class="fas fa-plus"></i> إضافة قسم جديد
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة قسم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="categories.php">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم القسم *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="parent_id" class="form-label">القسم الرئيسي</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">قسم رئيسي</option>
                                    <?php foreach ($mainCategories as $mainCat): ?>
                                    <option value="<?php echo $mainCat['id']; ?>"><?php echo $mainCat['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                        </div>
                        
                        <hr>
                        <h6>إعدادات SEO</h6>
                        
                        <div class="mb-3">
                            <label for="seo_title" class="form-label">عنوان SEO</label>
                            <input type="text" class="form-control" id="seo_title" name="seo_title">
                        </div>
                        
                        <div class="mb-3">
                            <label for="seo_description" class="form-label">وصف SEO</label>
                            <textarea class="form-control" id="seo_description" name="seo_description" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="seo_keywords" class="form-label">كلمات مفتاحية</label>
                            <input type="text" class="form-control" id="seo_keywords" name="seo_keywords" placeholder="كلمة1, كلمة2, كلمة3">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة القسم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل القسم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="categories.php" id="editCategoryForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="category_id" id="edit_category_id">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_name" class="form-label">اسم القسم *</label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_parent_id" class="form-label">القسم الرئيسي</label>
                                <select class="form-select" id="edit_parent_id" name="parent_id">
                                    <option value="">قسم رئيسي</option>
                                    <?php foreach ($mainCategories as $mainCat): ?>
                                    <option value="<?php echo $mainCat['id']; ?>"><?php echo $mainCat['name']; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_sort_order" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="edit_sort_order" name="sort_order">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function editCategory(category) {
        document.getElementById('edit_category_id').value = category.id;
        document.getElementById('edit_name').value = category.name;
        document.getElementById('edit_description').value = category.description || '';
        document.getElementById('edit_sort_order').value = category.sort_order;
        document.getElementById('edit_parent_id').value = category.parent_id || '';
        
        // تغيير action النموذج للتعديل
        document.getElementById('editCategoryForm').querySelector('input[name="action"]').value = 'edit';
        
        // عرض النافذة المنبثقة
        new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
    }
    </script>
</body>
</html>
