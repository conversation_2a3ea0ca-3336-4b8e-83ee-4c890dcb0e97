<?php
/**
 * سجل الأنشطة
 * Activity Logs
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$actionFilter = isset($_GET['action']) ? cleanInput($_GET['action']) : '';
$userTypeFilter = isset($_GET['user_type']) ? cleanInput($_GET['user_type']) : '';
$dateFrom = isset($_GET['date_from']) ? cleanInput($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? cleanInput($_GET['date_to']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(al.action LIKE :search OR al.description LIKE :search OR u.full_name LIKE :search)";
    $params['search'] = '%' . $search . '%';
}

if ($actionFilter) {
    $whereConditions[] = "al.action = :action";
    $params['action'] = $actionFilter;
}

if ($userTypeFilter) {
    $whereConditions[] = "al.user_type = :user_type";
    $params['user_type'] = $userTypeFilter;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(al.created_at) >= :date_from";
    $params['date_from'] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(al.created_at) <= :date_to";
    $params['date_to'] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// التحقق من وجود جدول activity_logs
$activities = [];
$totalActivities = 0;
$totalPages = 0;

try {
    // التحقق من وجود جدول users
    $usersTableExists = false;
    try {
        $database->fetch("SELECT 1 FROM users LIMIT 1");
        $usersTableExists = true;
    } catch (Exception $e) {
        $usersTableExists = false;
    }

    // بناء الاستعلام حسب وجود جدول users
    if ($usersTableExists) {
        $sql = "SELECT al.*, u.full_name, u.username
                FROM activity_logs al
                LEFT JOIN users u ON al.user_id = u.id
                {$whereClause}
                ORDER BY al.created_at DESC
                LIMIT :limit OFFSET :offset";

        $countSql = "SELECT COUNT(*) as total FROM activity_logs al LEFT JOIN users u ON al.user_id = u.id {$whereClause}";
    } else {
        $sql = "SELECT al.*,
                CASE
                    WHEN al.user_type = 'admin' THEN 'مدير النظام'
                    ELSE CONCAT('مستخدم ', al.user_id)
                END as full_name,
                CASE
                    WHEN al.user_type = 'admin' THEN 'admin'
                    ELSE CONCAT('user_', al.user_id)
                END as username
                FROM activity_logs al
                {$whereClause}
                ORDER BY al.created_at DESC
                LIMIT :limit OFFSET :offset";

        $countSql = "SELECT COUNT(*) as total FROM activity_logs al {$whereClause}";
    }

    $params['limit'] = $itemsPerPage;
    $params['offset'] = $offset;

    $activities = $database->fetchAll($sql, $params);

    // حساب إجمالي الأنشطة
    $countParams = array_diff_key($params, ['limit' => '', 'offset' => '']);
    $totalResult = $database->fetch($countSql, $countParams);
    $totalActivities = $totalResult['total'];
    $totalPages = ceil($totalActivities / $itemsPerPage);

} catch (Exception $e) {
    // في حالة عدم وجود جدول activity_logs، أنشئ بيانات تجريبية
    $activities = [
        [
            'id' => 1,
            'user_id' => 1,
            'user_type' => 'admin',
            'action' => 'login',
            'description' => 'تسجيل دخول المدير',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'created_at' => date('Y-m-d H:i:s'),
            'full_name' => 'مدير النظام',
            'username' => 'admin'
        ],
        [
            'id' => 2,
            'user_id' => 1,
            'user_type' => 'admin',
            'action' => 'view_activity_logs',
            'description' => 'عرض سجل الأنشطة',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'created_at' => date('Y-m-d H:i:s', strtotime('-5 minutes')),
            'full_name' => 'مدير النظام',
            'username' => 'admin'
        ]
    ];
    $totalActivities = count($activities);
    $totalPages = 1;
}

// الحصول على الأنشطة المختلفة للفلترة
$actions = [];
try {
    $actions = $database->fetchAll("SELECT DISTINCT action FROM activity_logs ORDER BY action");
} catch (Exception $e) {
    // في حالة عدم وجود الجدول، استخدم قائمة افتراضية
    $actions = [
        ['action' => 'login'],
        ['action' => 'logout'],
        ['action' => 'order_created'],
        ['action' => 'order_status_updated'],
        ['action' => 'product_added'],
        ['action' => 'product_updated'],
        ['action' => 'view_activity_logs']
    ];
}

$pageTitle = "سجل الأنشطة - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php" class="active"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">سجل الأنشطة</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Warning for demo data -->
                <?php if ($totalActivities <= 2 && !empty($activities) && $activities[0]['action'] == 'login'): ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> بيانات تجريبية</h5>
                    <p>لا يوجد جدول سجل الأنشطة أو أنه فارغ. البيانات المعروضة تجريبية. لإنشاء جدول سجل الأنشطة:</p>
                    <div class="mt-3">
                        <a href="update_inventory_database.php" class="btn btn-primary me-2">
                            <i class="fas fa-database"></i> إنشاء جدول سجل الأنشطة
                        </a>
                        <a href="test-orders.php" class="btn btn-success">
                            <i class="fas fa-play"></i> اختبار النظام لإنشاء أنشطة
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">البحث والتصفية</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="activity-logs.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="النشاط، الوصف، اسم المستخدم..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="action" class="form-label">النشاط</label>
                                <select class="form-select" id="action" name="action">
                                    <option value="">جميع الأنشطة</option>
                                    <?php foreach ($actions as $action): ?>
                                    <option value="<?php echo $action['action']; ?>" <?php echo ($actionFilter === $action['action']) ? 'selected' : ''; ?>>
                                        <?php echo $action['action']; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="user_type" class="form-label">نوع المستخدم</label>
                                <select class="form-select" id="user_type" name="user_type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="admin" <?php echo ($userTypeFilter === 'admin') ? 'selected' : ''; ?>>مدير</option>
                                    <option value="customer" <?php echo ($userTypeFilter === 'customer') ? 'selected' : ''; ?>>عميل</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn-admin btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Activity Logs Table -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            سجل الأنشطة (<?php echo number_format($totalActivities); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($activities)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>النشاط</th>
                                        <th>الوصف</th>
                                        <th>عنوان IP</th>
                                        <th>التاريخ والوقت</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($activities as $activity): ?>
                                    <tr>
                                        <td>
                                            <?php if ($activity['full_name']): ?>
                                            <div>
                                                <strong><?php echo $activity['full_name']; ?></strong>
                                                <br><small class="text-muted">@<?php echo $activity['username']; ?></small>
                                            </div>
                                            <?php else: ?>
                                            <span class="text-muted">مستخدم محذوف</span>
                                            <?php endif; ?>
                                            <br>
                                            <?php
                                            $typeClass = $activity['user_type'] === 'admin' ? 'danger' : 'primary';
                                            $typeText = $activity['user_type'] === 'admin' ? 'مدير' : 'عميل';
                                            ?>
                                            <span class="badge badge-<?php echo $typeClass; ?> badge-sm"><?php echo $typeText; ?></span>
                                        </td>
                                        <td>
                                            <code><?php echo $activity['action']; ?></code>
                                        </td>
                                        <td>
                                            <?php echo $activity['description'] ?: '-'; ?>
                                        </td>
                                        <td>
                                            <small class="text-muted"><?php echo $activity['ip_address']; ?></small>
                                        </td>
                                        <td>
                                            <?php echo formatDate($activity['created_at'], 'd/m/Y H:i:s'); ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات سجل الأنشطة" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <!-- No Activities -->
                        <div class="text-center py-5">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h5>لا توجد أنشطة</h5>
                            <p class="text-muted">
                                <?php if ($search || $actionFilter || $userTypeFilter || $dateFrom || $dateTo): ?>
                                    لم نجد أي أنشطة تطابق معايير البحث المحددة
                                <?php else: ?>
                                    لم يتم تسجيل أي أنشطة بعد
                                <?php endif; ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
