<?php
/**
 * الإعدادات العامة للموقع
 * General Site Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/database.php';

// الإعدادات العامة
define('SITE_URL', 'http://localhost/g8');
define('SITE_PATH', __DIR__ . '/..');
define('ADMIN_PATH', SITE_PATH . '/admin');
define('ASSETS_PATH', SITE_URL . '/assets');
define('UPLOADS_PATH', SITE_PATH . '/uploads');
define('UPLOADS_URL', SITE_URL . '/uploads');

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات الصفحات
define('ITEMS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// إعدادات الملفات المرفوعة
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'متجر المنتجات الطبيعية');

// المناطق الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات الأخطاء
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// دالة getSiteSetting موجودة في includes/functions.php

// دالة لتحديث إعدادات الموقع
function updateSiteSetting($key, $value) {
    global $database;
    
    $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (:key, :value) 
            ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()";
    
    return $database->query($sql, ['key' => $key, 'value' => $value]);
}

// الدوال التالية موجودة في includes/functions.php:
// isLoggedIn(), isAdmin(), cleanInput()

// دالة لتوليد رمز عشوائي
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// الدوال التالية موجودة في includes/functions.php:
// hashPassword(), verifyPassword(), logActivity()

// دالة لإعادة التوجيه
function redirect($url) {
    header('Location: ' . $url);
    exit();
}

// دالة لعرض الرسائل
function setMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// دالة لتنسيق السعر
function formatPrice($price) {
    return number_format($price, 2) . ' ريال';
}

// الدوال التالية موجودة في includes/functions.php:
// formatDate(), generateOrderNumber()

// دالة لقطع النص
function truncateText($text, $length = 100) {
    if (strlen($text) > $length) {
        return substr($text, 0, $length) . '...';
    }
    return $text;
}

// إنشاء مجلدات الرفع إذا لم تكن موجودة
if (!file_exists(UPLOADS_PATH)) {
    mkdir(UPLOADS_PATH, 0755, true);
}

if (!file_exists(UPLOADS_PATH . '/products')) {
    mkdir(UPLOADS_PATH . '/products', 0755, true);
}

if (!file_exists(UPLOADS_PATH . '/categories')) {
    mkdir(UPLOADS_PATH . '/categories', 0755, true);
}
?>
