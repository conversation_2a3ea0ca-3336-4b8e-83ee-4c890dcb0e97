<?php
/**
 * الإعدادات العامة للموقع
 * General Site Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/database.php';

// الإعدادات العامة
define('SITE_URL', 'http://localhost/g8');
define('SITE_PATH', __DIR__ . '/..');
define('ADMIN_PATH', SITE_PATH . '/admin');
define('ASSETS_PATH', SITE_URL . '/assets');
define('UPLOADS_PATH', SITE_PATH . '/uploads');
define('UPLOADS_URL', SITE_URL . '/uploads');

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة

// إعدادات الصفحات
define('ITEMS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// إعدادات الملفات المرفوعة
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'متجر المنتجات الطبيعية');

// المناطق الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات الأخطاء
if (defined('DEBUG') && DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// دالة للحصول على إعدادات الموقع
function getSiteSetting($key, $default = '') {
    global $database;
    
    $sql = "SELECT setting_value FROM site_settings WHERE setting_key = :key";
    $result = $database->fetch($sql, ['key' => $key]);
    
    return $result ? $result['setting_value'] : $default;
}

// دالة لتحديث إعدادات الموقع
function updateSiteSetting($key, $value) {
    global $database;
    
    $sql = "INSERT INTO site_settings (setting_key, setting_value) VALUES (:key, :value) 
            ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()";
    
    return $database->query($sql, ['key' => $key, 'value' => $value]);
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة للتحقق من صلاحيات الإدارة
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// دالة تسجيل الخروج موجودة في includes/functions.php

// دالة لتنظيف البيانات
function cleanInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// دالة لتوليد رمز عشوائي
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة تسجيل النشاط موجودة في includes/functions.php

// دالة لإعادة التوجيه
function redirect($url) {
    header('Location: ' . $url);
    exit();
}

// دالة لعرض الرسائل
function setMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// دالة لتنسيق السعر
function formatPrice($price) {
    return number_format($price, 2) . ' ريال';
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

// دالة لقطع النص
function truncateText($text, $length = 100) {
    if (strlen($text) > $length) {
        return substr($text, 0, $length) . '...';
    }
    return $text;
}

// دالة لتوليد رقم الطلب
function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

// إنشاء مجلدات الرفع إذا لم تكن موجودة
if (!file_exists(UPLOADS_PATH)) {
    mkdir(UPLOADS_PATH, 0755, true);
}

if (!file_exists(UPLOADS_PATH . '/products')) {
    mkdir(UPLOADS_PATH . '/products', 0755, true);
}

if (!file_exists(UPLOADS_PATH . '/categories')) {
    mkdir(UPLOADS_PATH . '/categories', 0755, true);
}
?>
