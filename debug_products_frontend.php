<?php
/**
 * تشخيص مشكلة عدم ظهور المنتجات في الواجهة الأمامية
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/functions.php';

echo "<h2>تشخيص مشكلة المنتجات في الواجهة الأمامية</h2>";

// نسخ نفس المنطق من products.php
$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;
$searchQuery = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = defined('ITEMS_PER_PAGE') ? ITEMS_PER_PAGE : 12;
$offset = ($page - 1) * $itemsPerPage;

echo "<h3>1. معاملات الصفحة:</h3>";
echo "<ul>";
echo "<li><strong>categoryId:</strong> " . ($categoryId ?: 'غير محدد') . "</li>";
echo "<li><strong>searchQuery:</strong> '" . htmlspecialchars($searchQuery) . "'</li>";
echo "<li><strong>page:</strong> $page</li>";
echo "<li><strong>itemsPerPage:</strong> $itemsPerPage</li>";
echo "<li><strong>offset:</strong> $offset</li>";
echo "</ul>";

// الحصول على المنتجات
$products = [];
$totalProducts = 0;

echo "<h3>2. اختبار استرجاع المنتجات:</h3>";

try {
    if ($searchQuery) {
        echo "<h4>البحث عن: '$searchQuery'</h4>";
        $products = $productManager->searchProducts($searchQuery, $itemsPerPage, $offset);
        $totalProducts = count($productManager->searchProducts($searchQuery));
        echo "عدد المنتجات من البحث: " . count($products) . "<br>";
        echo "إجمالي نتائج البحث: $totalProducts<br>";
        
    } elseif ($categoryId) {
        echo "<h4>منتجات القسم: $categoryId</h4>";
        $category = $categoryManager->getCategoryById($categoryId);
        if ($category) {
            echo "اسم القسم: " . $category['name'] . "<br>";
            $products = $productManager->getProductsByCategory($categoryId, $itemsPerPage, $offset);
            $totalProducts = $productManager->getProductsCount($categoryId);
            echo "عدد المنتجات من القسم: " . count($products) . "<br>";
            echo "إجمالي منتجات القسم: $totalProducts<br>";
        } else {
            echo "❌ القسم غير موجود<br>";
        }
        
    } else {
        echo "<h4>جميع المنتجات:</h4>";
        $products = $productManager->getAllProducts($page, $itemsPerPage, 'active');
        $totalProducts = $productManager->getProductsCount();
        echo "عدد المنتجات المسترجعة: " . count($products) . "<br>";
        echo "إجمالي المنتجات: $totalProducts<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في استرجاع المنتجات: " . $e->getMessage() . "<br>";
}

// اختبار مباشر للاستعلامات
echo "<h3>3. اختبار الاستعلامات المباشرة:</h3>";

// اختبار getAllProducts مباشرة
echo "<h4>اختبار getAllProducts:</h4>";
try {
    $directProducts = $database->fetchAll("SELECT p.*, c.name as category_name 
                                          FROM products p 
                                          LEFT JOIN categories c ON p.category_id = c.id 
                                          WHERE p.status = 'active' 
                                          ORDER BY p.created_at DESC 
                                          LIMIT 10");
    echo "عدد المنتجات من الاستعلام المباشر: " . count($directProducts) . "<br>";
    
    if (count($directProducts) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>الاسم</th><th>القسم</th><th>الحالة</th></tr>";
        foreach (array_slice($directProducts, 0, 5) as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . $product['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الاستعلام المباشر: " . $e->getMessage() . "<br>";
}

// اختبار دالة getAllProducts في ProductManager
echo "<h4>اختبار دالة getAllProducts في ProductManager:</h4>";
try {
    $managerProducts = $productManager->getAllProducts(1, 10, 'active');
    echo "عدد المنتجات من ProductManager: " . count($managerProducts) . "<br>";
    
    if (count($managerProducts) > 0) {
        echo "أول منتج: " . $managerProducts[0]['name'] . "<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في ProductManager: " . $e->getMessage() . "<br>";
}

// فحص مشكلة LIMIT/OFFSET
echo "<h3>4. فحص مشكلة LIMIT/OFFSET:</h3>";

echo "<h4>اختبار مع LIMIT مباشر:</h4>";
try {
    $limitTest = $database->fetchAll("SELECT * FROM products WHERE status = 'active' ORDER BY id DESC LIMIT 5");
    echo "عدد المنتجات مع LIMIT مباشر: " . count($limitTest) . "<br>";
} catch (Exception $e) {
    echo "❌ خطأ مع LIMIT مباشر: " . $e->getMessage() . "<br>";
}

echo "<h4>اختبار مع LIMIT كمعامل:</h4>";
try {
    $paramTest = $database->fetchAll("SELECT * FROM products WHERE status = 'active' ORDER BY id DESC LIMIT :limit", ['limit' => 5]);
    echo "عدد المنتجات مع LIMIT كمعامل: " . count($paramTest) . "<br>";
} catch (Exception $e) {
    echo "❌ خطأ مع LIMIT كمعامل: " . $e->getMessage() . "<br>";
}

// اختبار الأقسام
echo "<h3>5. اختبار الأقسام:</h3>";
try {
    $mainCategories = $categoryManager->getMainCategories();
    echo "عدد الأقسام الرئيسية: " . count($mainCategories) . "<br>";
    
    if (count($mainCategories) > 0) {
        echo "<ul>";
        foreach ($mainCategories as $cat) {
            $catProductsCount = $productManager->getProductsCount($cat['id']);
            echo "<li>" . $cat['name'] . " (عدد المنتجات: $catProductsCount)</li>";
        }
        echo "</ul>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الأقسام: " . $e->getMessage() . "<br>";
}

// النتيجة النهائية
echo "<h3>6. النتيجة والحلول:</h3>";

if (count($products) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ المنتجات موجودة!</h4>";
    echo "<p>تم العثور على " . count($products) . " منتج</p>";
    echo "<p>المشكلة قد تكون في عرض HTML أو CSS</p>";
    echo "</div>";
    
    // عرض المنتجات
    echo "<h4>المنتجات المسترجعة:</h4>";
    echo "<div style='display: flex; flex-wrap: wrap; gap: 10px;'>";
    foreach (array_slice($products, 0, 3) as $product) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 200px;'>";
        echo "<h5>" . htmlspecialchars($product['name']) . "</h5>";
        echo "<p>القسم: " . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</p>";
        echo "<p>السعر: " . formatPrice($product['price']) . "</p>";
        if ($product['image']) {
            $imageUrl = UPLOADS_URL . '/' . $product['image'];
            echo "<img src='$imageUrl' alt='" . htmlspecialchars($product['name']) . "' style='width: 100%; max-width: 180px; height: auto;'>";
        }
        echo "</div>";
    }
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ لا توجد منتجات</h4>";
    
    if (count($directProducts) > 0) {
        echo "<p><strong>المشكلة:</strong> المنتجات موجودة في قاعدة البيانات لكن دوال ProductManager لا تسترجعها</p>";
        echo "<p><strong>السبب المحتمل:</strong> مشكلة في معاملات LIMIT/OFFSET</p>";
        echo "<h5>الحلول:</h5>";
        echo "<ol>";
        echo "<li>إصلاح دالة getAllProducts في ProductManager</li>";
        echo "<li>استخدام LIMIT مباشر بدلاً من المعاملات</li>";
        echo "<li>فحص دالة getProductsByCategory</li>";
        echo "</ol>";
    } else {
        echo "<p><strong>المشكلة:</strong> لا توجد منتجات في قاعدة البيانات</p>";
        echo "<p><strong>الحل:</strong> <a href='add_sample_data.php'>إضافة بيانات تجريبية</a></p>";
    }
    echo "</div>";
}

echo "<h3>7. روابط الاختبار:</h3>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;' target='_blank'>صفحة المنتجات</a>";
echo "<a href='?category=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار قسم 1</a>";
echo "<a href='?search=عسل' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار البحث</a>";
echo "<a href='test_quick.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الاختبار السريع</a>";
?>
