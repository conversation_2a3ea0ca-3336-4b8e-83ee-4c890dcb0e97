<?php
/**
 * إعدادات الموقع
 * Site Settings
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $settings = [
        'site_name' => cleanInput($_POST['site_name'] ?? ''),
        'site_description' => cleanInput($_POST['site_description'] ?? ''),
        'site_keywords' => cleanInput($_POST['site_keywords'] ?? ''),
        'contact_email' => cleanInput($_POST['contact_email'] ?? ''),
        'contact_phone' => cleanInput($_POST['contact_phone'] ?? ''),
        'contact_address' => cleanInput($_POST['contact_address'] ?? ''),
        'facebook_url' => cleanInput($_POST['facebook_url'] ?? ''),
        'twitter_url' => cleanInput($_POST['twitter_url'] ?? ''),
        'instagram_url' => cleanInput($_POST['instagram_url'] ?? ''),
        'whatsapp_number' => cleanInput($_POST['whatsapp_number'] ?? ''),
        'currency' => cleanInput($_POST['currency'] ?? 'SAR'),
        'tax_rate' => floatval($_POST['tax_rate'] ?? 15),
        'shipping_cost' => floatval($_POST['shipping_cost'] ?? 0),
        'free_shipping_threshold' => floatval($_POST['free_shipping_threshold'] ?? 200),
        'items_per_page' => intval($_POST['items_per_page'] ?? 12),
        'admin_items_per_page' => intval($_POST['admin_items_per_page'] ?? 20),
        'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
        'allow_registration' => isset($_POST['allow_registration']) ? 1 : 0,
        'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0
    ];
    
    $errors = [];
    
    // التحقق من البيانات المطلوبة
    if (empty($settings['site_name'])) $errors[] = 'اسم الموقع مطلوب';
    if (empty($settings['contact_email']) || !filter_var($settings['contact_email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($errors)) {
        $success = true;
        
        // حفظ كل إعداد في قاعدة البيانات
        foreach ($settings as $key => $value) {
            $existing = $database->fetch("SELECT id FROM site_settings WHERE setting_key = :key", ['key' => $key]);
            
            if ($existing) {
                // تحديث الإعداد الموجود
                if (!$database->update('site_settings', ['setting_value' => $value], 'setting_key = :key', ['key' => $key])) {
                    $success = false;
                    break;
                }
            } else {
                // إضافة إعداد جديد
                if (!$database->insert('site_settings', ['setting_key' => $key, 'setting_value' => $value])) {
                    $success = false;
                    break;
                }
            }
        }
        
        if ($success) {
            $message = 'تم حفظ الإعدادات بنجاح';
            $messageType = 'success';
            logActivity('settings_updated', 'تم تحديث إعدادات الموقع');
        } else {
            $message = 'حدث خطأ أثناء حفظ الإعدادات';
            $messageType = 'danger';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

// الحصول على الإعدادات الحالية
$currentSettings = [];
$settingsResult = $database->fetchAll("SELECT setting_key, setting_value FROM site_settings");
foreach ($settingsResult as $setting) {
    $currentSettings[$setting['setting_key']] = $setting['setting_value'];
}

$pageTitle = "إعدادات الموقع - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="sidebar-logo">
                    <i class="fas fa-leaf"></i> G8 Admin
                </a>
            </div>
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a></li>
                    <li><a href="products.php"><i class="fas fa-box"></i> المنتجات</a></li>
                    <li><a href="categories.php"><i class="fas fa-tags"></i> الأقسام</a></li>
                    <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> الطلبات</a></li>
                    <li><a href="users.php"><i class="fas fa-users"></i> المستخدمين</a></li>
                    <li><a href="activity-logs.php"><i class="fas fa-history"></i> سجل الأنشطة</a></li>
                    <li><a href="settings.php" class="active"><i class="fas fa-cog"></i> الإعدادات</a></li>
                    <li><a href="<?php echo SITE_URL; ?>" target="_blank"><i class="fas fa-external-link-alt"></i> عرض الموقع</a></li>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إعدادات الموقع</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <form method="POST" action="settings.php">
                    <div class="row">
                        <!-- General Settings -->
                        <div class="col-lg-6">
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">الإعدادات العامة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="site_name" class="form-label">اسم الموقع *</label>
                                        <input type="text" class="form-control" id="site_name" name="site_name" 
                                               value="<?php echo htmlspecialchars($currentSettings['site_name'] ?? 'G8 Store'); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="site_description" class="form-label">وصف الموقع</label>
                                        <textarea class="form-control" id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($currentSettings['site_description'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="site_keywords" class="form-label">الكلمات المفتاحية</label>
                                        <input type="text" class="form-control" id="site_keywords" name="site_keywords" 
                                               placeholder="كلمة1, كلمة2, كلمة3" 
                                               value="<?php echo htmlspecialchars($currentSettings['site_keywords'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">معلومات التواصل</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="contact_email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="<?php echo htmlspecialchars($currentSettings['contact_email'] ?? ''); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="<?php echo htmlspecialchars($currentSettings['contact_phone'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="contact_address" class="form-label">العنوان</label>
                                        <textarea class="form-control" id="contact_address" name="contact_address" rows="3"><?php echo htmlspecialchars($currentSettings['contact_address'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Social Media -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">وسائل التواصل الاجتماعي</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="facebook_url" class="form-label">رابط فيسبوك</label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                               value="<?php echo htmlspecialchars($currentSettings['facebook_url'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="twitter_url" class="form-label">رابط تويتر</label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                               value="<?php echo htmlspecialchars($currentSettings['twitter_url'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="instagram_url" class="form-label">رابط إنستغرام</label>
                                        <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                               value="<?php echo htmlspecialchars($currentSettings['instagram_url'] ?? ''); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="whatsapp_number" class="form-label">رقم واتساب</label>
                                        <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                               placeholder="966501234567" 
                                               value="<?php echo htmlspecialchars($currentSettings['whatsapp_number'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Store Settings -->
                        <div class="col-lg-6">
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">إعدادات المتجر</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="currency" class="form-label">العملة</label>
                                            <select class="form-select" id="currency" name="currency">
                                                <option value="SAR" <?php echo ($currentSettings['currency'] ?? 'SAR') === 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                                <option value="USD" <?php echo ($currentSettings['currency'] ?? '') === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                                <option value="EUR" <?php echo ($currentSettings['currency'] ?? '') === 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="tax_rate" class="form-label">معدل الضريبة (%)</label>
                                            <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                                   step="0.01" min="0" max="100" 
                                                   value="<?php echo $currentSettings['tax_rate'] ?? 15; ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="shipping_cost" class="form-label">تكلفة الشحن</label>
                                            <input type="number" class="form-control" id="shipping_cost" name="shipping_cost" 
                                                   step="0.01" min="0" 
                                                   value="<?php echo $currentSettings['shipping_cost'] ?? 0; ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="free_shipping_threshold" class="form-label">حد الشحن المجاني</label>
                                            <input type="number" class="form-control" id="free_shipping_threshold" name="free_shipping_threshold" 
                                                   step="0.01" min="0" 
                                                   value="<?php echo $currentSettings['free_shipping_threshold'] ?? 200; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Display Settings -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">إعدادات العرض</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="items_per_page" class="form-label">عدد المنتجات في الصفحة</label>
                                            <input type="number" class="form-control" id="items_per_page" name="items_per_page" 
                                                   min="1" max="100" 
                                                   value="<?php echo $currentSettings['items_per_page'] ?? 12; ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="admin_items_per_page" class="form-label">عدد العناصر في لوحة التحكم</label>
                                            <input type="number" class="form-control" id="admin_items_per_page" name="admin_items_per_page" 
                                                   min="1" max="100" 
                                                   value="<?php echo $currentSettings['admin_items_per_page'] ?? 20; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- System Settings -->
                            <div class="admin-card mb-4">
                                <div class="card-header">
                                    <h5 class="card-title">إعدادات النظام</h5>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                               <?php echo ($currentSettings['maintenance_mode'] ?? 0) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="maintenance_mode">
                                            وضع الصيانة
                                        </label>
                                        <div class="form-text">عند التفعيل، سيظهر للزوار رسالة صيانة</div>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="allow_registration" name="allow_registration" 
                                               <?php echo ($currentSettings['allow_registration'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="allow_registration">
                                            السماح بالتسجيل
                                        </label>
                                        <div class="form-text">السماح للمستخدمين الجدد بإنشاء حسابات</div>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" 
                                               <?php echo ($currentSettings['email_notifications'] ?? 1) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="email_notifications">
                                            إشعارات البريد الإلكتروني
                                        </label>
                                        <div class="form-text">إرسال إشعارات عند الطلبات الجديدة</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="admin-card">
                                <div class="card-body">
                                    <div class="d-grid">
                                        <button type="submit" class="btn-admin btn-primary btn-lg">
                                            <i class="fas fa-save"></i> حفظ الإعدادات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // تحذير عند تفعيل وضع الصيانة
    document.getElementById('maintenance_mode').addEventListener('change', function() {
        if (this.checked) {
            if (!confirm('هل أنت متأكد من تفعيل وضع الصيانة؟ سيصبح الموقع غير متاح للزوار.')) {
                this.checked = false;
            }
        }
    });
    </script>
</body>
</html>
