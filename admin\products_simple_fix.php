<?php
/**
 * حل بسيط لمشكلة المنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<h1>حل بسيط لمشكلة المنتجات</h1>";

// اختبار 1: استعلام مباشر بدون معاملات
echo "<h3>اختبار 1: استعلام مباشر</h3>";
$directProducts = $database->fetchAll("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id DESC");
echo "عدد المنتجات من الاستعلام المباشر: " . count($directProducts) . "<br>";

if (count($directProducts) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>ID</th><th>الاسم</th><th>القسم</th><th>السعر</th></tr>";
    foreach ($directProducts as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</td>";
        echo "<td>" . formatPrice($product['price']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ لا توجد منتجات في قاعدة البيانات</p>";
}

// اختبار 2: استعلام مع LIMIT بدون معاملات
echo "<h3>اختبار 2: استعلام مع LIMIT مباشر</h3>";
$limitProducts = $database->fetchAll("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id DESC LIMIT 20");
echo "عدد المنتجات مع LIMIT 20: " . count($limitProducts) . "<br>";

// اختبار 3: استعلام مع معاملات
echo "<h3>اختبار 3: استعلام مع معاملات</h3>";
$sql = "SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id DESC LIMIT :limit OFFSET :offset";
$params = ['limit' => 20, 'offset' => 0];

echo "الاستعلام: " . $sql . "<br>";
echo "المعاملات: " . json_encode($params) . "<br>";

try {
    $paramProducts = $database->fetchAll($sql, $params);
    echo "عدد المنتجات مع المعاملات: " . count($paramProducts) . "<br>";
} catch (Exception $e) {
    echo "❌ خطأ في الاستعلام مع المعاملات: " . $e->getMessage() . "<br>";
    $paramProducts = [];
}

// اختبار 4: فحص كلاس Database
echo "<h3>اختبار 4: فحص كلاس Database</h3>";
echo "نوع كلاس Database: " . get_class($database) . "<br>";

// اختبار 5: استعلام بدون LIMIT/OFFSET في المعاملات
echo "<h3>اختبار 5: استعلام محسن</h3>";
$improvedSql = "SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id DESC LIMIT 20";
$improvedProducts = $database->fetchAll($improvedSql, []);
echo "عدد المنتجات من الاستعلام المحسن: " . count($improvedProducts) . "<br>";

// الحل النهائي
echo "<h3>الحل النهائي:</h3>";

if (count($directProducts) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ تم العثور على المنتجات!</h4>";
    echo "<p><strong>المشكلة:</strong> مشكلة في استخدام معاملات LIMIT/OFFSET</p>";
    echo "<p><strong>الحل:</strong> استخدام LIMIT مباشر في الاستعلام بدلاً من المعاملات</p>";
    
    // إنشاء صفحة منتجات تعمل
    echo "<h4>إنشاء صفحة منتجات تعمل:</h4>";
    
    $workingPageContent = '<?php
require_once "../includes/functions.php";

if (!isAdmin()) {
    header("Location: login.php");
    exit;
}

// الحصول على المنتجات بطريقة تعمل
$products = $database->fetchAll("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id DESC LIMIT 50");
$totalProducts = $database->fetch("SELECT COUNT(*) as total FROM products")["total"];
$categories = $categoryManager->getAllCategories();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>إدارة المنتجات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
<div class="container-fluid mt-4">
    <h2>إدارة المنتجات (<?php echo $totalProducts; ?>)</h2>
    
    <?php if (count($products) > 0): ?>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>اسم المنتج</th>
                    <th>القسم</th>
                    <th>السعر</th>
                    <th>المخزون</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($products as $product): ?>
                <tr>
                    <td><?php echo $product["id"]; ?></td>
                    <td><?php echo htmlspecialchars($product["name"]); ?></td>
                    <td><?php echo htmlspecialchars($product["category_name"] ?? "غير محدد"); ?></td>
                    <td><?php echo formatPrice($product["price"]); ?></td>
                    <td><?php echo $product["stock_quantity"]; ?></td>
                    <td><?php echo $product["status"]; ?></td>
                    <td>
                        <a href="edit-product.php?id=<?php echo $product["id"]; ?>" class="btn btn-sm btn-primary">تعديل</a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php else: ?>
    <div class="alert alert-warning">لا توجد منتجات</div>
    <?php endif; ?>
    
    <a href="add-product.php" class="btn btn-success">إضافة منتج جديد</a>
</div>
</body>
</html>';
    
    // حفظ الصفحة الجديدة
    file_put_contents('products_working.php', $workingPageContent);
    echo "<p>✅ تم إنشاء صفحة <a href='products_working.php' target='_blank'>products_working.php</a></p>";
    
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>❌ لا توجد منتجات في قاعدة البيانات</h4>";
    echo "<p><strong>الحل:</strong> <a href='../add_sample_data.php'>إضافة بيانات تجريبية</a></p>";
    echo "</div>";
}

echo "<h3>روابط مفيدة:</h3>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الأصلية</a>";
echo "<a href='products_fixed.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة المُصلحة</a>";
echo "<a href='products_working.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة التي تعمل</a>";
echo "<a href='add-product.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتج</a>";
?>
