<?php
/**
 * حذف منتج من السلة
 * Remove Product from Cart
 */

header('Content-Type: application/json');
require_once '../includes/functions.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

try {
    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('بيانات غير صحيحة');
    }
    
    $productId = intval($input['product_id'] ?? 0);
    
    // التحقق من صحة البيانات
    if ($productId <= 0) {
        throw new Exception('معرف المنتج غير صحيح');
    }
    
    // حذف المنتج من السلة
    $cartManager->removeFromCart($productId);
    
    // تسجيل النشاط
    logActivity('remove_from_cart', "تم حذف منتج رقم {$productId} من السلة");
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف المنتج من السلة',
        'cart_count' => $cartManager->getCartCount(),
        'cart_total' => $cartManager->getCartTotal()
    ]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
