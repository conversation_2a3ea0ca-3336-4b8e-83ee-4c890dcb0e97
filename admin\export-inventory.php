<?php
/**
 * تصدير بيانات المخزون
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    http_response_code(403);
    exit('غير مصرح لك بالوصول');
}

// الحصول على جميع المنتجات مع بيانات المخزون
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        WHERE p.status = 'active' 
        ORDER BY p.name ASC";

$products = $database->fetchAll($sql);

// إعداد ملف CSV
$filename = 'inventory_report_' . date('Y-m-d_H-i-s') . '.csv';

header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// إنشاء ملف CSV
$output = fopen('php://output', 'w');

// إضافة BOM للدعم العربي في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// عناوين الأعمدة
$headers = [
    'اسم المنتج',
    'رمز SKU',
    'القسم',
    'السعر',
    'سعر البيع',
    'المخزون الحالي',
    'الحد الأدنى',
    'حالة المخزون',
    'الموقع',
    'تاريخ الإنشاء',
    'آخر تحديث'
];

fputcsv($output, $headers);

// إضافة بيانات المنتجات
foreach ($products as $product) {
    // تحديد حالة المخزون
    if ($product['stock_quantity'] == 0) {
        $stockStatus = 'نفد المخزون';
    } elseif ($product['stock_quantity'] <= $product['min_stock_level']) {
        $stockStatus = 'مخزون منخفض';
    } else {
        $stockStatus = 'متوفر';
    }
    
    $row = [
        $product['name'],
        $product['sku'] ?? '',
        $product['category_name'] ?? 'غير محدد',
        number_format($product['price'], 2),
        $product['sale_price'] ? number_format($product['sale_price'], 2) : '',
        $product['stock_quantity'],
        $product['min_stock_level'],
        $stockStatus,
        $product['location'] ?? '',
        formatDate($product['created_at'], 'd/m/Y'),
        formatDate($product['updated_at'], 'd/m/Y')
    ];
    
    fputcsv($output, $row);
}

fclose($output);
exit;
?>
