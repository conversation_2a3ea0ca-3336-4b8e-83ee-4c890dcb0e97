<?php
/**
 * صفحة اتصل بنا
 * Contact Us Page
 */

require_once 'includes/functions.php';

$message = '';
$messageType = 'info';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = cleanInput($_POST['name'] ?? '');
    $email = cleanInput($_POST['email'] ?? '');
    $phone = cleanInput($_POST['phone'] ?? '');
    $subject = cleanInput($_POST['subject'] ?? '');
    $messageText = cleanInput($_POST['message'] ?? '');
    
    $errors = [];
    
    if (empty($name)) $errors[] = 'الاسم مطلوب';
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors[] = 'البريد الإلكتروني غير صحيح';
    if (empty($subject)) $errors[] = 'الموضوع مطلوب';
    if (empty($messageText)) $errors[] = 'الرسالة مطلوبة';
    
    if (empty($errors)) {
        // حفظ الرسالة في قاعدة البيانات (يمكن إضافة جدول للرسائل لاحقاً)
        // أو إرسال بريد إلكتروني
        
        logActivity('contact_form_submitted', "تم إرسال رسالة من: {$name} - {$email}");
        
        $message = 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.';
        $messageType = 'success';
        
        // مسح النموذج
        $name = $email = $phone = $subject = $messageText = '';
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'danger';
    }
}

$pageTitle = "اتصل بنا - " . getSiteSetting('site_name');
$pageDescription = "تواصل معنا للاستفسارات والدعم الفني";
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartManager->getCartCount(); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="navbar-nav d-flex flex-row">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link">الرئيسية</a>
                </li>
                <?php 
                $mainCategories = $categoryManager->getMainCategories();
                foreach ($mainCategories as $category): 
                ?>
                <li class="nav-item dropdown">
                    <a href="products.php?category=<?php echo $category['id']; ?>" class="nav-link">
                        <?php echo $category['name']; ?>
                    </a>
                </li>
                <?php endforeach; ?>
                <li class="nav-item">
                    <a href="contact.php" class="nav-link active">اتصل بنا</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item active">اتصل بنا</li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="page-title text-center mb-5">اتصل بنا</h1>
                </div>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <div class="row">
                <!-- Contact Form -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-envelope"></i> أرسل لنا رسالة</h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="contact.php">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="name" class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="<?php echo isset($name) ? htmlspecialchars($name) : ''; ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="phone" class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" 
                                               value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="subject" class="form-label">الموضوع *</label>
                                        <select class="form-select" id="subject" name="subject" required>
                                            <option value="">اختر الموضوع</option>
                                            <option value="استفسار عام" <?php echo (isset($subject) && $subject === 'استفسار عام') ? 'selected' : ''; ?>>استفسار عام</option>
                                            <option value="استفسار عن منتج" <?php echo (isset($subject) && $subject === 'استفسار عن منتج') ? 'selected' : ''; ?>>استفسار عن منتج</option>
                                            <option value="شكوى" <?php echo (isset($subject) && $subject === 'شكوى') ? 'selected' : ''; ?>>شكوى</option>
                                            <option value="اقتراح" <?php echo (isset($subject) && $subject === 'اقتراح') ? 'selected' : ''; ?>>اقتراح</option>
                                            <option value="دعم فني" <?php echo (isset($subject) && $subject === 'دعم فني') ? 'selected' : ''; ?>>دعم فني</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="message" class="form-label">الرسالة *</label>
                                    <textarea class="form-control" id="message" name="message" rows="6" 
                                              placeholder="اكتب رسالتك هنا..." required><?php echo isset($messageText) ? htmlspecialchars($messageText) : ''; ?></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane"></i> إرسال الرسالة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="col-lg-4">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h3><i class="fas fa-info-circle"></i> معلومات التواصل</h3>
                        </div>
                        <div class="card-body">
                            <div class="contact-info">
                                <div class="contact-item mb-4">
                                    <div class="contact-icon">
                                        <i class="fas fa-map-marker-alt text-primary"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>العنوان</h5>
                                        <p><?php echo getSiteSetting('contact_address', 'المملكة العربية السعودية'); ?></p>
                                    </div>
                                </div>
                                
                                <div class="contact-item mb-4">
                                    <div class="contact-icon">
                                        <i class="fas fa-phone text-success"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>الهاتف</h5>
                                        <p><a href="tel:<?php echo getSiteSetting('contact_phone'); ?>"><?php echo getSiteSetting('contact_phone'); ?></a></p>
                                    </div>
                                </div>
                                
                                <div class="contact-item mb-4">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope text-info"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>البريد الإلكتروني</h5>
                                        <p><a href="mailto:<?php echo getSiteSetting('contact_email'); ?>"><?php echo getSiteSetting('contact_email'); ?></a></p>
                                    </div>
                                </div>
                                
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-clock text-warning"></i>
                                    </div>
                                    <div class="contact-details">
                                        <h5>ساعات العمل</h5>
                                        <p>السبت - الخميس: 9:00 ص - 6:00 م<br>الجمعة: مغلق</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-share-alt"></i> تابعنا</h3>
                        </div>
                        <div class="card-body">
                            <div class="social-links text-center">
                                <a href="#" class="btn btn-outline-primary me-2 mb-2">
                                    <i class="fab fa-facebook"></i> فيسبوك
                                </a>
                                <a href="#" class="btn btn-outline-info me-2 mb-2">
                                    <i class="fab fa-twitter"></i> تويتر
                                </a>
                                <a href="#" class="btn btn-outline-danger me-2 mb-2">
                                    <i class="fab fa-instagram"></i> إنستغرام
                                </a>
                                <a href="#" class="btn btn-outline-success mb-2">
                                    <i class="fab fa-whatsapp"></i> واتساب
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div class="row mt-5">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-question-circle"></i> الأسئلة الشائعة</h3>
                        </div>
                        <div class="card-body">
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faq1">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                            كيف يمكنني تتبع طلبي؟
                                        </button>
                                    </h2>
                                    <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            يمكنك تتبع طلبك من خلال رقم الطلب الذي تم إرساله إليك عبر البريد الإلكتروني أو الرسائل النصية.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faq2">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                            ما هي طرق الدفع المتاحة؟
                                        </button>
                                    </h2>
                                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            نوفر طريقتين للدفع: الدفع عند الاستلام والتحويل البنكي.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="faq3">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                            كم تستغرق عملية التوصيل؟
                                        </button>
                                    </h2>
                                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            عادة ما تستغرق عملية التوصيل من 2-5 أيام عمل حسب المنطقة.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>عن المتجر</h3>
                    <p>متجر متخصص في بيع أفضل المنتجات الطبيعية من الأعشاب والعسل والجينسنج بأعلى معايير الجودة.</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                        <li><a href="products.php">المنتجات</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <ul>
                        <li><i class="fas fa-map-marker-alt"></i> <?php echo getSiteSetting('contact_address'); ?></li>
                        <li><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></li>
                        <li><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>تابعنا</h3>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-whatsapp fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> جميع الحقوق محفوظة - متجر المنتجات الطبيعية</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
</body>
</html>
