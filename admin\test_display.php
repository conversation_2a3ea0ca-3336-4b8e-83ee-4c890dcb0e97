<?php
/**
 * اختبار عرض المنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn() || !isAdmin()) {
    echo "❌ يجب تسجيل الدخول كمدير - <a href='login.php'>تسجيل الدخول</a><br>";
    exit;
}

echo "<h2>اختبار عرض المنتجات</h2>";

// الحصول على المنتجات
$sql = "SELECT p.*, c.name as category_name 
        FROM products p 
        LEFT JOIN categories c ON p.category_id = c.id 
        ORDER BY p.created_at DESC 
        LIMIT 10";

$products = $database->fetchAll($sql);

echo "<h3>نتائج الاستعلام:</h3>";
echo "عدد المنتجات: " . count($products) . "<br><br>";

if (count($products) > 0) {
    echo "<h3>اختبار العرض البسيط:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>اسم المنتج</th>";
    echo "<th style='padding: 10px;'>القسم</th>";
    echo "<th style='padding: 10px;'>السعر</th>";
    echo "<th style='padding: 10px;'>الحالة</th>";
    echo "</tr>";
    
    foreach ($products as $product) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $product['id'] . "</td>";
        echo "<td style='padding: 10px;'>" . $product['name'] . "</td>";
        echo "<td style='padding: 10px;'>" . ($product['category_name'] ?? 'غير محدد') . "</td>";
        echo "<td style='padding: 10px;'>" . formatPrice($product['price']) . "</td>";
        echo "<td style='padding: 10px;'>" . $product['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>اختبار العرض مع CSS:</h3>";
    ?>
    
    <!-- تضمين CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
    
    <div class="admin-card" style="margin: 20px 0;">
        <div class="card-header">
            <h5 class="card-title">المنتجات (<?php echo count($products); ?>)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>اسم المنتج</th>
                            <th>القسم</th>
                            <th>السعر</th>
                            <th>المخزون</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td>
                                <?php if ($product['image']): ?>
                                <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                                     alt="<?php echo $product['name']; ?>" 
                                     width="50" height="50" class="rounded">
                                <?php else: ?>
                                <div class="no-image-placeholder">
                                    <i class="fas fa-image"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <strong><?php echo $product['name']; ?></strong>
                                    <?php if ($product['sku']): ?>
                                    <br><small class="text-muted">رقم المنتج: <?php echo $product['sku']; ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td><?php echo $product['category_name'] ?? 'غير محدد'; ?></td>
                            <td>
                                <?php if ($product['sale_price']): ?>
                                <span class="text-success"><?php echo formatPrice($product['sale_price']); ?></span>
                                <br><small class="text-muted text-decoration-line-through"><?php echo formatPrice($product['price']); ?></small>
                                <?php else: ?>
                                <?php echo formatPrice($product['price']); ?>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="<?php echo ($product['stock_quantity'] <= 5) ? 'text-danger' : 'text-success'; ?>">
                                    <?php echo $product['stock_quantity']; ?>
                                </span>
                            </td>
                            <td>
                                <?php
                                $statusClass = '';
                                $statusText = '';
                                switch($product['status']) {
                                    case 'active':
                                        $statusClass = 'success';
                                        $statusText = 'نشط';
                                        break;
                                    case 'inactive':
                                        $statusClass = 'danger';
                                        $statusText = 'غير نشط';
                                        break;
                                    case 'out_of_stock':
                                        $statusClass = 'warning';
                                        $statusText = 'نفد المخزون';
                                        break;
                                    default:
                                        $statusClass = 'secondary';
                                        $statusText = $product['status'];
                                }
                                ?>
                                <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="edit-product.php?id=<?php echo $product['id']; ?>" 
                                       class="btn-admin btn-info btn-sm" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button class="btn-admin btn-danger btn-sm" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <?php
    echo "<h3>اختبار المتغيرات:</h3>";
    echo "<ul>";
    echo "<li><strong>ASSETS_PATH:</strong> " . (defined('ASSETS_PATH') ? ASSETS_PATH : 'غير معرف') . "</li>";
    echo "<li><strong>UPLOADS_URL:</strong> " . (defined('UPLOADS_URL') ? UPLOADS_URL : 'غير معرف') . "</li>";
    echo "<li><strong>ADMIN_ITEMS_PER_PAGE:</strong> " . (defined('ADMIN_ITEMS_PER_PAGE') ? ADMIN_ITEMS_PER_PAGE : 'غير معرف') . "</li>";
    echo "</ul>";
    
    echo "<h3>اختبار الدوال:</h3>";
    echo "<ul>";
    try {
        echo "<li><strong>formatPrice(99.99):</strong> " . formatPrice(99.99) . "</li>";
    } catch (Exception $e) {
        echo "<li><strong>formatPrice:</strong> خطأ - " . $e->getMessage() . "</li>";
    }
    
    try {
        echo "<li><strong>formatDate(now):</strong> " . formatDate(date('Y-m-d H:i:s')) . "</li>";
    } catch (Exception $e) {
        echo "<li><strong>formatDate:</strong> خطأ - " . $e->getMessage() . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>مقارنة مع admin/products.php:</h3>";
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<p><strong>إذا كانت المنتجات تظهر هنا ولا تظهر في admin/products.php، فالمشكلة قد تكون:</strong></p>";
    echo "<ol>";
    echo "<li><strong>مشكلة في CSS:</strong> ملف admin.css لا يتم تحميله بشكل صحيح</li>";
    echo "<li><strong>مشكلة في JavaScript:</strong> خطأ في JavaScript يمنع العرض</li>";
    echo "<li><strong>مشكلة في المتغيرات:</strong> متغير \$products فارغ في admin/products.php</li>";
    echo "<li><strong>مشكلة في الشروط:</strong> شرط العرض (!empty(\$products)) لا يعمل</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>الخطوات التالية:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<ol>";
    echo "<li><a href='products.php' target='_blank'>افتح admin/products.php في تبويب جديد</a></li>";
    echo "<li>قارن العرض بين هذه الصفحة وصفحة admin/products.php</li>";
    echo "<li>تحقق من وحدة تحكم المطور (F12) للأخطاء</li>";
    echo "<li>تحقق من تحميل ملفات CSS</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "❌ لا توجد منتجات للعرض<br>";
    echo "<a href='../add_sample_data.php'>إضافة بيانات تجريبية</a><br>";
}

echo "<br><h3>روابط مفيدة:</h3>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>admin/products.php</a>";
echo "<a href='add-product.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتج</a>";
echo "<a href='debug_products_display.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تشخيص مفصل</a>";
?>

<script>
// اختبار JavaScript
console.log('JavaScript يعمل بشكل صحيح');
console.log('عدد المنتجات:', <?php echo count($products); ?>);

// اختبار تحميل CSS
document.addEventListener('DOMContentLoaded', function() {
    const adminTable = document.querySelector('.admin-table');
    if (adminTable) {
        console.log('✅ جدول admin-table موجود');
        const computedStyle = window.getComputedStyle(adminTable);
        console.log('عرض الجدول:', computedStyle.width);
        console.log('عرض الجدول:', computedStyle.display);
    } else {
        console.log('❌ جدول admin-table غير موجود');
    }
    
    const adminCard = document.querySelector('.admin-card');
    if (adminCard) {
        console.log('✅ admin-card موجود');
    } else {
        console.log('❌ admin-card غير موجود');
    }
});
</script>
