<?php
/**
 * إصلاح مشاكل المنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>إصلاح مشاكل المنتجات</h2>";

try {
    require_once '../includes/functions.php';
    
    // التحقق من تسجيل الدخول
    if (!isLoggedIn() || !isAdmin()) {
        echo "❌ يجب تسجيل الدخول كمدير - <a href='login.php'>تسجيل الدخول</a><br>";
        exit;
    }
    
    echo "<h3>1. فحص المنتجات الحالية:</h3>";
    
    // عرض جميع المنتجات
    $allProducts = $database->fetchAll("SELECT * FROM products ORDER BY id");
    echo "عدد المنتجات الإجمالي: " . count($allProducts) . "<br><br>";
    
    if (count($allProducts) == 0) {
        echo "❌ لا توجد منتجات في قاعدة البيانات<br>";
        echo "<a href='../add_sample_data.php'>إضافة بيانات تجريبية</a><br>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>القسم</th><th>الحالة</th><th>تاريخ الإنشاء</th><th>المشاكل</th></tr>";
    
    $problemsFound = 0;
    $fixedProblems = 0;
    
    foreach ($allProducts as $product) {
        $problems = [];
        
        // فحص تاريخ الإنشاء
        if (empty($product['created_at']) || $product['created_at'] == '0000-00-00 00:00:00') {
            $problems[] = "تاريخ إنشاء فارغ";
        }
        
        // فحص القسم
        if (empty($product['category_id']) || $product['category_id'] == 0) {
            $problems[] = "قسم فارغ";
        }
        
        // فحص الحالة
        if (empty($product['status'])) {
            $problems[] = "حالة فارغة";
        }
        
        // فحص السعر
        if (empty($product['price']) || $product['price'] <= 0) {
            $problems[] = "سعر غير صحيح";
        }
        
        $problemsFound += count($problems);
        
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['name'] . "</td>";
        echo "<td>" . $product['category_id'] . "</td>";
        echo "<td>" . $product['status'] . "</td>";
        echo "<td>" . $product['created_at'] . "</td>";
        echo "<td style='color: " . (count($problems) > 0 ? 'red' : 'green') . ";'>" . 
             (count($problems) > 0 ? implode(', ', $problems) : 'لا توجد مشاكل') . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<br><h3>2. ملخص المشاكل:</h3>";
    echo "عدد المشاكل المكتشفة: " . $problemsFound . "<br>";
    
    if ($problemsFound > 0) {
        echo "<br><h3>3. إصلاح المشاكل:</h3>";
        
        // إصلاح تاريخ الإنشاء
        $result1 = $database->query("UPDATE products SET created_at = NOW() WHERE created_at IS NULL OR created_at = '0000-00-00 00:00:00' OR created_at = ''");
        echo "✅ تم إصلاح تواريخ الإنشاء<br>";
        $fixedProblems++;
        
        // إصلاح الأقسام
        $defaultCategory = $database->fetch("SELECT id FROM categories ORDER BY id LIMIT 1");
        if ($defaultCategory) {
            $result2 = $database->query("UPDATE products SET category_id = :cat_id WHERE category_id IS NULL OR category_id = 0", 
                ['cat_id' => $defaultCategory['id']]);
            echo "✅ تم إصلاح أقسام المنتجات (تم تعيين القسم الافتراضي: " . $defaultCategory['id'] . ")<br>";
            $fixedProblems++;
        } else {
            echo "❌ لا توجد أقسام لتعيينها للمنتجات<br>";
            echo "<a href='categories.php'>إضافة قسم أولاً</a><br>";
        }
        
        // إصلاح الحالة
        $result3 = $database->query("UPDATE products SET status = 'active' WHERE status IS NULL OR status = ''");
        echo "✅ تم إصلاح حالة المنتجات<br>";
        $fixedProblems++;
        
        // إصلاح الأسعار
        $result4 = $database->query("UPDATE products SET price = 1.00 WHERE price IS NULL OR price <= 0");
        echo "✅ تم إصلاح أسعار المنتجات<br>";
        $fixedProblems++;
        
        // إصلاح المخزون
        $result5 = $database->query("UPDATE products SET stock_quantity = 0 WHERE stock_quantity IS NULL");
        echo "✅ تم إصلاح كميات المخزون<br>";
        $fixedProblems++;
        
        echo "<br><h3>4. النتيجة:</h3>";
        echo "تم إصلاح " . $fixedProblems . " نوع من المشاكل<br>";
        
    } else {
        echo "✅ لا توجد مشاكل في البيانات<br>";
    }
    
    echo "<br><h3>5. اختبار الاستعلام:</h3>";
    
    // اختبار الاستعلام المستخدم في admin/products.php
    $testSql = "SELECT p.*, c.name as category_name 
                FROM products p 
                LEFT JOIN categories c ON p.category_id = c.id 
                ORDER BY p.created_at DESC 
                LIMIT 10";
    
    $testProducts = $database->fetchAll($testSql);
    echo "عدد المنتجات من الاستعلام: " . count($testProducts) . "<br>";
    
    if (count($testProducts) > 0) {
        echo "<br><strong>المنتجات المسترجعة:</strong><br>";
        foreach ($testProducts as $product) {
            echo "- " . $product['name'] . " (القسم: " . ($product['category_name'] ?? 'غير محدد') . ")<br>";
        }
    } else {
        echo "❌ لا يزال الاستعلام لا يسترجع منتجات<br>";
        
        // اختبار استعلام أبسط
        echo "<br><strong>اختبار استعلام أبسط:</strong><br>";
        $simpleProducts = $database->fetchAll("SELECT * FROM products LIMIT 5");
        echo "عدد المنتجات من الاستعلام البسيط: " . count($simpleProducts) . "<br>";
    }
    
    echo "<br><h3>6. الخطوات التالية:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<ol>";
    echo "<li><a href='products.php'>تحقق من صفحة المنتجات</a></li>";
    echo "<li><a href='debug_products_display.php'>تشخيص مفصل</a></li>";
    echo "<li><a href='add-product.php'>إضافة منتج جديد</a></li>";
    echo "<li><a href='categories.php'>إدارة الأقسام</a></li>";
    echo "</ol>";
    echo "</div>";
    
    // إضافة منتج تجريبي إذا لم توجد منتجات
    if (count($allProducts) == 0) {
        echo "<br><h3>7. إضافة منتج تجريبي:</h3>";
        
        // التأكد من وجود قسم
        $category = $database->fetch("SELECT id FROM categories LIMIT 1");
        if (!$category) {
            echo "إضافة قسم تجريبي...<br>";
            $testCategory = [
                'name' => 'قسم تجريبي',
                'description' => 'قسم تجريبي للاختبار',
                'status' => 'active'
            ];
            $database->insert('categories', $testCategory);
            $categoryId = $database->lastInsertId();
        } else {
            $categoryId = $category['id'];
        }
        
        $testProduct = [
            'name' => 'منتج تجريبي ' . date('H:i:s'),
            'description' => 'هذا منتج تجريبي تم إنشاؤه تلقائياً للاختبار',
            'short_description' => 'منتج تجريبي',
            'category_id' => $categoryId,
            'price' => 50.00,
            'sku' => 'TEST' . time(),
            'stock_quantity' => 10,
            'status' => 'active',
            'featured' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $result = $database->insert('products', $testProduct);
        if ($result) {
            echo "✅ تم إضافة منتج تجريبي بنجاح (ID: " . $database->lastInsertId() . ")<br>";
        } else {
            echo "❌ فشل في إضافة المنتج التجريبي<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
