<?php
/**
 * تقرير المبيعات الشهرية - نسخة مبسطة
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// الحصول على الشهر المحدد
$selectedMonth = $_GET['month'] ?? date('Y-m');
$monthStart = $selectedMonth . '-01';
$monthEnd = date('Y-m-t', strtotime($monthStart));

$pageTitle = "تقرير المبيعات الشهرية - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">📊 تقرير المبيعات الشهرية</h1>
                <div class="header-actions">
                    <form method="GET" class="d-flex align-items-center">
                        <label for="month" class="form-label me-2">الشهر:</label>
                        <input type="month" class="form-control me-2" id="month" name="month" 
                               value="<?php echo $selectedMonth; ?>" onchange="this.form.submit()">
                    </form>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                
                <?php
                // التحقق من وجود جدول order_items
                $orderItemsExists = false;
                $hasData = false;
                
                try {
                    $database->fetch("SELECT 1 FROM order_items LIMIT 1");
                    $orderItemsExists = true;
                    
                    // فحص وجود بيانات
                    $dataCheck = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
                    $hasData = ($dataCheck > 0);
                    
                } catch (Exception $e) {
                    $orderItemsExists = false;
                }
                ?>
                
                <!-- تحذيرات -->
                <?php if (!$orderItemsExists): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> جدول المبيعات غير موجود</h5>
                    <p>جدول عناصر الطلبات (order_items) غير موجود في قاعدة البيانات.</p>
                    <div class="mt-3">
                        <a href="fix-sales-report.php" class="btn btn-warning me-2">
                            <i class="fas fa-wrench"></i> إصلاح تلقائي
                        </a>
                        <a href="check-database-structure.php" class="btn btn-info">
                            <i class="fas fa-database"></i> فحص قاعدة البيانات
                        </a>
                    </div>
                </div>
                <?php elseif (!$hasData): ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> لا توجد بيانات مبيعات</h5>
                    <p>جدول المبيعات موجود ولكن لا يحتوي على بيانات.</p>
                    <div class="mt-3">
                        <a href="create-sample-data.php" class="btn btn-primary me-2">
                            <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                        </a>
                        <a href="fix-sales-report.php" class="btn btn-success">
                            <i class="fas fa-magic"></i> إصلاح وإنشاء بيانات
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- إحصائيات أساسية -->
                <div class="row mb-4">
                    <?php
                    // حساب الإحصائيات الأساسية
                    $totalProducts = 0;
                    $totalOrders = 0;
                    $totalRevenue = 0;
                    $productsWithSales = 0;
                    
                    try {
                        $totalProducts = $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
                        
                        if ($orderItemsExists && $hasData) {
                            $salesStats = $database->fetch("
                                SELECT 
                                    COUNT(DISTINCT o.id) as total_orders,
                                    COALESCE(SUM(oi.quantity * oi.price), 0) as total_revenue,
                                    COUNT(DISTINCT oi.product_id) as products_with_sales
                                FROM order_items oi
                                JOIN orders o ON oi.order_id = o.id
                                WHERE o.created_at BETWEEN :start AND :end
                                AND o.status IN ('confirmed', 'delivered')
                            ", [
                                'start' => $monthStart . ' 00:00:00',
                                'end' => $monthEnd . ' 23:59:59'
                            ]);
                            
                            $totalOrders = $salesStats['total_orders'] ?? 0;
                            $totalRevenue = $salesStats['total_revenue'] ?? 0;
                            $productsWithSales = $salesStats['products_with_sales'] ?? 0;
                        }
                    } catch (Exception $e) {
                        // في حالة خطأ، استخدم القيم الافتراضية
                    }
                    ?>
                    
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo number_format($totalProducts); ?></h3>
                                <p class="mb-0">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo number_format($totalOrders); ?></h3>
                                <p class="mb-0">إجمالي الطلبات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3><?php echo number_format($productsWithSales); ?></h3>
                                <p class="mb-0">منتجات لها مبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h3><?php echo formatPrice($totalRevenue); ?></h3>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقرير المنتجات -->
                <?php if ($orderItemsExists && $hasData): ?>
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">تقرير المنتجات للشهر: <?php echo date('F Y', strtotime($selectedMonth)); ?></h5>
                    </div>
                    <div class="card-body">
                        <?php
                        try {
                            $productsSales = $database->fetchAll("
                                SELECT 
                                    p.id,
                                    p.name,
                                    p.stock_quantity,
                                    p.min_stock_level,
                                    p.price,
                                    c.name as category_name,
                                    COALESCE(SUM(oi.quantity), 0) as total_sold,
                                    COALESCE(SUM(oi.quantity * oi.price), 0) as total_revenue,
                                    COUNT(DISTINCT o.id) as order_count
                                FROM products p
                                LEFT JOIN categories c ON p.category_id = c.id
                                LEFT JOIN order_items oi ON p.id = oi.product_id
                                LEFT JOIN orders o ON oi.order_id = o.id 
                                    AND o.created_at BETWEEN :start AND :end
                                    AND o.status IN ('confirmed', 'delivered')
                                WHERE p.status = 'active'
                                GROUP BY p.id
                                ORDER BY total_sold DESC, p.name ASC
                                LIMIT 20
                            ", [
                                'start' => $monthStart . ' 00:00:00',
                                'end' => $monthEnd . ' 23:59:59'
                            ]);
                            
                            if (!empty($productsSales)): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الفئة</th>
                                            <th>المبيعات</th>
                                            <th>الإيرادات</th>
                                            <th>عدد الطلبات</th>
                                            <th>المخزون الحالي</th>
                                            <th>الحد الأدنى</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($productsSales as $product): ?>
                                        <tr>
                                            <td><strong><?php echo htmlspecialchars($product['name']); ?></strong></td>
                                            <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                            <td>
                                                <span class="badge bg-primary">
                                                    <?php echo number_format($product['total_sold']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatPrice($product['total_revenue']); ?></td>
                                            <td><?php echo number_format($product['order_count']); ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?php echo number_format($product['stock_quantity']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark">
                                                    <?php echo number_format($product['min_stock_level']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <p class="mb-0">لا توجد مبيعات للشهر المحدد</p>
                            </div>
                            <?php endif;
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>";
                            echo "<h6>خطأ في استرجاع البيانات:</h6>";
                            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
                            echo "</div>";
                        }
                        ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- أدوات سريعة -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">أدوات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="fix-sales-report.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-wrench"></i><br>
                                    إصلاح النظام
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="create-sample-data.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-plus"></i><br>
                                    بيانات تجريبية
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="inventory.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-warehouse"></i><br>
                                    إدارة المخزون
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="test-monthly-report.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-bug"></i><br>
                                    اختبار النظام
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات تقنية -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">معلومات تقنية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>حالة النظام:</h6>
                                <ul class="list-unstyled">
                                    <li>
                                        <?php if ($orderItemsExists): ?>
                                            <span class="badge bg-success">✅</span> جدول المبيعات موجود
                                        <?php else: ?>
                                            <span class="badge bg-danger">❌</span> جدول المبيعات غير موجود
                                        <?php endif; ?>
                                    </li>
                                    <li>
                                        <?php if ($hasData): ?>
                                            <span class="badge bg-success">✅</span> يوجد بيانات مبيعات
                                        <?php else: ?>
                                            <span class="badge bg-warning">⚠️</span> لا توجد بيانات مبيعات
                                        <?php endif; ?>
                                    </li>
                                    <li>
                                        <span class="badge bg-info">ℹ️</span> الشهر المحدد: <?php echo date('F Y', strtotime($selectedMonth)); ?>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>خطوات الإصلاح:</h6>
                                <ol>
                                    <li>اختبر النظام أولاً</li>
                                    <li>قم بالإصلاح التلقائي</li>
                                    <li>أنشئ بيانات تجريبية</li>
                                    <li>اختبر التقرير مرة أخرى</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
