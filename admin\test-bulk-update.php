<?php
/**
 * اختبار التحديث المجمع للحد الأدنى
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$message = '';
$messageType = 'info';

// اختبار التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_update'])) {
    $productId = $_POST['product_id'];
    $newMinStock = $_POST['new_min_stock'];
    
    try {
        // الحصول على القيمة الحالية
        $currentProduct = $database->fetch("SELECT name, min_stock_level FROM products WHERE id = :id", ['id' => $productId]);
        
        if ($currentProduct) {
            // تحديث الحد الأدنى
            $updateResult = $database->update('products', 
                ['min_stock_level' => $newMinStock, 'updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $productId]
            );
            
            if ($updateResult) {
                logActivity('test_min_stock_update', "اختبار تحديث الحد الأدنى للمنتج: {$currentProduct['name']} من {$currentProduct['min_stock_level']} إلى {$newMinStock}");
                $message = "تم تحديث الحد الأدنى للمنتج '{$currentProduct['name']}' من {$currentProduct['min_stock_level']} إلى {$newMinStock} بنجاح";
                $messageType = 'success';
            } else {
                $message = "فشل في تحديث المنتج";
                $messageType = 'danger';
            }
        } else {
            $message = "المنتج غير موجود";
            $messageType = 'danger';
        }
    } catch (Exception $e) {
        $message = "خطأ في التحديث: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// الحصول على المنتجات للاختبار
$products = $database->fetchAll("SELECT id, name, min_stock_level, stock_quantity FROM products WHERE status = 'active' ORDER BY name LIMIT 10");

echo "<h2>اختبار التحديث المجمع للحد الأدنى</h2>";

// عرض الرسائل
if ($message) {
    echo "<div style='padding: 15px; margin: 20px 0; border-radius: 5px; background: " . 
         ($messageType == 'success' ? '#d4edda; color: #155724;' : '#f8d7da; color: #721c24;') . "'>";
    echo $message;
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🧪 اختبار فردي للتحديث:</h3>";
echo "<p>اختبر تحديث منتج واحد للتأكد من عمل النظام</p>";

if (!empty($products)) {
    echo "<form method='POST' style='margin: 20px 0;'>";
    echo "<div style='display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;'>";
    
    echo "<select name='product_id' required style='padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "<option value=''>اختر منتج</option>";
    foreach ($products as $product) {
        echo "<option value='{$product['id']}'>{$product['name']} (حالي: {$product['min_stock_level']})</option>";
    }
    echo "</select>";
    
    echo "<input type='number' name='new_min_stock' placeholder='الحد الأدنى الجديد' min='0' max='1000' required style='padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    
    echo "<button type='submit' name='test_update' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;'>اختبار التحديث</button>";
    
    echo "</div>";
    echo "</form>";
} else {
    echo "<p style='color: red;'>لا توجد منتجات للاختبار</p>";
}
echo "</div>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📊 حالة المنتجات الحالية:</h3>";

if (!empty($products)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>اسم المنتج</th><th>المخزون الحالي</th><th>الحد الأدنى</th><th>الحالة</th>";
    echo "</tr>";
    
    foreach ($products as $product) {
        $statusColor = 'green';
        $statusText = 'متوفر';
        
        if ($product['stock_quantity'] == 0) {
            $statusColor = 'red';
            $statusText = 'نفد المخزون';
        } elseif ($product['stock_quantity'] <= $product['min_stock_level']) {
            $statusColor = 'orange';
            $statusText = 'مخزون منخفض';
        }
        
        echo "<tr>";
        echo "<td>{$product['id']}</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>{$product['stock_quantity']}</td>";
        echo "<td>{$product['min_stock_level']}</td>";
        echo "<td style='color: $statusColor; font-weight: bold;'>$statusText</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>لا توجد منتجات</p>";
}
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔧 تشخيص المشاكل المحتملة:</h3>";

// فحص قاعدة البيانات
echo "<h4>فحص قاعدة البيانات:</h4>";
try {
    $database->fetch("SELECT 1 FROM products LIMIT 1");
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات يعمل</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ مشكلة في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// فحص جدول المنتجات
try {
    $columns = $database->fetchAll("SHOW COLUMNS FROM products");
    $hasMinStockLevel = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'min_stock_level') {
            $hasMinStockLevel = true;
            break;
        }
    }
    
    if ($hasMinStockLevel) {
        echo "<p style='color: green;'>✅ عمود min_stock_level موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ عمود min_stock_level غير موجود</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص جدول المنتجات: " . $e->getMessage() . "</p>";
}

// فحص دالة التحديث
echo "<h4>فحص دالة التحديث:</h4>";
try {
    $testResult = $database->update('products', 
        ['updated_at' => date('Y-m-d H:i:s')], 
        'id = :id', 
        ['id' => 999999] // ID غير موجود
    );
    echo "<p style='color: green;'>✅ دالة update تعمل (لم تجد المنتج كما متوقع)</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ مشكلة في دالة update: " . $e->getMessage() . "</p>";
}

// فحص دالة logActivity
echo "<h4>فحص دالة logActivity:</h4>";
try {
    logActivity('test_function', 'اختبار دالة logActivity');
    echo "<p style='color: green;'>✅ دالة logActivity تعمل</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ مشكلة في دالة logActivity: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🛠️ خطوات الإصلاح:</h3>";
echo "<ol>";
echo "<li><strong>إذا كان الاختبار الفردي يعمل:</strong> المشكلة في صفحة التحديث المجمع</li>";
echo "<li><strong>إذا كان الاختبار الفردي لا يعمل:</strong> المشكلة في قاعدة البيانات أو الدوال</li>";
echo "<li><strong>تحقق من سجل الأخطاء:</strong> في ملفات PHP error log</li>";
echo "<li><strong>تحقق من سجل الأنشطة:</strong> في صفحة activity-logs.php</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='bulk-update-stock.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>التحديث المجمع</a>";
echo "<a href='inventory.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المخزون</a>";
echo "<a href='activity-logs.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>سجل الأنشطة</a>";
echo "<a href='products.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المنتجات</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات:</h4>";
echo "<ul>";
echo "<li>استخدم الاختبار الفردي أولاً للتأكد من عمل النظام</li>";
echo "<li>تحقق من سجل الأنشطة لرؤية التحديثات</li>";
echo "<li>إذا كان الاختبار يعمل، فالمشكلة في واجهة التحديث المجمع</li>";
echo "<li>تأكد من تغيير القيم قبل الضغط على حفظ في التحديث المجمع</li>";
echo "</ul>";
echo "</div>";
?>
