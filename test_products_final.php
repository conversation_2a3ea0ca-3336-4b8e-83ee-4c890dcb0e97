<?php
/**
 * اختبار نهائي لصفحة المنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/functions.php';

echo "<h2>اختبار نهائي لصفحة المنتجات</h2>";

// اختبار دوال ProductManager المحدثة
echo "<h3>1. اختبار دوال ProductManager المحدثة:</h3>";

try {
    // اختبار getAllProducts
    echo "<h4>getAllProducts:</h4>";
    $allProducts = $productManager->getAllProducts(1, 5, 'active');
    echo "عدد المنتجات: " . count($allProducts) . "<br>";
    
    if (count($allProducts) > 0) {
        echo "<ul>";
        foreach ($allProducts as $product) {
            echo "<li>" . htmlspecialchars($product['name']) . " - " . formatPrice($product['price']) . "</li>";
        }
        echo "</ul>";
    }
    
    // اختبار getProductsByCategory
    echo "<h4>getProductsByCategory:</h4>";
    $categories = $categoryManager->getMainCategories();
    if (!empty($categories)) {
        $firstCategoryId = $categories[0]['id'];
        $categoryProducts = $productManager->getProductsByCategory($firstCategoryId, 3, 0);
        echo "منتجات القسم الأول (" . $categories[0]['name'] . "): " . count($categoryProducts) . "<br>";
        
        if (count($categoryProducts) > 0) {
            echo "<ul>";
            foreach ($categoryProducts as $product) {
                echo "<li>" . htmlspecialchars($product['name']) . "</li>";
            }
            echo "</ul>";
        }
    }
    
    // اختبار searchProducts
    echo "<h4>searchProducts:</h4>";
    $searchResults = $productManager->searchProducts('عسل', 3, 0);
    echo "نتائج البحث عن 'عسل': " . count($searchResults) . "<br>";
    
    // اختبار getFeaturedProducts
    echo "<h4>getFeaturedProducts:</h4>";
    $featuredProducts = $productManager->getFeaturedProducts(3);
    echo "المنتجات المميزة: " . count($featuredProducts) . "<br>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

// محاكاة نفس منطق products.php
echo "<h3>2. محاكاة منطق products.php:</h3>";

$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;
$searchQuery = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = defined('ITEMS_PER_PAGE') ? ITEMS_PER_PAGE : 12;
$offset = ($page - 1) * $itemsPerPage;

echo "<p><strong>معاملات:</strong> category=$categoryId, search='$searchQuery', page=$page</p>";

$products = [];
$totalProducts = 0;
$pageTitle = '';

if ($searchQuery) {
    $products = $productManager->searchProducts($searchQuery, $itemsPerPage, $offset);
    $totalProducts = count($productManager->searchProducts($searchQuery));
    $pageTitle = "نتائج البحث عن: $searchQuery";
    
} elseif ($categoryId) {
    $category = $categoryManager->getCategoryById($categoryId);
    if ($category) {
        $products = $productManager->getProductsByCategory($categoryId, $itemsPerPage, $offset);
        $totalProducts = $productManager->getProductsCount($categoryId);
        $pageTitle = $category['name'];
    }
    
} else {
    $products = $productManager->getAllProducts($page, $itemsPerPage, 'active');
    $totalProducts = $productManager->getProductsCount();
    $pageTitle = "جميع المنتجات";
}

echo "<p><strong>النتيجة:</strong></p>";
echo "<ul>";
echo "<li>عنوان الصفحة: $pageTitle</li>";
echo "<li>عدد المنتجات المسترجعة: " . count($products) . "</li>";
echo "<li>إجمالي المنتجات: $totalProducts</li>";
echo "</ul>";

if (count($products) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ المنتجات تعمل بشكل صحيح!</h4>";
    echo "<p>تم استرجاع " . count($products) . " منتج بنجاح</p>";
    echo "</div>";
    
    echo "<h4>عينة من المنتجات:</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach (array_slice($products, 0, 4) as $product) {
        echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: white;'>";
        echo "<h5 style='margin: 0 0 10px 0; color: #333;'>" . htmlspecialchars($product['name']) . "</h5>";
        
        if ($product['image']) {
            $imageUrl = UPLOADS_URL . '/' . $product['image'];
            echo "<img src='$imageUrl' alt='" . htmlspecialchars($product['name']) . "' style='width: 100%; height: 150px; object-fit: cover; border-radius: 5px; margin-bottom: 10px;'>";
        } else {
            echo "<div style='width: 100%; height: 150px; background: #f8f9fa; border-radius: 5px; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; color: #999;'>لا توجد صورة</div>";
        }
        
        echo "<p style='margin: 5px 0; color: #666; font-size: 14px;'>القسم: " . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</p>";
        
        if ($product['sale_price']) {
            echo "<p style='margin: 5px 0;'>";
            echo "<span style='color: #28a745; font-weight: bold; font-size: 16px;'>" . formatPrice($product['sale_price']) . "</span> ";
            echo "<span style='color: #999; text-decoration: line-through; font-size: 14px;'>" . formatPrice($product['price']) . "</span>";
            echo "</p>";
        } else {
            echo "<p style='margin: 5px 0; color: #333; font-weight: bold; font-size: 16px;'>" . formatPrice($product['price']) . "</p>";
        }
        
        echo "<p style='margin: 10px 0 0 0; color: #666; font-size: 13px;'>" . truncateText($product['short_description'] ?? $product['description'], 80) . "</p>";
        echo "</div>";
    }
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ لا توجد منتجات</h4>";
    echo "<p>لم يتم العثور على أي منتجات</p>";
    echo "</div>";
}

echo "<h3>3. روابط الاختبار:</h3>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;' target='_blank'>صفحة المنتجات الفعلية</a>";
echo "<a href='debug_products_frontend.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;'>تشخيص مفصل</a>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;' target='_blank'>الصفحة الرئيسية</a>";
echo "</div>";

if (!empty($categories)) {
    echo "<h4>اختبار الأقسام:</h4>";
    foreach ($categories as $category) {
        echo "<a href='?category=" . $category['id'] . "' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin: 3px; display: inline-block; font-size: 14px;'>" . htmlspecialchars($category['name']) . "</a>";
    }
}

echo "<h4 style='margin-top: 20px;'>اختبار البحث:</h4>";
echo "<a href='?search=عسل' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin: 3px; display: inline-block;'>البحث عن: عسل</a>";
echo "<a href='?search=جينسنج' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 3px; margin: 3px; display: inline-block;'>البحث عن: جينسنج</a>";
?>
