<?php
/**
 * إصلاح سريع لجدول المنتجات - إضافة أعمدة المخزون
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

echo "<h2>🔧 إصلاح سريع لجدول المنتجات</h2>";

$results = [];
$errors = [];

try {
    echo "<h3>1. فحص الأعمدة الموجودة:</h3>";
    
    // الحصول على الأعمدة الموجودة
    $columns = $database->fetchAll("SHOW COLUMNS FROM products");
    $existingColumns = array_column($columns, 'Field');
    
    echo "<p>الأعمدة الموجودة: " . implode(', ', $existingColumns) . "</p>";
    
    echo "<h3>2. إضافة الأعمدة المفقودة:</h3>";
    
    // قائمة الأعمدة المطلوبة
    $requiredColumns = [
        'stock_quantity' => [
            'sql' => 'ALTER TABLE products ADD COLUMN stock_quantity INT DEFAULT 10',
            'description' => 'كمية المخزون'
        ],
        'min_stock_level' => [
            'sql' => 'ALTER TABLE products ADD COLUMN min_stock_level INT DEFAULT 5',
            'description' => 'الحد الأدنى للمخزون'
        ],
        'location' => [
            'sql' => 'ALTER TABLE products ADD COLUMN location VARCHAR(100) DEFAULT NULL',
            'description' => 'موقع المنتج'
        ],
        'sku' => [
            'sql' => 'ALTER TABLE products ADD COLUMN sku VARCHAR(50) DEFAULT NULL',
            'description' => 'رمز المنتج'
        ]
    ];
    
    // إضافة الأعمدة المفقودة
    foreach ($requiredColumns as $columnName => $columnInfo) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $database->query($columnInfo['sql']);
                $results[] = "✅ تم إضافة عمود: {$columnInfo['description']} ($columnName)";
                echo "<p style='color: green;'>✅ تم إضافة عمود: {$columnInfo['description']} ($columnName)</p>";
            } catch (Exception $e) {
                $errors[] = "❌ فشل إضافة عمود $columnName: " . $e->getMessage();
                echo "<p style='color: red;'>❌ فشل إضافة عمود $columnName: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ العمود $columnName موجود بالفعل</p>";
        }
    }
    
    echo "<h3>3. تحديث البيانات الافتراضية:</h3>";
    
    // تحديث القيم الافتراضية للمنتجات الموجودة
    if (!in_array('stock_quantity', $existingColumns)) {
        try {
            $database->query("UPDATE products SET stock_quantity = 10 WHERE stock_quantity IS NULL OR stock_quantity = 0");
            echo "<p style='color: green;'>✅ تم تعيين كمية مخزون افتراضية (10) للمنتجات الموجودة</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في تحديث كمية المخزون: " . $e->getMessage() . "</p>";
        }
    }
    
    if (!in_array('min_stock_level', $existingColumns)) {
        try {
            $database->query("UPDATE products SET min_stock_level = 5 WHERE min_stock_level IS NULL OR min_stock_level = 0");
            echo "<p style='color: green;'>✅ تم تعيين حد أدنى افتراضي (5) للمنتجات الموجودة</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في تحديث الحد الأدنى: " . $e->getMessage() . "</p>";
        }
    }
    
    if (!in_array('sku', $existingColumns)) {
        try {
            // إنشاء SKU للمنتجات الموجودة
            $products = $database->fetchAll("SELECT id FROM products WHERE sku IS NULL OR sku = ''");
            foreach ($products as $product) {
                $sku = 'PRD-' . str_pad($product['id'], 4, '0', STR_PAD_LEFT);
                $database->update('products', ['sku' => $sku], 'id = :id', ['id' => $product['id']]);
            }
            echo "<p style='color: green;'>✅ تم إنشاء رموز SKU للمنتجات الموجودة (" . count($products) . " منتج)</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في إنشاء رموز SKU: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>4. اختبار التحديث:</h3>";
    
    // اختبار تحديث الحد الأدنى
    try {
        $testProduct = $database->fetch("SELECT id, name, min_stock_level FROM products LIMIT 1");
        
        if ($testProduct) {
            $originalValue = $testProduct['min_stock_level'];
            $testValue = $originalValue + 1;
            
            // تحديث القيمة
            $updateResult = $database->update('products', 
                ['min_stock_level' => $testValue], 
                'id = :id', 
                ['id' => $testProduct['id']]
            );
            
            if ($updateResult) {
                echo "<p style='color: green;'>✅ اختبار التحديث نجح!</p>";
                echo "<p>المنتج: " . htmlspecialchars($testProduct['name']) . "</p>";
                echo "<p>القيمة الأصلية: $originalValue → القيمة الجديدة: $testValue</p>";
                
                // إعادة القيمة الأصلية
                $database->update('products', 
                    ['min_stock_level' => $originalValue], 
                    'id = :id', 
                    ['id' => $testProduct['id']]
                );
                echo "<p><small>تم إعادة القيمة الأصلية</small></p>";
                
                $results[] = "✅ اختبار التحديث نجح";
            } else {
                $errors[] = "❌ فشل اختبار التحديث";
                echo "<p style='color: red;'>❌ فشل اختبار التحديث</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ لا توجد منتجات للاختبار</p>";
        }
        
    } catch (Exception $e) {
        $errors[] = "❌ خطأ في اختبار التحديث: " . $e->getMessage();
        echo "<p style='color: red;'>❌ خطأ في اختبار التحديث: " . $e->getMessage() . "</p>";
    }
    
    // تسجيل النشاط
    if (!empty($results)) {
        logActivity('products_table_fixed', "تم إصلاح جدول المنتجات: " . implode(', ', $results));
    }
    
} catch (Exception $e) {
    $errors[] = "❌ خطأ عام: " . $e->getMessage();
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
}

echo "<h3>5. النتيجة النهائية:</h3>";

if (empty($errors)) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎉 تم الإصلاح بنجاح!</h4>";
    echo "<p>جدول المنتجات الآن يحتوي على جميع الأعمدة المطلوبة</p>";
    
    if (!empty($results)) {
        echo "<h5>ما تم إنجازه:</h5>";
        echo "<ul>";
        foreach ($results as $result) {
            echo "<li>$result</li>";
        }
        echo "</ul>";
    }
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='simple-bulk-update.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تجربة التحديث المجمع</a>";
    echo "<a href='test-bulk-update.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار التحديث</a>";
    echo "<a href='inventory.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة المخزون</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>❌ حدثت أخطاء أثناء الإصلاح</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    
    echo "<h5>خطوات الإصلاح اليدوي:</h5>";
    echo "<ol>";
    echo "<li>تحقق من صلاحيات قاعدة البيانات</li>";
    echo "<li>تأكد من أن المستخدم له صلاحية ALTER TABLE</li>";
    echo "<li>جرب تشغيل الأوامر يدوياً في phpMyAdmin</li>";
    echo "<li>تحقق من سجل أخطاء MySQL</li>";
    echo "</ol>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='check-products-table.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>فحص مفصل للجدول</a>";
    echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تحديث قاعدة البيانات</a>";
    echo "</div>";
    echo "</div>";
}

echo "<h3>6. فحص نهائي للجدول:</h3>";

try {
    $finalColumns = $database->fetchAll("SHOW COLUMNS FROM products");
    $finalColumnNames = array_column($finalColumns, 'Field');
    
    echo "<p><strong>الأعمدة الموجودة الآن:</strong> " . implode(', ', $finalColumnNames) . "</p>";
    
    $requiredForInventory = ['stock_quantity', 'min_stock_level', 'location', 'sku'];
    $missingRequired = array_diff($requiredForInventory, $finalColumnNames);
    
    if (empty($missingRequired)) {
        echo "<p style='color: green; font-weight: bold;'>✅ جميع الأعمدة المطلوبة لنظام المخزون موجودة!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ الأعمدة المفقودة: " . implode(', ', $missingRequired) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الفحص النهائي: " . $e->getMessage() . "</p>";
}

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات:</h4>";
echo "<ul>";
echo "<li>هذا الإصلاح آمن ولا يحذف أي بيانات موجودة</li>";
echo "<li>يمكن تشغيله عدة مرات بأمان</li>";
echo "<li>القيم الافتراضية: stock_quantity = 10, min_stock_level = 5</li>";
echo "<li>رموز SKU تُنشأ تلقائياً بصيغة PRD-0001</li>";
echo "</ul>";
echo "</div>";
?>
