<?php
/**
 * تقرير المبيعات الشهرية لمراجعة الحد الأدنى
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// الحصول على الشهر المحدد
$selectedMonth = $_GET['month'] ?? date('Y-m');
$monthStart = $selectedMonth . '-01';
$monthEnd = date('Y-m-t', strtotime($monthStart));

// حساب الشهر السابق للمقارنة
$previousMonth = date('Y-m', strtotime($monthStart . ' -1 month'));
$prevMonthStart = $previousMonth . '-01';
$prevMonthEnd = date('Y-m-t', strtotime($prevMonthStart));

// التحقق من وجود جدول order_items
$orderItemsExists = false;
try {
    $database->fetch("SELECT 1 FROM order_items LIMIT 1");
    $orderItemsExists = true;
} catch (Exception $e) {
    // الجدول غير موجود
}

// بيانات المبيعات للشهر المحدد
$currentMonthSales = [];
if ($orderItemsExists) {
    try {
        $currentMonthSales = $database->fetchAll("
            SELECT
                p.id,
                p.name,
                p.stock_quantity,
                p.min_stock_level,
                p.price,
                c.name as category_name,
                COALESCE(SUM(oi.quantity), 0) as total_sold,
                COALESCE(SUM(oi.quantity * oi.price), 0) as total_revenue,
                COUNT(DISTINCT o.id) as order_count,
                COALESCE(AVG(oi.quantity), 0) as avg_per_order,
                MIN(o.created_at) as first_sale,
                MAX(o.created_at) as last_sale
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id AND o.created_at BETWEEN :month_start AND :month_end AND o.status IN ('confirmed', 'delivered')
            WHERE p.status = 'active'
            GROUP BY p.id
            ORDER BY total_sold DESC NULLS LAST
        ", [
            'month_start' => $monthStart . ' 00:00:00',
            'month_end' => $monthEnd . ' 23:59:59'
        ]);
    } catch (Exception $e) {
        // في حالة خطأ، استخدم بيانات المنتجات فقط
        $currentMonthSales = $database->fetchAll("
            SELECT
                p.id,
                p.name,
                p.stock_quantity,
                p.min_stock_level,
                p.price,
                c.name as category_name,
                0 as total_sold,
                0 as total_revenue,
                0 as order_count,
                0 as avg_per_order,
                NULL as first_sale,
                NULL as last_sale
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'active'
            ORDER BY p.name
        ");
    }
} else {
    // إذا لم يكن جدول order_items موجود، استخدم بيانات المنتجات فقط
    $currentMonthSales = $database->fetchAll("
        SELECT
            p.id,
            p.name,
            p.stock_quantity,
            p.min_stock_level,
            p.price,
            c.name as category_name,
            0 as total_sold,
            0 as total_revenue,
            0 as order_count,
            0 as avg_per_order,
            NULL as first_sale,
            NULL as last_sale
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = 'active'
        ORDER BY p.name
    ");
}

// بيانات المبيعات للشهر السابق للمقارنة
$previousMonthSales = [];
if ($orderItemsExists) {
    try {
        $previousMonthSales = $database->fetchAll("
            SELECT
                p.id,
                SUM(oi.quantity) as total_sold
            FROM products p
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id AND o.created_at BETWEEN :prev_start AND :prev_end
            WHERE p.status = 'active' AND (o.status IN ('confirmed', 'delivered') OR o.id IS NULL)
            GROUP BY p.id
        ", [
            'prev_start' => $prevMonthStart . ' 00:00:00',
            'prev_end' => $prevMonthEnd . ' 23:59:59'
        ]);
    } catch (Exception $e) {
        // في حالة خطأ، استخدم مصفوفة فارغة
        $previousMonthSales = [];
    }
}

// تحويل بيانات الشهر السابق إلى مصفوفة للوصول السريع
$prevSalesMap = [];
foreach ($previousMonthSales as $sale) {
    $prevSalesMap[$sale['id']] = $sale['total_sold'] ?? 0;
}

// إضافة بيانات المقارنة
foreach ($currentMonthSales as &$product) {
    $product['prev_month_sold'] = $prevSalesMap[$product['id']] ?? 0;
    $product['growth'] = 0;
    
    if ($product['prev_month_sold'] > 0) {
        $product['growth'] = (($product['total_sold'] - $product['prev_month_sold']) / $product['prev_month_sold']) * 100;
    } elseif ($product['total_sold'] > 0) {
        $product['growth'] = 100; // منتج جديد
    }
    
    // حساب التوصية للحد الأدنى
    $daysInMonth = date('t', strtotime($monthStart));
    $dailyAverage = ($product['total_sold'] ?? 0) / $daysInMonth;
    $recommendedMin = max(1, ceil($dailyAverage * 7)); // أسبوع كمخزون أمان
    
    // تعديل حسب النمو
    if ($product['growth'] > 50) {
        $recommendedMin = ceil($recommendedMin * 1.5); // زيادة 50% للمنتجات النامية
    } elseif ($product['growth'] < -30) {
        $recommendedMin = ceil($recommendedMin * 0.7); // تقليل 30% للمنتجات المتراجعة
    }
    
    $product['recommended_min'] = min($recommendedMin, $product['stock_quantity']);
    $product['daily_average'] = round($dailyAverage, 2);
}

// إحصائيات عامة
$totalProducts = count($currentMonthSales);
$productsWithSales = count(array_filter($currentMonthSales, function($p) { return $p['total_sold'] > 0; }));
$productsNoSales = $totalProducts - $productsWithSales;
$totalRevenue = array_sum(array_column($currentMonthSales, 'total_revenue'));

$pageTitle = "تقرير المبيعات الشهرية - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
    
    <style>
    .growth-positive { color: #28a745; }
    .growth-negative { color: #dc3545; }
    .growth-neutral { color: #6c757d; }
    .recommendation-different {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
    }
    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">📊 تقرير المبيعات الشهرية</h1>
                <div class="header-actions">
                    <form method="GET" class="d-flex align-items-center">
                        <label for="month" class="form-label me-2">الشهر:</label>
                        <input type="month" class="form-control me-2" id="month" name="month" 
                               value="<?php echo $selectedMonth; ?>" onchange="this.form.submit()">
                        <button type="button" class="btn btn-success btn-sm" onclick="exportReport()">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                    </form>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- No Data Warning -->
                <?php if (!$orderItemsExists): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> لا توجد بيانات مبيعات</h5>
                    <p>جدول عناصر الطلبات غير موجود أو فارغ. تحتاج لإنشاء بيانات تجريبية أو إضافة طلبات حقيقية لعرض تقرير المبيعات.</p>
                    <div class="mt-3">
                        <a href="create-sample-data.php" class="btn btn-warning me-2">
                            <i class="fas fa-plus"></i> إنشاء بيانات تجريبية
                        </a>
                        <a href="check-database-structure.php" class="btn btn-info">
                            <i class="fas fa-database"></i> فحص قاعدة البيانات
                        </a>
                    </div>
                </div>
                <?php elseif ($productsWithSales == 0): ?>
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> لا توجد مبيعات للشهر المحدد</h5>
                    <p>لا توجد مبيعات مسجلة للشهر <?php echo date('F Y', strtotime($selectedMonth)); ?>. جرب شهر آخر أو أضف بيانات تجريبية.</p>
                    <div class="mt-3">
                        <a href="create-sample-data.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة بيانات تجريبية
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $totalProducts; ?></h3>
                                <p class="mb-0">إجمالي المنتجات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo $productsWithSales; ?></h3>
                                <p class="mb-0">منتجات لها مبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h3><?php echo $productsNoSales; ?></h3>
                                <p class="mb-0">منتجات بدون مبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <h3><?php echo formatPrice($totalRevenue); ?></h3>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="admin-card">
                            <div class="card-header">
                                <h5 class="card-title">أفضل 10 منتجات مبيعاً</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="topProductsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="admin-card">
                            <div class="card-header">
                                <h5 class="card-title">توزيع المبيعات حسب الفئة</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="categorySalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Detailed Report -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">تقرير مفصل ومراجعة الحد الأدنى</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الفئة</th>
                                        <th>المبيعات الحالية</th>
                                        <th>المبيعات السابقة</th>
                                        <th>النمو %</th>
                                        <th>المتوسط اليومي</th>
                                        <th>المخزون الحالي</th>
                                        <th>الحد الأدنى الحالي</th>
                                        <th>الحد المقترح</th>
                                        <th>الإيرادات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($currentMonthSales as $product): ?>
                                    <?php
                                    $growthClass = 'growth-neutral';
                                    $growthIcon = 'fa-minus';
                                    if ($product['growth'] > 10) {
                                        $growthClass = 'growth-positive';
                                        $growthIcon = 'fa-arrow-up';
                                    } elseif ($product['growth'] < -10) {
                                        $growthClass = 'growth-negative';
                                        $growthIcon = 'fa-arrow-down';
                                    }
                                    
                                    $recommendationClass = '';
                                    if (abs($product['recommended_min'] - $product['min_stock_level']) > 2) {
                                        $recommendationClass = 'recommendation-different';
                                    }
                                    ?>
                                    <tr class="<?php echo $recommendationClass; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <span class="badge bg-primary fs-6">
                                                <?php echo $product['total_sold'] ?? 0; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?php echo $product['prev_month_sold']; ?>
                                            </span>
                                        </td>
                                        <td class="<?php echo $growthClass; ?>">
                                            <i class="fas <?php echo $growthIcon; ?>"></i>
                                            <?php echo number_format($product['growth'], 1); ?>%
                                        </td>
                                        <td><?php echo $product['daily_average']; ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo $product['stock_quantity']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                <?php echo $product['min_stock_level']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo $product['recommended_min']; ?>
                                            </span>
                                            <?php if (abs($product['recommended_min'] - $product['min_stock_level']) > 2): ?>
                                                <br><small class="text-warning">
                                                    <i class="fas fa-exclamation-triangle"></i> يحتاج مراجعة
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatPrice($product['total_revenue'] ?? 0); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="smart-stock-analysis.php" class="btn btn-primary w-100">
                                    <i class="fas fa-brain"></i><br>
                                    تحليل ذكي للمخزون
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="bulk-update-stock.php" class="btn btn-warning w-100">
                                    <i class="fas fa-edit"></i><br>
                                    تحديث مجمع للحد الأدنى
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="inventory.php" class="btn btn-success w-100">
                                    <i class="fas fa-warehouse"></i><br>
                                    إدارة المخزون
                                </a>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-info w-100" onclick="scheduleMonthlyReview()">
                                    <i class="fas fa-calendar-check"></i><br>
                                    جدولة مراجعة شهرية
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // رسم بياني لأفضل المنتجات
    const topProducts = <?php echo json_encode(array_slice(array_filter($currentMonthSales, function($p) { return $p['total_sold'] > 0; }), 0, 10)); ?>;
    
    const topProductsCtx = document.getElementById('topProductsChart').getContext('2d');
    new Chart(topProductsCtx, {
        type: 'bar',
        data: {
            labels: topProducts.map(p => p.name.substring(0, 20) + '...'),
            datasets: [{
                label: 'المبيعات',
                data: topProducts.map(p => p.total_sold),
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // رسم بياني للفئات
    const categoryData = {};
    <?php foreach ($currentMonthSales as $product): ?>
        <?php if ($product['total_sold'] > 0): ?>
            const category = '<?php echo addslashes($product['category_name'] ?? 'غير محدد'); ?>';
            if (!categoryData[category]) categoryData[category] = 0;
            categoryData[category] += <?php echo $product['total_sold']; ?>;
        <?php endif; ?>
    <?php endforeach; ?>

    const categorySalesCtx = document.getElementById('categorySalesChart').getContext('2d');
    new Chart(categorySalesCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(categoryData),
            datasets: [{
                data: Object.values(categoryData),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    function exportReport() {
        window.open('export-monthly-report.php?month=<?php echo $selectedMonth; ?>', '_blank');
    }

    function scheduleMonthlyReview() {
        alert('سيتم إضافة ميزة جدولة المراجعة الشهرية قريباً!\n\nنصيحة: اجعل يوم 1 من كل شهر موعداً لمراجعة الحد الأدنى.');
    }
    </script>
</body>
</html>
