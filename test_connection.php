<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Test Database Connection
 */

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    require_once 'includes/functions.php';

    echo "✅ تم تحميل الملفات بنجاح<br>";

    // اختبار الاتصال بقاعدة البيانات
    $testQuery = $database->fetch("SELECT COUNT(*) as count FROM users");
    echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
    echo "عدد المستخدمين: " . $testQuery['count'] . "<br>";

    // اختبار جدول الإعدادات
    $siteName = getSiteSetting('site_name', 'اسم افتراضي');
    echo "✅ جدول الإعدادات يعمل<br>";
    echo "اسم الموقع: " . $siteName . "<br>";

    // اختبار دالة تشفير كلمة المرور
    $testPassword = hashPassword('password');
    echo "✅ دالة تشفير كلمة المرور تعمل<br>";

    // اختبار التحقق من كلمة المرور
    $isValid = verifyPassword('password', $testPassword);
    echo "✅ دالة التحقق من كلمة المرور تعمل: " . ($isValid ? 'صحيح' : 'خطأ') . "<br>";

    // اختبار المستخدم الإداري
    $adminUser = $database->fetch("SELECT * FROM users WHERE username = 'admin'");
    if ($adminUser) {
        echo "✅ المستخدم الإداري موجود<br>";
        echo "اسم المستخدم: " . $adminUser['username'] . "<br>";
        echo "الاسم الكامل: " . $adminUser['full_name'] . "<br>";
    } else {
        echo "❌ المستخدم الإداري غير موجود<br>";
    }

    echo "<br><h3>✅ جميع الاختبارات نجحت!</h3>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<p>اسم المستخدم: <code>admin</code></p>";
    echo "<p>كلمة المرور: <code>password</code></p>";
    echo "<br>";
    echo "<a href='admin/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل لصفحة تسجيل الدخول</a><br><br>";
    echo "<a href='update_admin_password.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث كلمة مرور المدير</a><br><br>";
    echo "<a href='index.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الصفحة الرئيسية</a>";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
    echo "<br><br>";
    echo "<h3>خطوات الإصلاح:</h3>";
    echo "<ol>";
    echo "<li>تأكد من استيراد ملف database.sql في phpMyAdmin</li>";
    echo "<li>تحقق من إعدادات قاعدة البيانات في config/database.php</li>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "</ol>";
}
?>
