<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Test Database Connection
 */

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    require_once 'includes/functions.php';
    
    echo "✅ تم تحميل الملفات بنجاح<br>";
    
    // اختبار الاتصال بقاعدة البيانات
    $testQuery = $database->fetch("SELECT COUNT(*) as count FROM users");
    echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
    echo "عدد المستخدمين: " . $testQuery['count'] . "<br>";
    
    // اختبار جدول الإعدادات
    $siteName = getSiteSetting('site_name', 'اسم افتراضي');
    echo "✅ جدول الإعدادات يعمل<br>";
    echo "اسم الموقع: " . $siteName . "<br>";
    
    // اختبار دالة تشفير كلمة المرور
    $testPassword = hashPassword('password');
    echo "✅ دالة تشفير كلمة المرور تعمل<br>";
    
    // اختبار التحقق من كلمة المرور
    $isValid = verifyPassword('password', $testPassword);
    echo "✅ دالة التحقق من كلمة المرور تعمل: " . ($isValid ? 'صحيح' : 'خطأ') . "<br>";
    
    echo "<br><h3>✅ جميع الاختبارات نجحت!</h3>";
    echo "<a href='admin/login.php'>انتقل لصفحة تسجيل الدخول</a><br>";
    echo "<a href='update_admin_password.php'>تحديث كلمة مرور المدير</a>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
