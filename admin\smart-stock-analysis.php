<?php
/**
 * تحليل ذكي للمخزون وتوصيات الحد الأدنى
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// معالجة تحديث الحد الأدنى بناءً على التوصيات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_recommendations'])) {
    $recommendations = $_POST['recommendations'] ?? [];
    $updatedCount = 0;
    
    foreach ($recommendations as $productId => $newMinStock) {
        if (is_numeric($newMinStock) && $newMinStock >= 0) {
            $database->update('products', 
                ['min_stock_level' => intval($newMinStock), 'updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $productId]
            );
            
            $productName = $database->fetch("SELECT name FROM products WHERE id = :id", ['id' => $productId])['name'];
            logActivity('smart_min_stock_update', "تم تحديث الحد الأدنى للمنتج: {$productName} إلى {$newMinStock} (توصية ذكية)");
            
            $updatedCount++;
        }
    }
    
    $message = "تم تطبيق التوصيات على {$updatedCount} منتج بنجاح";
    $messageType = 'success';
}

// حساب إحصائيات البيع للشهر الماضي
$lastMonth = date('Y-m-d', strtotime('-30 days'));
$salesData = [];

// التحقق من وجود جدول order_items
$orderItemsExists = false;
try {
    $database->fetch("SELECT 1 FROM order_items LIMIT 1");
    $orderItemsExists = true;
} catch (Exception $e) {
    $orderItemsExists = false;
}

if ($orderItemsExists) {
    try {
        $salesData = $database->fetchAll("
            SELECT
                oi.product_id,
                p.name,
                p.stock_quantity,
                p.min_stock_level,
                p.price,
                SUM(oi.quantity) as total_sold,
                COUNT(DISTINCT o.id) as order_count,
                AVG(oi.quantity) as avg_per_order,
                MAX(oi.created_at) as last_sold
            FROM order_items oi
            JOIN orders o ON oi.order_id = o.id
            JOIN products p ON oi.product_id = p.id
            WHERE o.created_at >= :last_month
            AND o.status IN ('confirmed', 'delivered')
            AND p.status = 'active'
            GROUP BY oi.product_id
            ORDER BY total_sold DESC
        ", ['last_month' => $lastMonth]);
    } catch (Exception $e) {
        // في حالة خطأ، استخدم بيانات المنتجات فقط
        $salesData = [];
    }
}

// إذا لم توجد بيانات مبيعات، استخدم بيانات المنتجات مع قيم افتراضية
if (empty($salesData)) {
    $allProducts = $database->fetchAll("
        SELECT
            p.id as product_id,
            p.name,
            p.stock_quantity,
            p.min_stock_level,
            p.price,
            0 as total_sold,
            0 as order_count,
            0 as avg_per_order,
            NULL as last_sold
        FROM products p
        WHERE p.status = 'active'
        ORDER BY p.name
    ");

    // إضافة بيانات تجريبية للتحليل
    foreach ($allProducts as &$product) {
        // محاكاة مبيعات بناءً على السعر (المنتجات الأرخص تباع أكثر)
        if ($product['price'] < 50) {
            $product['total_sold'] = rand(5, 15);
        } elseif ($product['price'] < 100) {
            $product['total_sold'] = rand(2, 8);
        } else {
            $product['total_sold'] = rand(1, 5);
        }
        $product['order_count'] = ceil($product['total_sold'] / 2);
        $product['avg_per_order'] = $product['total_sold'] / max(1, $product['order_count']);
    }

    $salesData = $allProducts;
}

// حساب التوصيات الذكية
$recommendations = [];
$currentMonth = date('n'); // الشهر الحالي
$isRamadan = ($currentMonth == 4 || $currentMonth == 5); // رمضان تقريباً
$isWinter = ($currentMonth >= 11 || $currentMonth <= 2); // الشتاء

foreach ($salesData as $product) {
    $dailyAverage = $product['total_sold'] / 30; // متوسط البيع اليومي
    $baseMinStock = ceil($dailyAverage * 7); // أسبوع كمخزون أمان
    
    // تعديل حسب الموسم
    $seasonalMultiplier = 1;
    
    // منتجات العسل والمناعة في الشتاء
    if ($isWinter && (stripos($product['name'], 'عسل') !== false || 
                      stripos($product['name'], 'جينسنج') !== false ||
                      stripos($product['name'], 'فيتامين') !== false)) {
        $seasonalMultiplier = 1.5;
    }
    
    // منتجات رمضان
    if ($isRamadan && (stripos($product['name'], 'تمر') !== false ||
                       stripos($product['name'], 'عسل') !== false ||
                       stripos($product['name'], 'حبة البركة') !== false)) {
        $seasonalMultiplier = 2;
    }
    
    // تصنيف المنتج حسب سرعة البيع
    $category = 'slow';
    $categoryMultiplier = 1;
    
    if ($product['total_sold'] >= 20) {
        $category = 'fast';
        $categoryMultiplier = 1.5;
    } elseif ($product['total_sold'] >= 10) {
        $category = 'medium';
        $categoryMultiplier = 1.2;
    }
    
    // حساب التوصية النهائية
    $recommendedMinStock = ceil($baseMinStock * $seasonalMultiplier * $categoryMultiplier);
    
    // التأكد من أن التوصية منطقية
    $recommendedMinStock = max(2, min($recommendedMinStock, $product['stock_quantity']));
    
    $recommendations[] = [
        'product_id' => $product['product_id'],
        'name' => $product['name'],
        'current_stock' => $product['stock_quantity'],
        'current_min' => $product['min_stock_level'],
        'recommended_min' => $recommendedMinStock,
        'total_sold' => $product['total_sold'],
        'daily_average' => round($dailyAverage, 2),
        'category' => $category,
        'seasonal_factor' => $seasonalMultiplier,
        'last_sold' => $product['last_sold'],
        'priority' => abs($recommendedMinStock - $product['min_stock_level'])
    ];
}

// ترتيب حسب الأولوية
usort($recommendations, function($a, $b) {
    return $b['priority'] - $a['priority'];
});

// المنتجات التي لم تُباع في الشهر الماضي
$noSalesProducts = [];

if ($orderItemsExists) {
    try {
        $noSalesProducts = $database->fetchAll("
            SELECT p.id, p.name, p.stock_quantity, p.min_stock_level, p.updated_at
            FROM products p
            WHERE p.status = 'active'
            AND p.id NOT IN (
                SELECT DISTINCT oi.product_id
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE o.created_at >= :last_month
                AND o.status IN ('confirmed', 'delivered')
            )
            ORDER BY p.min_stock_level DESC
        ", ['last_month' => $lastMonth]);
    } catch (Exception $e) {
        // في حالة خطأ، استخدم مصفوفة فارغة
        $noSalesProducts = [];
    }
} else {
    // إذا لم يكن هناك جدول مبيعات، اعتبر المنتجات عالية الحد الأدنى كمنتجات بدون مبيعات
    $noSalesProducts = $database->fetchAll("
        SELECT p.id, p.name, p.stock_quantity, p.min_stock_level, p.updated_at
        FROM products p
        WHERE p.status = 'active'
        AND p.min_stock_level > 10
        ORDER BY p.min_stock_level DESC
        LIMIT 10
    ");
}

$pageTitle = "تحليل ذكي للمخزون - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
    
    <style>
    .recommendation-card {
        border-left: 4px solid #007bff;
        transition: all 0.3s ease;
    }
    .recommendation-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .priority-high { border-left-color: #dc3545; }
    .priority-medium { border-left-color: #ffc107; }
    .priority-low { border-left-color: #28a745; }
    .seasonal-badge {
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
        color: white;
        border: none;
    }
    .trend-up { color: #28a745; }
    .trend-down { color: #dc3545; }
    .trend-stable { color: #6c757d; }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">🧠 تحليل ذكي للمخزون</h1>
                <div class="header-actions">
                    <span class="badge bg-info">آخر 30 يوم</span>
                    <?php if ($isRamadan): ?>
                        <span class="badge seasonal-badge">🌙 موسم رمضان</span>
                    <?php elseif ($isWinter): ?>
                        <span class="badge bg-primary">❄️ موسم الشتاء</span>
                    <?php endif; ?>
                </div>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Data Source Warning -->
                <?php if (!$orderItemsExists): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> تحليل محدود</h5>
                    <p>لا توجد بيانات مبيعات حقيقية. التحليل مبني على بيانات تجريبية. لتحليل أكثر دقة، أضف بيانات مبيعات حقيقية.</p>
                    <div class="mt-3">
                        <a href="create-sample-data.php" class="btn btn-warning me-2">
                            <i class="fas fa-plus"></i> إنشاء بيانات مبيعات تجريبية
                        </a>
                        <a href="fix-sales-report.php" class="btn btn-info">
                            <i class="fas fa-wrench"></i> إصلاح نظام المبيعات
                        </a>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Messages -->
                <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Summary Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <h3><?php echo count($recommendations); ?></h3>
                                <p class="mb-0">منتجات لها مبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark">
                            <div class="card-body text-center">
                                <h3><?php echo count(array_filter($recommendations, function($r) { return $r['priority'] > 5; })); ?></h3>
                                <p class="mb-0">تحتاج مراجعة عاجلة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <h3><?php echo count($noSalesProducts); ?></h3>
                                <p class="mb-0">بدون مبيعات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <h3><?php echo array_sum(array_column($salesData, 'total_sold')); ?></h3>
                                <p class="mb-0">إجمالي المبيعات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Seasonal Insights -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">🌍 رؤى موسمية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الشهر الحالي: <?php echo date('F Y'); ?></h6>
                                <?php if ($isRamadan): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-moon"></i> <strong>موسم رمضان:</strong>
                                        زيادة الطلب على التمر، العسل، وحبة البركة بنسبة 100%
                                    </div>
                                <?php elseif ($isWinter): ?>
                                    <div class="alert alert-primary">
                                        <i class="fas fa-snowflake"></i> <strong>موسم الشتاء:</strong>
                                        زيادة الطلب على منتجات المناعة والعسل بنسبة 50%
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-secondary">
                                        <i class="fas fa-sun"></i> <strong>موسم عادي:</strong>
                                        معدلات بيع طبيعية
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <h6>توصيات موسمية:</h6>
                                <ul class="list-unstyled">
                                    <?php if ($isRamadan): ?>
                                        <li>✅ زيادة مخزون التمر والعسل</li>
                                        <li>✅ تحضير منتجات الإفطار</li>
                                        <li>✅ مراقبة حبة البركة</li>
                                    <?php elseif ($isWinter): ?>
                                        <li>✅ زيادة منتجات المناعة</li>
                                        <li>✅ تحضير الزيوت الطبيعية</li>
                                        <li>✅ مراقبة الجينسنج</li>
                                    <?php else: ?>
                                        <li>✅ مراجعة دورية للمخزون</li>
                                        <li>✅ تحضير للموسم القادم</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Smart Recommendations -->
                <div class="admin-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title">🎯 توصيات ذكية للحد الأدنى</h5>
                        <div>
                            <button type="button" class="btn btn-info btn-sm" onclick="selectAllRecommendations()">
                                <i class="fas fa-check-double"></i> تحديد الكل
                            </button>
                            <button type="button" class="btn btn-warning btn-sm" onclick="selectHighPriority()">
                                <i class="fas fa-exclamation-triangle"></i> الأولوية العالية فقط
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="recommendationsForm">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>تطبيق</th>
                                            <th>المنتج</th>
                                            <th>المبيعات (30 يوم)</th>
                                            <th>المتوسط اليومي</th>
                                            <th>الحد الحالي</th>
                                            <th>الحد المقترح</th>
                                            <th>التصنيف</th>
                                            <th>الأولوية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recommendations as $rec): ?>
                                        <?php 
                                        $priorityClass = '';
                                        $priorityText = '';
                                        if ($rec['priority'] > 10) {
                                            $priorityClass = 'priority-high';
                                            $priorityText = 'عالية';
                                        } elseif ($rec['priority'] > 5) {
                                            $priorityClass = 'priority-medium';
                                            $priorityText = 'متوسطة';
                                        } else {
                                            $priorityClass = 'priority-low';
                                            $priorityText = 'منخفضة';
                                        }
                                        
                                        $categoryBadge = '';
                                        switch($rec['category']) {
                                            case 'fast':
                                                $categoryBadge = '<span class="badge bg-danger">سريع</span>';
                                                break;
                                            case 'medium':
                                                $categoryBadge = '<span class="badge bg-warning text-dark">متوسط</span>';
                                                break;
                                            case 'slow':
                                                $categoryBadge = '<span class="badge bg-secondary">بطيء</span>';
                                                break;
                                        }
                                        ?>
                                        <tr class="recommendation-card <?php echo $priorityClass; ?>">
                                            <td>
                                                <input type="checkbox" class="form-check-input recommendation-checkbox" 
                                                       name="apply_recommendation[]" value="<?php echo $rec['product_id']; ?>"
                                                       data-priority="<?php echo $rec['priority']; ?>">
                                                <input type="hidden" name="recommendations[<?php echo $rec['product_id']; ?>]" 
                                                       value="<?php echo $rec['recommended_min']; ?>">
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($rec['name']); ?></strong>
                                                <br><small class="text-muted">المخزون: <?php echo $rec['current_stock']; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-success fs-6"><?php echo $rec['total_sold']; ?></span>
                                            </td>
                                            <td><?php echo $rec['daily_average']; ?></td>
                                            <td>
                                                <span class="badge bg-secondary"><?php echo $rec['current_min']; ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary fs-6"><?php echo $rec['recommended_min']; ?></span>
                                                <?php if ($rec['seasonal_factor'] > 1): ?>
                                                    <br><small class="text-info">عامل موسمي: ×<?php echo $rec['seasonal_factor']; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo $categoryBadge; ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo ($rec['priority'] > 10) ? 'danger' : (($rec['priority'] > 5) ? 'warning' : 'success'); ?>">
                                                    <?php echo $priorityText; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        التوصيات مبنية على بيانات المبيعات الفعلية والعوامل الموسمية
                                    </small>
                                </div>
                                <div>
                                    <button type="submit" name="apply_recommendations" class="btn btn-success btn-lg">
                                        <i class="fas fa-magic"></i> تطبيق التوصيات المحددة
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Products with No Sales -->
                <?php if (!empty($noSalesProducts)): ?>
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">⚠️ منتجات بدون مبيعات (آخر 30 يوم)</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>توصية:</strong> فكر في تقليل الحد الأدنى لهذه المنتجات أو إعادة تقييم استراتيجية التسويق
                        </div>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>المخزون الحالي</th>
                                        <th>الحد الأدنى الحالي</th>
                                        <th>التوصية</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($noSalesProducts as $product): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($product['name']); ?></td>
                                        <td><?php echo $product['stock_quantity']; ?></td>
                                        <td><?php echo $product['min_stock_level']; ?></td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                تقليل إلى <?php echo max(1, floor($product['min_stock_level'] / 2)); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function selectAllRecommendations() {
        document.querySelectorAll('.recommendation-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
    }
    
    function selectHighPriority() {
        document.querySelectorAll('.recommendation-checkbox').forEach(checkbox => {
            const priority = parseInt(checkbox.dataset.priority);
            checkbox.checked = priority > 5;
        });
    }
    
    // تحديد التوصيات تلقائياً عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        selectHighPriority();
    });
    </script>
</body>
</html>
