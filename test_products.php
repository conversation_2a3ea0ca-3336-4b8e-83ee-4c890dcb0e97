<?php
/**
 * اختبار المنتجات
 * Test Products
 */

require_once 'includes/functions.php';

echo "<h2>اختبار المنتجات</h2>";

try {
    // اختبار عدد الأقسام
    $categoriesCount = $database->fetch("SELECT COUNT(*) as count FROM categories");
    echo "<h3>الأقسام:</h3>";
    echo "عدد الأقسام: " . $categoriesCount['count'] . "<br>";
    
    if ($categoriesCount['count'] > 0) {
        $categories = $database->fetchAll("SELECT * FROM categories ORDER BY id");
        foreach ($categories as $category) {
            echo "- " . $category['name'] . " (ID: " . $category['id'] . ")<br>";
        }
    }
    
    // اختبار عدد المنتجات
    $productsCount = $database->fetch("SELECT COUNT(*) as count FROM products");
    echo "<br><h3>المنتجات:</h3>";
    echo "عدد المنتجات: " . $productsCount['count'] . "<br>";
    
    if ($productsCount['count'] > 0) {
        $products = $database->fetchAll("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id ORDER BY p.id LIMIT 10");
        foreach ($products as $product) {
            echo "- " . $product['name'] . " (القسم: " . $product['category_name'] . ", السعر: " . $product['price'] . " ريال)<br>";
        }
    } else {
        echo "<p style='color: red;'>❌ لا توجد منتجات في قاعدة البيانات</p>";
        echo "<p><a href='add_sample_data.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إضافة بيانات تجريبية</a></p>";
    }
    
    // اختبار ProductManager
    echo "<br><h3>اختبار ProductManager:</h3>";
    
    // اختبار المنتجات المميزة
    $featuredProducts = $productManager->getFeaturedProducts(5);
    echo "عدد المنتجات المميزة: " . count($featuredProducts) . "<br>";
    
    if (!empty($featuredProducts)) {
        echo "المنتجات المميزة:<br>";
        foreach ($featuredProducts as $product) {
            echo "- " . $product['name'] . " (" . formatPrice($product['price']) . ")<br>";
        }
    }
    
    // اختبار CategoryManager
    echo "<br><h3>اختبار CategoryManager:</h3>";
    
    $mainCategories = $categoryManager->getMainCategories();
    echo "عدد الأقسام الرئيسية: " . count($mainCategories) . "<br>";
    
    if (!empty($mainCategories)) {
        echo "الأقسام الرئيسية:<br>";
        foreach ($mainCategories as $category) {
            $productsInCategory = $productManager->getProductsByCategory($category['id'], 1, 5);
            echo "- " . $category['name'] . " (عدد المنتجات: " . count($productsInCategory) . ")<br>";
        }
    }
    
    echo "<br><h3>الروابط:</h3>";
    echo "<a href='add_sample_data.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة بيانات تجريبية</a>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a>";
    echo "<a href='products.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المنتجات</a>";
    echo "<a href='admin/' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage();
}
?>
