<?php
/**
 * اختبار إجراءات لوحة التحكم
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$message = '';
$messageType = 'info';

// اختبار تحديث حالة طلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_order_update'])) {
    try {
        $orderId = intval($_POST['order_id']);
        $newStatus = $_POST['new_status'];
        
        // التحقق من وجود الطلب
        $order = $database->fetch("SELECT order_number, status FROM orders WHERE id = :id", ['id' => $orderId]);
        if (!$order) {
            throw new Exception('الطلب غير موجود');
        }
        
        // تحديث الحالة
        $updateResult = $database->update('orders', 
            ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')], 
            'id = :id', 
            ['id' => $orderId]
        );
        
        if ($updateResult) {
            logActivity('test_order_status_updated', "اختبار تحديث حالة الطلب {$order['order_number']} من {$order['status']} إلى {$newStatus}");
            $message = "✅ تم تحديث حالة الطلب {$order['order_number']} بنجاح من {$order['status']} إلى {$newStatus}";
            $messageType = 'success';
        } else {
            throw new Exception('فشل في تحديث قاعدة البيانات');
        }
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث الطلب: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// اختبار تحديث منتج
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_product_update'])) {
    try {
        $productId = intval($_POST['product_id']);
        $newMinStock = intval($_POST['new_min_stock']);
        
        // التحقق من وجود المنتج
        $product = $database->fetch("SELECT name, min_stock_level FROM products WHERE id = :id", ['id' => $productId]);
        if (!$product) {
            throw new Exception('المنتج غير موجود');
        }
        
        // تحديث الحد الأدنى
        $updateResult = $database->update('products', 
            ['min_stock_level' => $newMinStock, 'updated_at' => date('Y-m-d H:i:s')], 
            'id = :id', 
            ['id' => $productId]
        );
        
        if ($updateResult) {
            logActivity('test_product_min_stock_updated', "اختبار تحديث الحد الأدنى للمنتج {$product['name']} من {$product['min_stock_level']} إلى {$newMinStock}");
            $message = "✅ تم تحديث الحد الأدنى للمنتج {$product['name']} بنجاح من {$product['min_stock_level']} إلى {$newMinStock}";
            $messageType = 'success';
        } else {
            throw new Exception('فشل في تحديث قاعدة البيانات');
        }
        
    } catch (Exception $e) {
        $message = "❌ خطأ في تحديث المنتج: " . $e->getMessage();
        $messageType = 'danger';
    }
}

echo "<h2>🧪 اختبار إجراءات لوحة التحكم</h2>";

// عرض الرسائل
if ($message) {
    echo "<div style='padding: 15px; margin: 20px 0; border-radius: 5px; background: " . 
         ($messageType == 'success' ? '#d4edda; color: #155724;' : '#f8d7da; color: #721c24;') . "'>";
    echo $message;
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔍 فحص حالة النظام:</h3>";

// فحص الجداول الأساسية
$tables = ['products', 'orders', 'categories'];
$tablesStatus = [];

foreach ($tables as $table) {
    try {
        $count = $database->fetch("SELECT COUNT(*) as count FROM $table")['count'];
        $tablesStatus[$table] = ['exists' => true, 'count' => $count];
        echo "<p style='color: green;'>✅ جدول $table موجود - عدد السجلات: $count</p>";
    } catch (Exception $e) {
        $tablesStatus[$table] = ['exists' => false, 'count' => 0];
        echo "<p style='color: red;'>❌ جدول $table غير موجود أو به مشكلة</p>";
    }
}

// فحص جدول سجل الأنشطة
try {
    $activityCount = $database->fetch("SELECT COUNT(*) as count FROM activity_logs")['count'];
    echo "<p style='color: green;'>✅ جدول activity_logs موجود - عدد الأنشطة: $activityCount</p>";
    $tablesStatus['activity_logs'] = ['exists' => true, 'count' => $activityCount];
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ جدول activity_logs غير موجود</p>";
    $tablesStatus['activity_logs'] = ['exists' => false, 'count' => 0];
}

// فحص جدول عناصر الطلبات
try {
    $orderItemsCount = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
    echo "<p style='color: green;'>✅ جدول order_items موجود - عدد العناصر: $orderItemsCount</p>";
    $tablesStatus['order_items'] = ['exists' => true, 'count' => $orderItemsCount];
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ جدول order_items غير موجود</p>";
    $tablesStatus['order_items'] = ['exists' => false, 'count' => 0];
}

echo "</div>";

// اختبار تحديث الطلبات
if ($tablesStatus['orders']['exists'] && $tablesStatus['orders']['count'] > 0) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📋 اختبار تحديث حالة الطلبات:</h3>";
    
    $testOrders = $database->fetchAll("SELECT id, order_number, status FROM orders ORDER BY created_at DESC LIMIT 5");
    
    if (!empty($testOrders)) {
        echo "<p>اختر طلب لاختبار تحديث حالته:</p>";
        
        foreach ($testOrders as $order) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: white;'>";
            echo "<h5>طلب: {$order['order_number']}</h5>";
            echo "<p>الحالة الحالية: <strong>{$order['status']}</strong></p>";
            
            echo "<form method='POST' style='display: inline-block; margin: 5px;'>";
            echo "<input type='hidden' name='order_id' value='{$order['id']}'>";
            echo "<select name='new_status' style='padding: 5px; margin-right: 10px;'>";
            echo "<option value='pending'" . ($order['status'] == 'pending' ? ' selected' : '') . ">قيد الانتظار</option>";
            echo "<option value='confirmed'" . ($order['status'] == 'confirmed' ? ' selected' : '') . ">مؤكد</option>";
            echo "<option value='processing'" . ($order['status'] == 'processing' ? ' selected' : '') . ">قيد التحضير</option>";
            echo "<option value='shipped'" . ($order['status'] == 'shipped' ? ' selected' : '') . ">تم الشحن</option>";
            echo "<option value='delivered'" . ($order['status'] == 'delivered' ? ' selected' : '') . ">تم التوصيل</option>";
            echo "<option value='cancelled'" . ($order['status'] == 'cancelled' ? ' selected' : '') . ">ملغي</option>";
            echo "</select>";
            echo "<button type='submit' name='test_order_update' style='background: #007bff; color: white; padding: 5px 15px; border: none; border-radius: 3px; cursor: pointer;'>اختبار التحديث</button>";
            echo "</form>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>لا توجد طلبات للاختبار</p>";
        echo "<a href='test-orders.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء طلبات تجريبية</a>";
    }
    
    echo "</div>";
}

// اختبار تحديث المنتجات
if ($tablesStatus['products']['exists'] && $tablesStatus['products']['count'] > 0) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📦 اختبار تحديث الحد الأدنى للمنتجات:</h3>";
    
    $testProducts = $database->fetchAll("SELECT id, name, min_stock_level FROM products WHERE status = 'active' ORDER BY name LIMIT 5");
    
    if (!empty($testProducts)) {
        echo "<p>اختر منتج لاختبار تحديث الحد الأدنى:</p>";
        
        foreach ($testProducts as $product) {
            echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px; background: white;'>";
            echo "<h5>منتج: {$product['name']}</h5>";
            echo "<p>الحد الأدنى الحالي: <strong>{$product['min_stock_level']}</strong></p>";
            
            echo "<form method='POST' style='display: inline-block; margin: 5px;'>";
            echo "<input type='hidden' name='product_id' value='{$product['id']}'>";
            echo "<input type='number' name='new_min_stock' value='{$product['min_stock_level']}' min='1' max='100' style='padding: 5px; margin-right: 10px; width: 80px;'>";
            echo "<button type='submit' name='test_product_update' style='background: #28a745; color: white; padding: 5px 15px; border: none; border-radius: 3px; cursor: pointer;'>اختبار التحديث</button>";
            echo "</form>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>لا توجد منتجات للاختبار</p>";
    }
    
    echo "</div>";
}

// اختبار JavaScript
echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🖥️ اختبار JavaScript:</h3>";
echo "<p>اختبر دوال JavaScript المستخدمة في لوحة التحكم:</p>";

echo "<button onclick='testJavaScriptFunction()' style='background: #6f42c1; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;'>اختبار دالة JavaScript</button>";

echo "<button onclick='testAjaxCall()' style='background: #fd7e14; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px;'>اختبار AJAX</button>";

echo "<div id='jsTestResult' style='margin-top: 15px; padding: 10px; background: white; border-radius: 5px; display: none;'></div>";

echo "</div>";

// روابط سريعة للاختبار
echo "<div style='background: #d1ecf1; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🔗 روابط الاختبار السريع:</h3>";

echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";

echo "<a href='orders.php' style='background: #007bff; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>";
echo "<i class='fas fa-shopping-cart' style='font-size: 24px; display: block; margin-bottom: 10px;'></i>";
echo "<strong>إدارة الطلبات</strong><br><small>اختبار تحديث الحالات</small>";
echo "</a>";

echo "<a href='simple-bulk-update.php' style='background: #28a745; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>";
echo "<i class='fas fa-edit' style='font-size: 24px; display: block; margin-bottom: 10px;'></i>";
echo "<strong>التحديث المجمع</strong><br><small>اختبار تحديث المنتجات</small>";
echo "</a>";

echo "<a href='activity-logs.php' style='background: #17a2b8; color: white; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>";
echo "<i class='fas fa-history' style='font-size: 24px; display: block; margin-bottom: 10px;'></i>";
echo "<strong>سجل الأنشطة</strong><br><small>مراجعة العمليات</small>";
echo "</a>";

echo "<a href='test-orders.php' style='background: #ffc107; color: black; padding: 15px; text-decoration: none; border-radius: 8px; text-align: center; display: block;'>";
echo "<i class='fas fa-flask' style='font-size: 24px; display: block; margin-bottom: 10px;'></i>";
echo "<strong>اختبار الطلبات</strong><br><small>إنشاء بيانات تجريبية</small>";
echo "</a>";

echo "</div>";
echo "</div>";

// ملاحظات مهمة
echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li><strong>اختبار التحديث:</strong> جرب تحديث حالة طلب أو حد أدنى لمنتج</li>";
echo "<li><strong>مراجعة السجل:</strong> تحقق من سجل الأنشطة بعد كل عملية</li>";
echo "<li><strong>JavaScript:</strong> تأكد من عمل دوال JavaScript في المتصفح</li>";
echo "<li><strong>AJAX:</strong> اختبر تحميل تفاصيل الطلب بـ AJAX</li>";
echo "<li><strong>رسائل الخطأ:</strong> لاحظ رسائل النجاح والخطأ</li>";
echo "</ul>";
echo "</div>";
?>

<script>
function testJavaScriptFunction() {
    const result = document.getElementById('jsTestResult');
    result.style.display = 'block';
    result.style.background = '#d4edda';
    result.style.color = '#155724';
    result.innerHTML = '✅ JavaScript يعمل بشكل صحيح! الوقت الحالي: ' + new Date().toLocaleString('ar-SA');
    
    // اختبار console.log
    console.log('اختبار JavaScript - الوقت:', new Date());
}

function testAjaxCall() {
    const result = document.getElementById('jsTestResult');
    result.style.display = 'block';
    result.innerHTML = '⏳ جاري اختبار AJAX...';
    
    // اختبار AJAX بسيط
    fetch('test-orders.php')
    .then(response => {
        if (response.ok) {
            result.style.background = '#d4edda';
            result.style.color = '#155724';
            result.innerHTML = '✅ AJAX يعمل بشكل صحيح! تم الاتصال بالخادم بنجاح.';
        } else {
            throw new Error('HTTP ' + response.status);
        }
    })
    .catch(error => {
        result.style.background = '#f8d7da';
        result.style.color = '#721c24';
        result.innerHTML = '❌ خطأ في AJAX: ' + error.message;
    });
}

// اختبار تحديث حالة الطلب (نفس الدالة المستخدمة في orders.php)
function updateOrderStatus(orderId, status) {
    console.log('اختبار تحديث حالة الطلب:', orderId, status);
    
    if (confirm('هل تريد تحديث حالة الطلب؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'update_status';
        form.appendChild(actionInput);
        
        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;
        form.appendChild(orderIdInput);
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        form.appendChild(statusInput);
        
        document.body.appendChild(form);
        console.log('إرسال النموذج...');
        form.submit();
    }
}

// تسجيل تحميل الصفحة
console.log('تم تحميل صفحة اختبار إجراءات لوحة التحكم');
</script>
