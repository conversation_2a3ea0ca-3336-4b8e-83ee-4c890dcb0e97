<?php
/**
 * القائمة الجانبية المشتركة لصفحات الإدارة
 */

// تحديد الصفحة الحالية
$currentPage = basename($_SERVER['PHP_SELF']);

// قائمة الصفحات مع معلوماتها
$menuItems = [
    'index.php' => [
        'title' => 'لوحة التحكم',
        'icon' => 'fas fa-tachometer-alt',
        'badge' => null
    ],
    'products.php' => [
        'title' => 'المنتجات',
        'icon' => 'fas fa-box',
        'badge' => null
    ],
    'add-product.php' => [
        'title' => 'إضافة منتج',
        'icon' => 'fas fa-plus-circle',
        'badge' => null,
        'parent' => 'products.php'
    ],
    'categories.php' => [
        'title' => 'الأقسام',
        'icon' => 'fas fa-tags',
        'badge' => null
    ],
    'orders.php' => [
        'title' => 'الطلبات',
        'icon' => 'fas fa-shopping-cart',
        'badge' => null
    ],
    'inventory.php' => [
        'title' => 'إدارة المخزون',
        'icon' => 'fas fa-warehouse',
        'badge' => null
    ],
    'stock-movements.php' => [
        'title' => 'حركات المخزون',
        'icon' => 'fas fa-exchange-alt',
        'badge' => null,
        'parent' => 'inventory.php'
    ],
    'users.php' => [
        'title' => 'المستخدمين',
        'icon' => 'fas fa-users',
        'badge' => null
    ],
    'activity-logs.php' => [
        'title' => 'سجل الأنشطة',
        'icon' => 'fas fa-history',
        'badge' => null
    ],
    'settings.php' => [
        'title' => 'الإعدادات',
        'icon' => 'fas fa-cog',
        'badge' => null
    ]
];

// حساب الإحصائيات للشارات
try {
    // عدد الطلبات الجديدة
    $newOrdersCount = $database->fetch("SELECT COUNT(*) as count FROM orders WHERE status = 'pending'")['count'] ?? 0;
    if ($newOrdersCount > 0) {
        $menuItems['orders.php']['badge'] = $newOrdersCount;
    }
    
    // عدد المنتجات منخفضة المخزون
    $lowStockCount = $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND stock_quantity <= min_stock_level")['count'] ?? 0;
    if ($lowStockCount > 0) {
        $menuItems['inventory.php']['badge'] = $lowStockCount;
    }
    
} catch (Exception $e) {
    // تجاهل الأخطاء في حساب الإحصائيات
}

// دالة لتحديد ما إذا كانت الصفحة نشطة
function isActivePage($page, $currentPage, $menuItems) {
    if ($page === $currentPage) {
        return true;
    }
    
    // التحقق من الصفحات الفرعية
    if (isset($menuItems[$currentPage]['parent']) && $menuItems[$currentPage]['parent'] === $page) {
        return true;
    }
    
    return false;
}
?>

<!-- Sidebar -->
<aside class="admin-sidebar">
    <div class="sidebar-header">
        <a href="index.php" class="sidebar-logo">
            <i class="fas fa-leaf"></i> G8 Admin
        </a>
    </div>
    
    <nav class="sidebar-nav">
        <ul class="sidebar-menu">
            <?php foreach ($menuItems as $page => $item): ?>
                <?php 
                // تخطي الصفحات الفرعية في القائمة الرئيسية
                if (isset($item['parent'])) continue;
                
                $isActive = isActivePage($page, $currentPage, $menuItems);
                $activeClass = $isActive ? 'active' : '';
                ?>
                <li>
                    <a href="<?php echo $page; ?>" class="<?php echo $activeClass; ?>">
                        <i class="<?php echo $item['icon']; ?>"></i> 
                        <?php echo $item['title']; ?>
                        <?php if ($item['badge']): ?>
                            <span class="badge badge-danger"><?php echo $item['badge']; ?></span>
                        <?php endif; ?>
                    </a>
                    
                    <?php 
                    // عرض القائمة الفرعية إذا كانت موجودة
                    $hasSubMenu = false;
                    foreach ($menuItems as $subPage => $subItem) {
                        if (isset($subItem['parent']) && $subItem['parent'] === $page) {
                            if (!$hasSubMenu) {
                                echo '<ul class="sidebar-submenu">';
                                $hasSubMenu = true;
                            }
                            
                            $subIsActive = $subPage === $currentPage ? 'active' : '';
                            echo '<li><a href="' . $subPage . '" class="' . $subIsActive . '">';
                            echo '<i class="' . $subItem['icon'] . '"></i> ' . $subItem['title'];
                            if ($subItem['badge']) {
                                echo '<span class="badge badge-danger">' . $subItem['badge'] . '</span>';
                            }
                            echo '</a></li>';
                        }
                    }
                    if ($hasSubMenu) {
                        echo '</ul>';
                    }
                    ?>
                </li>
            <?php endforeach; ?>
            
            <!-- روابط إضافية -->
            <li class="sidebar-divider"></li>
            
            <li>
                <a href="<?php echo SITE_URL; ?>" target="_blank">
                    <i class="fas fa-external-link-alt"></i> عرض الموقع
                </a>
            </li>
            
            <li>
                <a href="backup.php">
                    <i class="fas fa-download"></i> نسخ احتياطي
                </a>
            </li>
            
            <li>
                <a href="system-info.php">
                    <i class="fas fa-info-circle"></i> معلومات النظام
                </a>
            </li>
            
            <li class="sidebar-divider"></li>
            
            <li>
                <a href="logout.php" class="text-danger">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </a>
            </li>
        </ul>
    </nav>
    
    <!-- معلومات المستخدم -->
    <div class="sidebar-footer">
        <div class="user-info">
            <div class="user-avatar">
                <i class="fas fa-user-circle"></i>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo htmlspecialchars($_SESSION['username'] ?? 'مدير'); ?></div>
                <div class="user-role">مدير النظام</div>
            </div>
        </div>
    </div>
</aside>

<style>
/* تنسيقات إضافية للقائمة الجانبية */
.sidebar-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(0,0,0,0.1);
}

.sidebar-submenu li {
    margin: 0;
}

.sidebar-submenu a {
    padding: 8px 20px 8px 50px;
    font-size: 14px;
    color: rgba(255,255,255,0.8);
}

.sidebar-submenu a:hover,
.sidebar-submenu a.active {
    background: rgba(255,255,255,0.1);
    color: #fff;
}

.sidebar-divider {
    height: 1px;
    background: rgba(255,255,255,0.1);
    margin: 10px 0;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px;
    background: rgba(0,0,0,0.2);
    border-top: 1px solid rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    align-items: center;
    color: #fff;
}

.user-avatar {
    font-size: 24px;
    margin-left: 10px;
    color: rgba(255,255,255,0.8);
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: bold;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: rgba(255,255,255,0.7);
}

.badge {
    display: inline-block;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 10px;
    margin-right: 5px;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-success {
    background-color: #28a745;
}

.badge-info {
    background-color: #17a2b8;
}

.text-danger {
    color: #dc3545 !important;
}

.text-danger:hover {
    color: #c82333 !important;
}
</style>
