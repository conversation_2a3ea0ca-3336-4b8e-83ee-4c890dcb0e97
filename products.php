<?php
/**
 * صفحة المنتجات
 * Products Page
 */

require_once 'includes/functions.php';

// معاملات البحث والتصفية
$categoryId = isset($_GET['category']) ? intval($_GET['category']) : null;
$searchQuery = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

// الحصول على المنتجات
$products = [];
$totalProducts = 0;

if ($searchQuery) {
    $products = $productManager->searchProducts($searchQuery, $itemsPerPage, $offset);
    $totalProducts = count($productManager->searchProducts($searchQuery));
    $pageTitle = "نتائج البحث عن: " . $searchQuery;
} elseif ($categoryId) {
    $category = $categoryManager->getCategoryById($categoryId);
    if ($category) {
        $products = $productManager->getProductsByCategory($categoryId, $itemsPerPage, $offset);
        $totalProducts = $productManager->getProductsCount($categoryId);
        $pageTitle = $category['seo_title'] ?: $category['name'];
        $pageDescription = $category['seo_description'] ?: $category['description'];
        $pageKeywords = $category['seo_keywords'] ?: $category['name'];
    } else {
        $pageTitle = "قسم غير موجود";
    }
} else {
    $products = $productManager->getAllProducts('active', $itemsPerPage, $offset);
    $totalProducts = $productManager->getProductsCount();
    $pageTitle = "جميع المنتجات";
}

// حساب عدد الصفحات
$totalPages = ceil($totalProducts / $itemsPerPage);

// الحصول على الأقسام للقائمة الجانبية
$mainCategories = $categoryManager->getMainCategories();

// تسجيل النشاط
if ($searchQuery) {
    logActivity('search', "البحث عن: {$searchQuery}");
} elseif ($categoryId) {
    logActivity('category_view', "عرض قسم: {$categoryId}");
} else {
    logActivity('products_view', "عرض جميع المنتجات");
}

$pageTitle = $pageTitle ?: "المنتجات - " . getSiteSetting('site_name');
$pageDescription = $pageDescription ?: "تصفح مجموعتنا الواسعة من المنتجات الطبيعية عالية الجودة";
$pageKeywords = $pageKeywords ?: getSiteSetting('site_keywords');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="keywords" content="<?php echo $pageKeywords; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات..." value="<?php echo htmlspecialchars($searchQuery); ?>">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartManager->getCartCount(); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="navbar-nav d-flex flex-row">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link">الرئيسية</a>
                </li>
                <?php foreach ($mainCategories as $category): ?>
                <li class="nav-item dropdown">
                    <a href="products.php?category=<?php echo $category['id']; ?>" class="nav-link <?php echo ($categoryId == $category['id']) ? 'active' : ''; ?>">
                        <?php echo $category['name']; ?>
                    </a>
                    <?php 
                    $subCategories = $categoryManager->getSubCategories($category['id']);
                    if (!empty($subCategories)): 
                    ?>
                    <ul class="dropdown">
                        <?php foreach ($subCategories as $subCategory): ?>
                        <li><a href="products.php?category=<?php echo $subCategory['id']; ?>" class="<?php echo ($categoryId == $subCategory['id']) ? 'active' : ''; ?>"><?php echo $subCategory['name']; ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>
                </li>
                <?php endforeach; ?>
                <li class="nav-item">
                    <a href="contact.php" class="nav-link">اتصل بنا</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <?php if ($categoryId && isset($category)): ?>
                    <li class="breadcrumb-item active"><?php echo $category['name']; ?></li>
                <?php elseif ($searchQuery): ?>
                    <li class="breadcrumb-item active">نتائج البحث</li>
                <?php else: ?>
                    <li class="breadcrumb-item active">المنتجات</li>
                <?php endif; ?>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-lg-3 col-md-4 mb-4">
                    <div class="sidebar">
                        <!-- Categories Filter -->
                        <div class="filter-section mb-4">
                            <h5 class="filter-title">الأقسام</h5>
                            <ul class="category-list">
                                <li><a href="products.php" class="<?php echo (!$categoryId) ? 'active' : ''; ?>">جميع المنتجات</a></li>
                                <?php foreach ($mainCategories as $cat): ?>
                                <li>
                                    <a href="products.php?category=<?php echo $cat['id']; ?>" class="<?php echo ($categoryId == $cat['id']) ? 'active' : ''; ?>">
                                        <?php echo $cat['name']; ?>
                                    </a>
                                    <?php 
                                    $subCats = $categoryManager->getSubCategories($cat['id']);
                                    if (!empty($subCats)): 
                                    ?>
                                    <ul class="subcategory-list">
                                        <?php foreach ($subCats as $subCat): ?>
                                        <li><a href="products.php?category=<?php echo $subCat['id']; ?>" class="<?php echo ($categoryId == $subCat['id']) ? 'active' : ''; ?>"><?php echo $subCat['name']; ?></a></li>
                                        <?php endforeach; ?>
                                    </ul>
                                    <?php endif; ?>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="col-lg-9 col-md-8">
                    <!-- Page Header -->
                    <div class="page-header mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h1 class="page-title">
                                    <?php 
                                    if ($searchQuery) {
                                        echo "نتائج البحث عن: \"" . htmlspecialchars($searchQuery) . "\"";
                                    } elseif ($categoryId && isset($category)) {
                                        echo $category['name'];
                                    } else {
                                        echo "جميع المنتجات";
                                    }
                                    ?>
                                </h1>
                                <p class="text-muted">عرض <?php echo count($products); ?> من أصل <?php echo $totalProducts; ?> منتج</p>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="sort-options">
                                    <select class="form-select" onchange="sortProducts(this.value)">
                                        <option value="">ترتيب حسب</option>
                                        <option value="name_asc">الاسم (أ-ي)</option>
                                        <option value="name_desc">الاسم (ي-أ)</option>
                                        <option value="price_asc">السعر (الأقل أولاً)</option>
                                        <option value="price_desc">السعر (الأعلى أولاً)</option>
                                        <option value="newest">الأحدث</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <?php if (!empty($products)): ?>
                    <div class="products-grid">
                        <div class="row">
                            <?php foreach ($products as $product): ?>
                            <div class="col-lg-4 col-md-6 col-sm-6 mb-4">
                                <div class="product-card">
                                    <div class="product-image">
                                        <a href="product-details.php?id=<?php echo $product['id']; ?>">
                                            <?php if ($product['image']): ?>
                                            <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                                            <?php else: ?>
                                            <img src="<?php echo ASSETS_PATH; ?>/images/no-image.jpg" alt="<?php echo $product['name']; ?>">
                                            <?php endif; ?>
                                        </a>
                                        
                                        <?php if ($product['sale_price']): ?>
                                        <span class="product-badge">خصم</span>
                                        <?php endif; ?>
                                        
                                        <div class="product-overlay">
                                            <button onclick="addToCart(<?php echo $product['id']; ?>)" class="btn btn-primary btn-sm">
                                                <i class="fas fa-cart-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="product-info">
                                        <h3 class="product-title">
                                            <a href="product-details.php?id=<?php echo $product['id']; ?>"><?php echo $product['name']; ?></a>
                                        </h3>
                                        <p class="product-category"><?php echo $product['category_name']; ?></p>
                                        <p class="product-description"><?php echo truncateText($product['short_description'], 80); ?></p>
                                        
                                        <div class="product-price">
                                            <div>
                                                <?php if ($product['sale_price']): ?>
                                                <span class="price"><?php echo formatPrice($product['sale_price']); ?></span>
                                                <span class="old-price"><?php echo formatPrice($product['price']); ?></span>
                                                <?php else: ?>
                                                <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if ($product['weight']): ?>
                                            <small class="text-muted"><?php echo $product['weight']; ?></small>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="product-actions">
                                            <a href="product-details.php?id=<?php echo $product['id']; ?>" class="btn btn-outline">عرض التفاصيل</a>
                                            <button onclick="addToCart(<?php echo $product['id']; ?>)" class="btn btn-primary">أضف للسلة</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <nav aria-label="صفحات المنتجات" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                    
                    <?php else: ?>
                    <!-- No Products Found -->
                    <div class="no-products text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h3>لا توجد منتجات</h3>
                        <p class="text-muted">
                            <?php if ($searchQuery): ?>
                                لم نجد أي منتجات تطابق بحثك عن "<?php echo htmlspecialchars($searchQuery); ?>"
                            <?php elseif ($categoryId): ?>
                                لا توجد منتجات في هذا القسم حالياً
                            <?php else: ?>
                                لا توجد منتجات متاحة حالياً
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo SITE_URL; ?>" class="btn btn-primary">العودة للرئيسية</a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
    
    <script>
    function sortProducts(sortBy) {
        if (sortBy) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sortBy);
            url.searchParams.delete('page'); // Reset to first page
            window.location.href = url.toString();
        }
    }
    </script>
</body>
</html>
