<?php
/**
 * تحديث القائمة الجانبية في جميع صفحات الإدارة
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

echo "<h2>تحديث القائمة الجانبية في جميع صفحات الإدارة</h2>";

// قائمة الصفحات التي تحتاج تحديث
$adminPages = [
    'index.php' => 'لوحة التحكم',
    'categories.php' => 'إدارة الأقسام',
    'users.php' => 'إدارة المستخدمين',
    'settings.php' => 'الإعدادات',
    'add-product.php' => 'إضافة منتج',
    'edit-product.php' => 'تعديل منتج'
];

$updatedPages = [];
$errors = [];

foreach ($adminPages as $page => $description) {
    if (file_exists($page)) {
        try {
            $content = file_get_contents($page);
            
            // البحث عن القائمة الجانبية القديمة
            $oldSidebarPattern = '/<!-- Sidebar -->\s*<aside class="admin-sidebar">.*?<\/aside>/s';
            
            if (preg_match($oldSidebarPattern, $content)) {
                // استبدال القائمة الجانبية القديمة بالجديدة
                $newContent = preg_replace(
                    $oldSidebarPattern,
                    '<?php include \'includes/sidebar.php\'; ?>',
                    $content
                );
                
                if ($newContent && $newContent !== $content) {
                    file_put_contents($page, $newContent);
                    $updatedPages[] = $page;
                    echo "<p style='color: green;'>✅ تم تحديث $description ($page)</p>";
                } else {
                    echo "<p style='color: blue;'>ℹ️ $description ($page) لا يحتاج تحديث</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ لم يتم العثور على القائمة الجانبية في $description ($page)</p>";
            }
            
        } catch (Exception $e) {
            $errors[] = "خطأ في تحديث $page: " . $e->getMessage();
            echo "<p style='color: red;'>❌ خطأ في تحديث $description ($page): " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: gray;'>⚪ الملف $page غير موجود</p>";
    }
}

echo "<h3>ملخص التحديث:</h3>";

if (!empty($updatedPages)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ الصفحات المحدثة:</h4>";
    echo "<ul>";
    foreach ($updatedPages as $page) {
        echo "<li>$page</li>";
    }
    echo "</ul>";
    echo "</div>";
}

if (!empty($errors)) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ الأخطاء:</h4>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>$error</li>";
    }
    echo "</ul>";
    echo "</div>";
}

// فحص الصفحات المحدثة يدوياً
echo "<h3>الصفحات المحدثة يدوياً:</h3>";
$manuallyUpdated = [
    'products.php' => 'إدارة المنتجات',
    'orders.php' => 'إدارة الطلبات',
    'inventory.php' => 'إدارة المخزون',
    'stock-movements.php' => 'حركات المخزون'
];

foreach ($manuallyUpdated as $page => $description) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        if (strpos($content, "include 'includes/sidebar.php'") !== false) {
            echo "<p style='color: green;'>✅ $description ($page) محدث</p>";
        } else {
            echo "<p style='color: red;'>❌ $description ($page) غير محدث</p>";
        }
    } else {
        echo "<p style='color: gray;'>⚪ $page غير موجود</p>";
    }
}

echo "<h3>اختبار القائمة الجانبية:</h3>";

// التحقق من وجود ملف القائمة الجانبية
if (file_exists('includes/sidebar.php')) {
    echo "<p style='color: green;'>✅ ملف القائمة الجانبية موجود: includes/sidebar.php</p>";
    
    // فحص محتوى الملف
    $sidebarContent = file_get_contents('includes/sidebar.php');
    $requiredElements = [
        'admin-sidebar' => 'كلاس القائمة الجانبية',
        'sidebar-menu' => 'قائمة العناصر',
        'inventory.php' => 'رابط إدارة المخزون',
        'stock-movements.php' => 'رابط حركات المخزون',
        'activity-logs.php' => 'رابط سجل الأنشطة',
        'badge' => 'نظام الشارات'
    ];
    
    foreach ($requiredElements as $element => $description) {
        if (strpos($sidebarContent, $element) !== false) {
            echo "<p style='color: green;'>✅ $description موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ $description غير موجود</p>";
        }
    }
    
} else {
    echo "<p style='color: red;'>❌ ملف القائمة الجانبية غير موجود</p>";
}

echo "<h3>النتيجة النهائية:</h3>";

$allPagesUpdated = true;
$testPages = array_merge($adminPages, $manuallyUpdated);

foreach ($testPages as $page => $description) {
    if (file_exists($page)) {
        $content = file_get_contents($page);
        if (strpos($content, "include 'includes/sidebar.php'") === false) {
            $allPagesUpdated = false;
            break;
        }
    }
}

if ($allPagesUpdated && file_exists('includes/sidebar.php')) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎉 تم تحديث القائمة الجانبية بنجاح!</h4>";
    echo "<p>جميع صفحات الإدارة تستخدم الآن القائمة الجانبية الموحدة</p>";
    echo "<h5>الميزات الجديدة:</h5>";
    echo "<ul>";
    echo "<li>✅ قائمة موحدة لجميع الصفحات</li>";
    echo "<li>✅ شارات تفاعلية للطلبات والمخزون</li>";
    echo "<li>✅ قوائم فرعية منظمة</li>";
    echo "<li>✅ معلومات المستخدم في الأسفل</li>";
    echo "<li>✅ تصميم محسن ومتجاوب</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>❌ التحديث غير مكتمل</h4>";
    echo "<p>بعض الصفحات لم يتم تحديثها بعد</p>";
    echo "</div>";
}

echo "<h3>روابط الاختبار:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='test_sidebar.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار القائمة الجانبية</a>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "<a href='products.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>المنتجات</a>";
echo "<a href='inventory.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>المخزون</a>";
echo "<a href='orders.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الطلبات</a>";
echo "</div>";

echo "<h3>ملاحظات مهمة:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<ul>";
echo "<li>تأكد من أن مجلد <code>admin/includes/</code> موجود</li>";
echo "<li>تأكد من أن ملف <code>admin/includes/sidebar.php</code> موجود</li>";
echo "<li>تأكد من أن ملف CSS للإدارة محدث</li>";
echo "<li>اختبر جميع الصفحات للتأكد من عمل القائمة</li>";
echo "</ul>";
echo "</div>";
?>
