<?php
/**
 * تشخيص مشاكل المنتجات في لوحة التحكم
 * Debug Admin Products Issues
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص مشاكل المنتجات في لوحة التحكم</h2>";

try {
    require_once '../includes/functions.php';
    echo "✅ تم تحميل functions.php بنجاح<br>";
    
    // التحقق من تسجيل الدخول
    echo "<h3>1. التحقق من تسجيل الدخول:</h3>";
    if (isLoggedIn()) {
        echo "✅ المستخدم مسجل دخول<br>";
        echo "معرف المستخدم: " . $_SESSION['user_id'] . "<br>";
        echo "اسم المستخدم: " . ($_SESSION['user_name'] ?? 'غير محدد') . "<br>";
        echo "الدور: " . ($_SESSION['user_role'] ?? 'غير محدد') . "<br>";
        
        if (isAdmin()) {
            echo "✅ المستخدم لديه صلاحيات إدارية<br>";
        } else {
            echo "❌ المستخدم ليس لديه صلاحيات إدارية<br>";
            echo "<a href='login.php'>تسجيل دخول الإدارة</a><br>";
        }
    } else {
        echo "❌ المستخدم غير مسجل دخول<br>";
        echo "<a href='login.php'>تسجيل دخول الإدارة</a><br>";
    }
    
    // اختبار قاعدة البيانات
    echo "<h3>2. اختبار قاعدة البيانات:</h3>";
    $productsCount = $database->fetch("SELECT COUNT(*) as count FROM products");
    echo "عدد المنتجات في قاعدة البيانات: " . $productsCount['count'] . "<br>";
    
    if ($productsCount['count'] > 0) {
        echo "✅ توجد منتجات في قاعدة البيانات<br>";
        
        // عرض أول 5 منتجات
        $sampleProducts = $database->fetchAll("SELECT * FROM products LIMIT 5");
        echo "<strong>عينة من المنتجات:</strong><br>";
        foreach ($sampleProducts as $product) {
            echo "- " . $product['name'] . " (ID: " . $product['id'] . ", الحالة: " . $product['status'] . ")<br>";
        }
    } else {
        echo "❌ لا توجد منتجات في قاعدة البيانات<br>";
        echo "<a href='../add_sample_data.php'>إضافة بيانات تجريبية</a><br>";
    }
    
    // اختبار الاستعلام المستخدم في admin/products.php
    echo "<h3>3. اختبار استعلام لوحة التحكم:</h3>";
    
    // نسخ نفس الاستعلام من admin/products.php
    $itemsPerPage = ADMIN_ITEMS_PER_PAGE;
    $offset = 0;
    
    $sql = "SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            ORDER BY p.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $params = [
        'limit' => $itemsPerPage,
        'offset' => $offset
    ];
    
    echo "الاستعلام: " . $sql . "<br>";
    echo "المعاملات: limit=" . $itemsPerPage . ", offset=" . $offset . "<br>";
    
    try {
        $adminProducts = $database->fetchAll($sql, $params);
        echo "✅ الاستعلام نجح - عدد المنتجات المسترجعة: " . count($adminProducts) . "<br>";
        
        if (!empty($adminProducts)) {
            echo "<strong>المنتجات المسترجعة:</strong><br>";
            foreach ($adminProducts as $product) {
                echo "- " . $product['name'] . " (القسم: " . ($product['category_name'] ?? 'غير محدد') . ")<br>";
            }
        } else {
            echo "❌ الاستعلام لم يسترجع أي منتجات<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في الاستعلام: " . $e->getMessage() . "<br>";
    }
    
    // اختبار CategoryManager
    echo "<h3>4. اختبار CategoryManager:</h3>";
    if (isset($categoryManager)) {
        echo "✅ متغير categoryManager موجود<br>";
        
        try {
            $categories = $categoryManager->getAllCategories();
            echo "✅ دالة getAllCategories تعمل - عدد الأقسام: " . count($categories) . "<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في getAllCategories: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ متغير categoryManager غير موجود<br>";
    }
    
    // اختبار الثوابت
    echo "<h3>5. اختبار الثوابت:</h3>";
    echo "ADMIN_ITEMS_PER_PAGE: " . (defined('ADMIN_ITEMS_PER_PAGE') ? ADMIN_ITEMS_PER_PAGE : 'غير معرف') . "<br>";
    echo "ASSETS_PATH: " . (defined('ASSETS_PATH') ? ASSETS_PATH : 'غير معرف') . "<br>";
    echo "UPLOADS_URL: " . (defined('UPLOADS_URL') ? UPLOADS_URL : 'غير معرف') . "<br>";
    
    // اختبار دالة formatPrice
    echo "<h3>6. اختبار دالة formatPrice:</h3>";
    try {
        $testPrice = formatPrice(99.99);
        echo "✅ دالة formatPrice تعمل: " . $testPrice . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في formatPrice: " . $e->getMessage() . "<br>";
    }
    
    // اختبار دالة formatDate
    echo "<h3>7. اختبار دالة formatDate:</h3>";
    try {
        $testDate = formatDate(date('Y-m-d H:i:s'), 'd/m/Y');
        echo "✅ دالة formatDate تعمل: " . $testDate . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في formatDate: " . $e->getMessage() . "<br>";
    }
    
    echo "<h3>الحلول المقترحة:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ol>";
    
    if ($productsCount['count'] == 0) {
        echo "<li><strong>إضافة منتجات:</strong> <a href='../add_sample_data.php'>إضافة بيانات تجريبية</a></li>";
    }
    
    if (!isLoggedIn() || !isAdmin()) {
        echo "<li><strong>تسجيل دخول الإدارة:</strong> <a href='login.php'>تسجيل الدخول</a></li>";
    }
    
    echo "<li><strong>تجربة صفحة المنتجات:</strong> <a href='products.php'>admin/products.php</a></li>";
    echo "<li><strong>إضافة منتج جديد:</strong> <a href='add-product.php'>admin/add-product.php</a></li>";
    echo "<li><strong>العودة للتشخيص العام:</strong> <a href='../debug_products.php'>debug_products.php</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "<br>";
    echo "<br><strong>تفاصيل الخطأ:</strong><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
