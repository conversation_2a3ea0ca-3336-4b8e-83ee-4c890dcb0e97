<?php
/**
 * إصلاح مسارات الصور في قاعدة البيانات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<h2>إصلاح مسارات الصور في قاعدة البيانات</h2>";

// الحصول على جميع المنتجات التي لها صور
$products = $database->fetchAll("SELECT id, name, image, gallery FROM products WHERE image IS NOT NULL AND image != ''");

echo "<h3>1. فحص المنتجات الحالية:</h3>";
echo "<p>عدد المنتجات التي لها صور: " . count($products) . "</p>";

if (count($products) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>اسم المنتج</th><th>المسار الحالي</th><th>المسار الصحيح</th><th>الملف موجود؟</th><th>الحالة</th>";
    echo "</tr>";
    
    $needsFixing = [];
    
    foreach ($products as $product) {
        $currentPath = $product['image'];
        $correctPath = '';
        $fileExists = false;
        $status = '';
        
        // التحقق من المسار الحالي
        if (strpos($currentPath, 'products/') === 0) {
            // المسار صحيح بالفعل
            $correctPath = $currentPath;
            $status = 'صحيح ✅';
            $fileExists = file_exists(UPLOADS_PATH . '/' . $currentPath);
        } else {
            // المسار يحتاج إصلاح
            $correctPath = 'products/' . $currentPath;
            $status = 'يحتاج إصلاح ❌';
            
            // التحقق من وجود الملف في المكان الصحيح
            $fileExists = file_exists(UPLOADS_PATH . '/' . $correctPath);
            
            if ($fileExists) {
                $needsFixing[] = [
                    'id' => $product['id'],
                    'current' => $currentPath,
                    'correct' => $correctPath
                ];
            }
        }
        
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . htmlspecialchars($product['name']) . "</td>";
        echo "<td>" . htmlspecialchars($currentPath) . "</td>";
        echo "<td>" . htmlspecialchars($correctPath) . "</td>";
        echo "<td>" . ($fileExists ? 'نعم ✅' : 'لا ❌') . "</td>";
        echo "<td>" . $status . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>2. ملخص الإصلاح:</h3>";
    echo "<ul>";
    echo "<li><strong>إجمالي المنتجات:</strong> " . count($products) . "</li>";
    echo "<li><strong>تحتاج إصلاح:</strong> " . count($needsFixing) . "</li>";
    echo "</ul>";
    
    if (count($needsFixing) > 0) {
        echo "<h3>3. المنتجات التي تحتاج إصلاح:</h3>";
        echo "<ul>";
        foreach ($needsFixing as $item) {
            echo "<li>المنتج #" . $item['id'] . ": " . $item['current'] . " → " . $item['correct'] . "</li>";
        }
        echo "</ul>";
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>⚠️ هل تريد إصلاح المسارات؟</h4>";
        echo "<p>سيتم تحديث مسارات الصور في قاعدة البيانات لتشمل مجلد 'products/'</p>";
        echo "<a href='?fix=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إصلاح المسارات</a>";
        echo "<a href='?test=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار فقط</a>";
        echo "</div>";
        
        // تنفيذ الإصلاح
        if (isset($_GET['fix']) && $_GET['fix'] == '1') {
            echo "<h3>4. تنفيذ الإصلاح:</h3>";
            
            $fixedCount = 0;
            foreach ($needsFixing as $item) {
                $updateResult = $database->update(
                    'products', 
                    ['image' => $item['correct'], 'updated_at' => date('Y-m-d H:i:s')], 
                    'id = :id', 
                    ['id' => $item['id']]
                );
                
                if ($updateResult) {
                    echo "<p style='color: green;'>✅ تم إصلاح المنتج #" . $item['id'] . "</p>";
                    $fixedCount++;
                } else {
                    echo "<p style='color: red;'>❌ فشل في إصلاح المنتج #" . $item['id'] . "</p>";
                }
            }
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>✅ تم الإصلاح!</h4>";
            echo "<p>تم إصلاح " . $fixedCount . " من " . count($needsFixing) . " منتج</p>";
            echo "<a href='?' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>إعادة فحص</a>";
            echo "</div>";
        }
        
        // اختبار العرض
        if (isset($_GET['test']) && $_GET['test'] == '1') {
            echo "<h3>4. اختبار عرض الصور:</h3>";
            
            foreach ($needsFixing as $item) {
                echo "<h4>المنتج #" . $item['id'] . ":</h4>";
                
                $currentImageUrl = UPLOADS_URL . '/' . $item['current'];
                $correctImageUrl = UPLOADS_URL . '/' . $item['correct'];
                
                echo "<p><strong>المسار الحالي:</strong> <a href='$currentImageUrl' target='_blank'>$currentImageUrl</a></p>";
                echo "<img src='$currentImageUrl' alt='المسار الحالي' style='max-width: 150px; height: auto; border: 1px solid red; padding: 5px; margin: 5px;'>";
                
                echo "<p><strong>المسار الصحيح:</strong> <a href='$correctImageUrl' target='_blank'>$correctImageUrl</a></p>";
                echo "<img src='$correctImageUrl' alt='المسار الصحيح' style='max-width: 150px; height: auto; border: 1px solid green; padding: 5px; margin: 5px;'>";
                
                echo "<hr>";
            }
        }
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>✅ جميع المسارات صحيحة!</h4>";
        echo "<p>لا توجد مسارات تحتاج إصلاح</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ لا توجد منتجات بصور</h4>";
    echo "<p>لا توجد منتجات تحتوي على صور في قاعدة البيانات</p>";
    echo "</div>";
}

// اختبار دالة uploadFile المحدثة
echo "<h3>5. اختبار دالة uploadFile المحدثة:</h3>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])) {
    $file = $_FILES['test_image'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $uploadResult = uploadFile($file, 'products');
        
        if ($uploadResult['success']) {
            echo "<p style='color: green;'>✅ تم رفع الملف بنجاح</p>";
            echo "<ul>";
            echo "<li><strong>اسم الملف:</strong> " . $uploadResult['filename'] . "</li>";
            echo "<li><strong>المسار:</strong> " . $uploadResult['path'] . "</li>";
            echo "</ul>";
            
            $imageUrl = UPLOADS_URL . '/' . $uploadResult['filename'];
            echo "<p><strong>رابط الصورة:</strong> <a href='$imageUrl' target='_blank'>$imageUrl</a></p>";
            echo "<img src='$imageUrl' alt='اختبار' style='max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;'>";
            
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>✅ الآن المسار يتضمن 'products/' كما هو مطلوب!</strong></p>";
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ فشل في رفع الملف: " . $uploadResult['message'] . "</p>";
        }
    }
}

?>

<h4>اختبار رفع صورة جديدة:</h4>
<form method="POST" enctype="multipart/form-data" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
    <input type="file" name="test_image" accept="image/*" required>
    <button type="submit" style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 3px; margin-left: 10px;">
        اختبار الرفع
    </button>
</form>

<h3>6. روابط مفيدة:</h3>
<a href="products.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">قائمة المنتجات</a>
<a href="edit-product.php?id=3" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تعديل منتج</a>
<a href="test_image_update.php?id=3" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">اختبار التحديث</a>
