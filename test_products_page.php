<?php
/**
 * اختبار صفحة المنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/functions.php';

echo "<h2>اختبار صفحة المنتجات</h2>";

// اختبار الثوابت
echo "<h3>1. فحص الثوابت:</h3>";
echo "<ul>";
echo "<li><strong>ITEMS_PER_PAGE:</strong> " . (defined('ITEMS_PER_PAGE') ? ITEMS_PER_PAGE : 'غير معرف') . "</li>";
echo "<li><strong>UPLOADS_URL:</strong> " . (defined('UPLOADS_URL') ? UPLOADS_URL : 'غير معرف') . "</li>";
echo "<li><strong>ASSETS_PATH:</strong> " . (defined('ASSETS_PATH') ? ASSETS_PATH : 'غير معرف') . "</li>";
echo "<li><strong>SITE_URL:</strong> " . (defined('SITE_URL') ? SITE_URL : 'غير معرف') . "</li>";
echo "</ul>";

// اختبار ProductManager
echo "<h3>2. اختبار ProductManager:</h3>";
try {
    $page = 1;
    $itemsPerPage = defined('ITEMS_PER_PAGE') ? ITEMS_PER_PAGE : 12;
    
    echo "<p><strong>معاملات الاختبار:</strong></p>";
    echo "<ul>";
    echo "<li>الصفحة: $page</li>";
    echo "<li>عدد العناصر في الصفحة: $itemsPerPage</li>";
    echo "</ul>";
    
    // اختبار getAllProducts
    $products = $productManager->getAllProducts($page, $itemsPerPage, 'active');
    echo "<p><strong>getAllProducts:</strong> " . count($products) . " منتج</p>";
    
    // اختبار getProductsCount
    $totalProducts = $productManager->getProductsCount();
    echo "<p><strong>getProductsCount:</strong> $totalProducts منتج إجمالي</p>";
    
    // اختبار getFeaturedProducts
    $featuredProducts = $productManager->getFeaturedProducts(6);
    echo "<p><strong>getFeaturedProducts:</strong> " . count($featuredProducts) . " منتج مميز</p>";
    
    if (count($products) > 0) {
        echo "<h4>عينة من المنتجات:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>الاسم</th><th>القسم</th><th>السعر</th><th>الصورة</th><th>الحالة</th>";
        echo "</tr>";
        
        foreach (array_slice($products, 0, 5) as $product) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['category_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . formatPrice($product['price']) . "</td>";
            echo "<td>";
            if ($product['image']) {
                $imageUrl = UPLOADS_URL . '/' . $product['image'];
                echo "<a href='$imageUrl' target='_blank'>عرض</a>";
            } else {
                echo "لا توجد";
            }
            echo "</td>";
            echo "<td>" . $product['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في ProductManager: " . $e->getMessage() . "</p>";
}

// اختبار CategoryManager
echo "<h3>3. اختبار CategoryManager:</h3>";
try {
    $mainCategories = $categoryManager->getMainCategories();
    echo "<p><strong>getMainCategories:</strong> " . count($mainCategories) . " قسم رئيسي</p>";
    
    if (count($mainCategories) > 0) {
        echo "<h4>الأقسام الرئيسية:</h4>";
        echo "<ul>";
        foreach ($mainCategories as $category) {
            echo "<li>" . htmlspecialchars($category['name']) . " (ID: " . $category['id'] . ")</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في CategoryManager: " . $e->getMessage() . "</p>";
}

// اختبار CartManager
echo "<h3>4. اختبار CartManager:</h3>";
try {
    $cartCount = $cartManager->getCartCount();
    echo "<p><strong>getCartCount:</strong> $cartCount عنصر في السلة</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في CartManager: " . $e->getMessage() . "</p>";
}

// اختبار الدوال المساعدة
echo "<h3>5. اختبار الدوال المساعدة:</h3>";
try {
    echo "<ul>";
    echo "<li><strong>formatPrice(99.99):</strong> " . formatPrice(99.99) . "</li>";
    echo "<li><strong>formatDate(now):</strong> " . formatDate(date('Y-m-d H:i:s')) . "</li>";
    echo "<li><strong>truncateText('نص طويل جداً...'):</strong> " . truncateText('هذا نص طويل جداً يحتاج إلى اختصار لأنه يحتوي على كلمات كثيرة', 30) . "</li>";
    echo "<li><strong>getSiteSetting('site_name'):</strong> " . getSiteSetting('site_name', 'G8 Store') . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في الدوال المساعدة: " . $e->getMessage() . "</p>";
}

// اختبار عرض صورة
if (!empty($products) && $products[0]['image']) {
    echo "<h3>6. اختبار عرض الصور:</h3>";
    $firstProduct = $products[0];
    $imageUrl = UPLOADS_URL . '/' . $firstProduct['image'];
    
    echo "<p><strong>المنتج:</strong> " . htmlspecialchars($firstProduct['name']) . "</p>";
    echo "<p><strong>مسار الصورة:</strong> " . htmlspecialchars($firstProduct['image']) . "</p>";
    echo "<p><strong>رابط الصورة:</strong> <a href='$imageUrl' target='_blank'>$imageUrl</a></p>";
    echo "<img src='$imageUrl' alt='" . htmlspecialchars($firstProduct['name']) . "' style='max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;'>";
}

echo "<h3>7. النتيجة:</h3>";

if (count($products) > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>✅ صفحة المنتجات جاهزة للعمل!</h4>";
    echo "<p>تم العثور على " . count($products) . " منتج من أصل $totalProducts منتج</p>";
    echo "<p>جميع الدوال تعمل بشكل صحيح</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ لا توجد منتجات</h4>";
    echo "<p>تحتاج لإضافة منتجات أولاً</p>";
    echo "<a href='add_sample_data.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>إضافة بيانات تجريبية</a>";
    echo "</div>";
}

echo "<h3>8. روابط الاختبار:</h3>";
echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;' target='_blank'>صفحة المنتجات</a>";
echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;' target='_blank'>الصفحة الرئيسية</a>";
echo "<a href='admin/products.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;' target='_blank'>لوحة التحكم</a>";

// اختبار مع معاملات مختلفة
echo "<h3>9. اختبار مع معاملات مختلفة:</h3>";
echo "<ul>";
echo "<li><a href='products.php' target='_blank'>جميع المنتجات</a></li>";
if (!empty($mainCategories)) {
    echo "<li><a href='products.php?category=" . $mainCategories[0]['id'] . "' target='_blank'>منتجات قسم: " . $mainCategories[0]['name'] . "</a></li>";
}
echo "<li><a href='products.php?search=عسل' target='_blank'>البحث عن: عسل</a></li>";
echo "<li><a href='products.php?page=2' target='_blank'>الصفحة الثانية</a></li>";
echo "</ul>";
?>
