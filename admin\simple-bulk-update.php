<?php
/**
 * تحديث مجمع مبسط للحد الأدنى
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_products'])) {
    $updates = $_POST['min_stock'] ?? [];
    $updatedCount = 0;
    $errors = [];
    
    foreach ($updates as $productId => $newMinStock) {
        if (is_numeric($newMinStock) && $newMinStock >= 0) {
            try {
                // الحصول على المنتج
                $product = $database->fetch("SELECT name, min_stock_level FROM products WHERE id = :id", ['id' => $productId]);
                
                if ($product) {
                    // تحديث الحد الأدنى
                    $result = $database->update('products', 
                        ['min_stock_level' => intval($newMinStock), 'updated_at' => date('Y-m-d H:i:s')], 
                        'id = :id', 
                        ['id' => $productId]
                    );
                    
                    if ($result) {
                        logActivity('bulk_min_stock_update', "تحديث الحد الأدنى للمنتج: {$product['name']} من {$product['min_stock_level']} إلى {$newMinStock}");
                        $updatedCount++;
                    } else {
                        $errors[] = "فشل تحديث: {$product['name']}";
                    }
                } else {
                    $errors[] = "منتج غير موجود: ID $productId";
                }
            } catch (Exception $e) {
                $errors[] = "خطأ في المنتج ID $productId: " . $e->getMessage();
            }
        }
    }
    
    if ($updatedCount > 0) {
        $message = "تم تحديث $updatedCount منتج بنجاح";
        $messageType = 'success';
        
        if (!empty($errors)) {
            $message .= "<br><small>أخطاء: " . implode(', ', $errors) . "</small>";
        }
    } else {
        $message = "لم يتم تحديث أي منتج";
        if (!empty($errors)) {
            $message .= "<br>الأخطاء: " . implode('<br>', $errors);
        }
        $messageType = 'warning';
    }
}

// الحصول على المنتجات
$products = $database->fetchAll("
    SELECT id, name, stock_quantity, min_stock_level, price 
    FROM products 
    WHERE status = 'active' 
    ORDER BY name ASC
");

$pageTitle = "تحديث مجمع مبسط - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
    
    <style>
    .changed-input {
        background-color: #fff3cd !important;
        border: 2px solid #ffc107 !important;
    }
    .product-row:hover {
        background-color: #f8f9fa;
    }
    </style>
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">📝 تحديث مجمع مبسط للحد الأدنى</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Instructions -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">📋 تعليمات الاستخدام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>كيفية الاستخدام:</h6>
                                <ol>
                                    <li>عدل الحد الأدنى للمنتجات المطلوبة</li>
                                    <li>الحقول المتغيرة ستظهر بلون أصفر</li>
                                    <li>انقر على "حفظ التحديثات"</li>
                                    <li>تحقق من النتيجة في الرسالة</li>
                                </ol>
                            </div>
                            <div class="col-md-6">
                                <h6>نصائح سريعة:</h6>
                                <ul>
                                    <li><strong>منتجات سريعة:</strong> 10-20</li>
                                    <li><strong>منتجات متوسطة:</strong> 5-10</li>
                                    <li><strong>منتجات بطيئة:</strong> 2-5</li>
                                    <li><strong>منتجات غالية:</strong> 1-3</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update Form -->
                <div class="admin-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title">تحديث الحد الأدنى للمنتجات</h5>
                        <div>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="setAllValue()">
                                <i class="fas fa-magic"></i> تعيين للكل
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="resetChanges()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="updateForm">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>المخزون الحالي</th>
                                            <th>الحد الأدنى الحالي</th>
                                            <th>الحد الأدنى الجديد</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($products as $product): ?>
                                        <tr class="product-row">
                                            <td>
                                                <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                                <br><small class="text-muted">ID: <?php echo $product['id']; ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info fs-6">
                                                    <?php echo number_format($product['stock_quantity']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark fs-6">
                                                    <?php echo number_format($product['min_stock_level']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <input type="number" 
                                                       class="form-control form-control-sm min-stock-input" 
                                                       name="min_stock[<?php echo $product['id']; ?>]" 
                                                       value="<?php echo $product['min_stock_level']; ?>" 
                                                       min="0" 
                                                       max="1000"
                                                       style="width: 80px;"
                                                       data-original="<?php echo $product['min_stock_level']; ?>"
                                                       data-product-id="<?php echo $product['id']; ?>"
                                                       data-current-stock="<?php echo $product['stock_quantity']; ?>">
                                            </td>
                                            <td>
                                                <span class="status-indicator" id="status-<?php echo $product['id']; ?>">
                                                    <?php if ($product['stock_quantity'] == 0): ?>
                                                        <span class="badge bg-danger">نفد المخزون</span>
                                                    <?php elseif ($product['stock_quantity'] <= $product['min_stock_level']): ?>
                                                        <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">متوفر</span>
                                                    <?php endif; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i> 
                                        سيتم تحديث جميع المنتجات بالقيم المعروضة
                                    </small>
                                    <br>
                                    <small class="text-warning" id="changedCount">
                                        <i class="fas fa-edit"></i> 
                                        لم يتم تغيير أي قيمة
                                    </small>
                                </div>
                                <div>
                                    <button type="submit" name="update_products" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save"></i> حفظ التحديثات
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-primary w-100" onclick="setByCategory(15)">
                                    <i class="fas fa-fire"></i><br>
                                    منتجات سريعة<br>
                                    <small>(حد أدنى: 15)</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-warning w-100" onclick="setByCategory(8)">
                                    <i class="fas fa-clock"></i><br>
                                    منتجات متوسطة<br>
                                    <small>(حد أدنى: 8)</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="setByCategory(3)">
                                    <i class="fas fa-turtle"></i><br>
                                    منتجات بطيئة<br>
                                    <small>(حد أدنى: 3)</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-info w-100" onclick="calculateBasedOnStock()">
                                    <i class="fas fa-calculator"></i><br>
                                    حساب تلقائي<br>
                                    <small>(20% من المخزون)</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Links -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">روابط الاختبار</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="test-bulk-update.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-bug"></i><br>
                                    اختبار التحديث
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="inventory.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-warehouse"></i><br>
                                    إدارة المخزون
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="activity-logs.php" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-history"></i><br>
                                    سجل الأنشطة
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="bulk-update-stock.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-edit"></i><br>
                                    النسخة المتقدمة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // تتبع التغييرات
    function updateChangedCount() {
        let changedCount = 0;
        document.querySelectorAll('.min-stock-input').forEach(input => {
            const original = input.dataset.original;
            const current = input.value;
            
            if (original !== current) {
                input.classList.add('changed-input');
                changedCount++;
            } else {
                input.classList.remove('changed-input');
            }
        });
        
        const countElement = document.getElementById('changedCount');
        if (changedCount > 0) {
            countElement.innerHTML = `<i class="fas fa-edit"></i> تم تغيير ${changedCount} منتج`;
            countElement.className = 'text-warning';
        } else {
            countElement.innerHTML = `<i class="fas fa-edit"></i> لم يتم تغيير أي قيمة`;
            countElement.className = 'text-muted';
        }
    }
    
    // تحديث حالة المنتج
    function updateStatus(input) {
        const productId = input.dataset.productId;
        const currentStock = parseInt(input.dataset.currentStock);
        const newMinStock = parseInt(input.value);
        const statusElement = document.getElementById('status-' + productId);
        
        let statusHtml;
        if (currentStock == 0) {
            statusHtml = '<span class="badge bg-danger">نفد المخزون</span>';
        } else if (currentStock <= newMinStock) {
            statusHtml = '<span class="badge bg-warning text-dark">مخزون منخفض</span>';
        } else {
            statusHtml = '<span class="badge bg-success">متوفر</span>';
        }
        
        statusElement.innerHTML = statusHtml;
    }
    
    // تعيين قيمة لجميع المنتجات
    function setAllValue() {
        const value = prompt('أدخل الحد الأدنى لجميع المنتجات:');
        if (value && !isNaN(value) && value >= 0) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                input.value = value;
                updateStatus(input);
            });
            updateChangedCount();
        }
    }
    
    // إعادة تعيين التغييرات
    function resetChanges() {
        if (confirm('هل تريد إعادة تعيين جميع القيم للحالة الأصلية؟')) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                input.value = input.dataset.original;
                updateStatus(input);
            });
            updateChangedCount();
        }
    }
    
    // تعيين حسب الفئة
    function setByCategory(value) {
        if (confirm(`هل تريد تعيين الحد الأدنى إلى ${value} لجميع المنتجات؟`)) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                input.value = value;
                updateStatus(input);
            });
            updateChangedCount();
        }
    }
    
    // حساب بناءً على المخزون
    function calculateBasedOnStock() {
        if (confirm('هل تريد حساب الحد الأدنى كـ 20% من المخزون الحالي؟')) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                const currentStock = parseInt(input.dataset.currentStock);
                const minStock = Math.max(1, Math.round(currentStock * 0.2));
                input.value = minStock;
                updateStatus(input);
            });
            updateChangedCount();
        }
    }
    
    // إضافة مستمعات الأحداث
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.min-stock-input').forEach(input => {
            input.addEventListener('input', function() {
                updateStatus(this);
                updateChangedCount();
            });
        });
        
        // تحديث العداد الأولي
        updateChangedCount();
    });
    </script>
</body>
</html>
