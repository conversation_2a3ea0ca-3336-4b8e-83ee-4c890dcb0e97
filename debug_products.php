<?php
/**
 * تشخيص مشاكل المنتجات
 * Debug Products Issues
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>تشخيص مشاكل المنتجات</h2>";

try {
    require_once 'includes/functions.php';
    echo "✅ تم تحميل functions.php بنجاح<br>";
    
    // اختبار الاتصال بقاعدة البيانات
    echo "<h3>1. اختبار قاعدة البيانات:</h3>";
    $testQuery = $database->fetch("SELECT 1 as test");
    if ($testQuery) {
        echo "✅ الاتصال بقاعدة البيانات يعمل<br>";
    } else {
        echo "❌ مشكلة في الاتصال بقاعدة البيانات<br>";
        exit;
    }
    
    // اختبار وجود الجداول
    echo "<h3>2. اختبار وجود الجداول:</h3>";
    $tables = ['categories', 'products', 'users', 'site_settings'];
    foreach ($tables as $table) {
        try {
            $result = $database->fetch("SELECT COUNT(*) as count FROM $table");
            echo "✅ جدول $table موجود - عدد السجلات: " . $result['count'] . "<br>";
        } catch (Exception $e) {
            echo "❌ جدول $table غير موجود أو به مشكلة: " . $e->getMessage() . "<br>";
        }
    }
    
    // اختبار ProductManager
    echo "<h3>3. اختبار ProductManager:</h3>";
    if (isset($productManager)) {
        echo "✅ متغير productManager موجود<br>";
        
        try {
            $allProducts = $productManager->getAllProducts(1, 10);
            echo "✅ دالة getAllProducts تعمل - عدد المنتجات: " . count($allProducts) . "<br>";
            
            if (!empty($allProducts)) {
                echo "أول منتج: " . $allProducts[0]['name'] . "<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في getAllProducts: " . $e->getMessage() . "<br>";
        }
        
        try {
            $featuredProducts = $productManager->getFeaturedProducts(5);
            echo "✅ دالة getFeaturedProducts تعمل - عدد المنتجات المميزة: " . count($featuredProducts) . "<br>";
        } catch (Exception $e) {
            echo "❌ خطأ في getFeaturedProducts: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ متغير productManager غير موجود<br>";
    }
    
    // اختبار CategoryManager
    echo "<h3>4. اختبار CategoryManager:</h3>";
    if (isset($categoryManager)) {
        echo "✅ متغير categoryManager موجود<br>";
        
        try {
            $categories = $categoryManager->getAllCategories();
            echo "✅ دالة getAllCategories تعمل - عدد الأقسام: " . count($categories) . "<br>";
            
            if (!empty($categories)) {
                echo "أول قسم: " . $categories[0]['name'] . "<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في getAllCategories: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ متغير categoryManager غير موجود<br>";
    }
    
    // اختبار مباشر للمنتجات
    echo "<h3>5. اختبار مباشر للمنتجات:</h3>";
    try {
        $directProducts = $database->fetchAll("SELECT * FROM products LIMIT 5");
        echo "✅ استعلام مباشر للمنتجات - عدد المنتجات: " . count($directProducts) . "<br>";
        
        if (!empty($directProducts)) {
            foreach ($directProducts as $product) {
                echo "- " . $product['name'] . " (ID: " . $product['id'] . ", الحالة: " . $product['status'] . ")<br>";
            }
        } else {
            echo "❌ لا توجد منتجات في قاعدة البيانات<br>";
            echo "<p style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
            echo "<strong>الحل:</strong> تحتاج لإضافة منتجات إلى قاعدة البيانات<br>";
            echo "<a href='add_sample_data.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>إضافة بيانات تجريبية</a>";
            echo "</p>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في الاستعلام المباشر: " . $e->getMessage() . "<br>";
    }
    
    // اختبار مباشر للأقسام
    echo "<h3>6. اختبار مباشر للأقسام:</h3>";
    try {
        $directCategories = $database->fetchAll("SELECT * FROM categories LIMIT 5");
        echo "✅ استعلام مباشر للأقسام - عدد الأقسام: " . count($directCategories) . "<br>";
        
        if (!empty($directCategories)) {
            foreach ($directCategories as $category) {
                echo "- " . $category['name'] . " (ID: " . $category['id'] . ", الحالة: " . $category['status'] . ")<br>";
            }
        } else {
            echo "❌ لا توجد أقسام في قاعدة البيانات<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في استعلام الأقسام: " . $e->getMessage() . "<br>";
    }
    
    // اختبار صفحة المنتجات
    echo "<h3>7. اختبار صفحة المنتجات:</h3>";
    echo "<a href='products.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة المنتجات</a><br><br>";
    
    // اختبار لوحة التحكم
    echo "<h3>8. اختبار لوحة التحكم:</h3>";
    echo "<a href='admin/products.php' target='_blank' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح لوحة تحكم المنتجات</a><br><br>";
    
    echo "<h3>الإجراءات المقترحة:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<ol>";
    echo "<li><a href='add_sample_data.php'>إضافة بيانات تجريبية</a> - إذا كانت قاعدة البيانات فارغة</li>";
    echo "<li><a href='admin/add-product.php'>إضافة منتج يدوياً</a> - من لوحة التحكم</li>";
    echo "<li><a href='test_connection.php'>اختبار الاتصال العام</a> - للتأكد من عمل النظام</li>";
    echo "<li>تحقق من ملف database.sql وتأكد من استيراده بالكامل</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "<br>";
    echo "<br><strong>تفاصيل الخطأ:</strong><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
