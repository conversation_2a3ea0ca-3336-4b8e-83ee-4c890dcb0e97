<?php
/**
 * إدارة الطلبات
 * Orders Management
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'update_status':
                    $orderId = intval($_POST['order_id']);
                    $newStatus = cleanInput($_POST['status']);

                    // التحقق من صحة الحالة
                    $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
                    if (!in_array($newStatus, $validStatuses)) {
                        throw new Exception('حالة غير صحيحة');
                    }

                    // الحصول على معلومات الطلب قبل التحديث
                    $order = $database->fetch("SELECT order_number, status FROM orders WHERE id = :id", ['id' => $orderId]);
                    if (!$order) {
                        throw new Exception('الطلب غير موجود');
                    }

                    // تحديث حالة الطلب
                    $updateResult = $database->update('orders',
                        ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')],
                        'id = :id',
                        ['id' => $orderId]
                    );

                    if ($updateResult) {
                        $message = "تم تحديث حالة الطلب {$order['order_number']} إلى: " . getStatusText($newStatus);
                        $messageType = 'success';
                        logActivity('order_status_updated', "تم تحديث حالة الطلب {$order['order_number']} من {$order['status']} إلى {$newStatus}");
                    } else {
                        throw new Exception('فشل في تحديث قاعدة البيانات');
                    }
                    break;

                case 'delete':
                    $orderId = intval($_POST['order_id']);

                    // الحصول على معلومات الطلب قبل الحذف
                    $order = $database->fetch("SELECT order_number FROM orders WHERE id = :id", ['id' => $orderId]);
                    if (!$order) {
                        throw new Exception('الطلب غير موجود');
                    }

                    // حذف عناصر الطلب أولاً (إذا كانت موجودة)
                    try {
                        $database->delete('order_items', 'order_id = :order_id', ['order_id' => $orderId]);
                    } catch (Exception $e) {
                        // تجاهل الخطأ إذا كان الجدول غير موجود
                    }

                    // حذف الطلب
                    if ($database->delete('orders', 'id = :id', ['id' => $orderId])) {
                        $message = "تم حذف الطلب {$order['order_number']} بنجاح";
                        $messageType = 'success';
                        logActivity('order_deleted', "تم حذف الطلب {$order['order_number']} (ID: {$orderId})");
                    } else {
                        throw new Exception('فشل في حذف الطلب من قاعدة البيانات');
                    }
                    break;

                default:
                    throw new Exception('إجراء غير صحيح');
            }
        } catch (Exception $e) {
            $message = 'خطأ: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// دالة مساعدة لترجمة حالات الطلبات
function getStatusText($status) {
    $statusTexts = [
        'pending' => 'قيد الانتظار',
        'confirmed' => 'مؤكد',
        'processing' => 'قيد التحضير',
        'shipped' => 'تم الشحن',
        'delivered' => 'تم التوصيل',
        'cancelled' => 'ملغي'
    ];
    return $statusTexts[$status] ?? $status;
}

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$statusFilter = isset($_GET['status']) ? cleanInput($_GET['status']) : '';
$dateFrom = isset($_GET['date_from']) ? cleanInput($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? cleanInput($_GET['date_to']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(order_number LIKE :search OR customer_name LIKE :search OR customer_email LIKE :search OR customer_phone LIKE :search)";
    $params['search'] = '%' . $search . '%';
}

if ($statusFilter) {
    $whereConditions[] = "status = :status";
    $params['status'] = $statusFilter;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(created_at) >= :date_from";
    $params['date_from'] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(created_at) <= :date_to";
    $params['date_to'] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على الطلبات
$sql = "SELECT * FROM orders {$whereClause} ORDER BY created_at DESC LIMIT " . intval($itemsPerPage) . " OFFSET " . intval($offset);

$orders = $database->fetchAll($sql, $params);

// حساب إجمالي الطلبات
$countSql = "SELECT COUNT(*) as total FROM orders {$whereClause}";
$countParams = array_diff_key($params, ['limit' => '', 'offset' => '']);
$totalResult = $database->fetch($countSql, $countParams);
$totalOrders = $totalResult['total'];
$totalPages = ceil($totalOrders / $itemsPerPage);

$pageTitle = "إدارة الطلبات - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">إدارة الطلبات</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">البحث والتصفية</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="orders.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="رقم الطلب، اسم العميل، البريد، الهاتف..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?php echo ($statusFilter === 'pending') ? 'selected' : ''; ?>>قيد الانتظار</option>
                                    <option value="confirmed" <?php echo ($statusFilter === 'confirmed') ? 'selected' : ''; ?>>مؤكد</option>
                                    <option value="processing" <?php echo ($statusFilter === 'processing') ? 'selected' : ''; ?>>قيد التحضير</option>
                                    <option value="shipped" <?php echo ($statusFilter === 'shipped') ? 'selected' : ''; ?>>تم الشحن</option>
                                    <option value="delivered" <?php echo ($statusFilter === 'delivered') ? 'selected' : ''; ?>>تم التوصيل</option>
                                    <option value="cancelled" <?php echo ($statusFilter === 'cancelled') ? 'selected' : ''; ?>>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn-admin btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Orders Table -->
                <div class="admin-card">
                    <div class="card-header">
                        <h5 class="card-title">
                            الطلبات (<?php echo number_format($totalOrders); ?>)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($orders)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>طريقة الدفع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo $order['order_number']; ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?php echo $order['customer_name']; ?></strong>
                                                <br><small class="text-muted"><?php echo $order['customer_email']; ?></small>
                                                <br><small class="text-muted"><?php echo $order['customer_phone']; ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <strong><?php echo formatPrice($order['total_amount']); ?></strong>
                                        </td>
                                        <td>
                                            <?php 
                                            echo ($order['payment_method'] === 'cash_on_delivery') ? 'الدفع عند الاستلام' : 'تحويل بنكي';
                                            ?>
                                        </td>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            switch($order['status']) {
                                                case 'pending':
                                                    $statusClass = 'warning';
                                                    $statusText = 'قيد الانتظار';
                                                    break;
                                                case 'confirmed':
                                                    $statusClass = 'info';
                                                    $statusText = 'مؤكد';
                                                    break;
                                                case 'processing':
                                                    $statusClass = 'primary';
                                                    $statusText = 'قيد التحضير';
                                                    break;
                                                case 'shipped':
                                                    $statusClass = 'secondary';
                                                    $statusText = 'تم الشحن';
                                                    break;
                                                case 'delivered':
                                                    $statusClass = 'success';
                                                    $statusText = 'تم التوصيل';
                                                    break;
                                                case 'cancelled':
                                                    $statusClass = 'danger';
                                                    $statusText = 'ملغي';
                                                    break;
                                                default:
                                                    $statusClass = 'secondary';
                                                    $statusText = $order['status'];
                                            }
                                            ?>
                                            <span class="badge badge-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                        </td>
                                        <td><?php echo formatDate($order['created_at'], 'd/m/Y H:i'); ?></td>
                                        <td>
                                            <div class="action-buttons">
                                                <button type="button" class="btn-admin btn-info btn-sm" 
                                                        onclick="viewOrder(<?php echo $order['id']; ?>)" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                
                                                <div class="dropdown d-inline">
                                                    <button class="btn-admin btn-warning btn-sm dropdown-toggle" type="button" 
                                                            data-bs-toggle="dropdown" title="تغيير الحالة">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'pending')">قيد الانتظار</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'confirmed')">مؤكد</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'processing')">قيد التحضير</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'shipped')">تم الشحن</a></li>
                                                        <li><a class="dropdown-item" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'delivered')">تم التوصيل</a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'cancelled')">إلغاء</a></li>
                                                    </ul>
                                                </div>
                                                
                                                <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا الطلب؟')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                    <button type="submit" class="btn-admin btn-danger btn-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات الطلبات" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <!-- No Orders -->
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5>لا توجد طلبات</h5>
                            <p class="text-muted">
                                <?php if ($search || $statusFilter || $dateFrom || $dateTo): ?>
                                    لم نجد أي طلبات تطابق معايير البحث المحددة
                                <?php else: ?>
                                    لم يتم استلام أي طلبات بعد
                                <?php endif; ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Order Details Modal -->
    <div class="modal fade" id="orderDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الطلب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderDetailsContent">
                    <!-- سيتم تحميل المحتوى هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function viewOrder(orderId) {
        // تحميل تفاصيل الطلب عبر AJAX
        fetch('order-details-ajax.php?id=' + orderId)
        .then(response => response.text())
        .then(data => {
            document.getElementById('orderDetailsContent').innerHTML = data;
            new bootstrap.Modal(document.getElementById('orderDetailsModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل تفاصيل الطلب');
        });
    }
    
    function updateOrderStatus(orderId, status) {
        console.log('تحديث حالة الطلب:', orderId, status); // للتشخيص

        if (confirm('هل تريد تحديث حالة الطلب؟')) {
            // إنشاء النموذج
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'orders.php'; // تأكد من المسار

            // إضافة الحقول المخفية
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'update_status';
            form.appendChild(actionInput);

            const orderIdInput = document.createElement('input');
            orderIdInput.type = 'hidden';
            orderIdInput.name = 'order_id';
            orderIdInput.value = orderId;
            form.appendChild(orderIdInput);

            const statusInput = document.createElement('input');
            statusInput.type = 'hidden';
            statusInput.name = 'status';
            statusInput.value = status;
            form.appendChild(statusInput);

            // إضافة النموذج للصفحة وإرساله
            document.body.appendChild(form);
            console.log('إرسال النموذج...'); // للتشخيص
            form.submit();
        }
    }
    </script>
</body>
</html>
