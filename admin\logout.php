<?php
/**
 * تسجيل خروج الإدارة
 * Admin Logout
 */

require_once '../includes/functions.php';

// تسجيل النشاط قبل تسجيل الخروج
if (isLoggedIn()) {
    $userName = $_SESSION['user_name'] ?? 'مستخدم غير معروف';
    logActivity('admin_logout', "تسجيل خروج من لوحة التحكم - المستخدم: {$userName}");
}

// تسجيل الخروج
logout();

// إعادة التوجيه إلى صفحة تسجيل الدخول
header('Location: login.php?message=' . urlencode('تم تسجيل الخروج بنجاح'));
exit;
?>
