<?php
/**
 * إنشاء بيانات تجريبية للطلبات والمبيعات
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

$message = '';
$messageType = 'info';

// معالجة إنشاء البيانات التجريبية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_sample_data'])) {
    try {
        // التأكد من وجود جدول order_items
        $database->query("
            CREATE TABLE IF NOT EXISTS order_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT NOT NULL,
                product_id INT NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                price DECIMAL(10,2) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        // الحصول على المنتجات المتاحة
        $products = $database->fetchAll("SELECT id, name, price FROM products WHERE status = 'active' LIMIT 10");
        
        if (empty($products)) {
            throw new Exception("لا توجد منتجات نشطة لإنشاء بيانات تجريبية");
        }
        
        $ordersCreated = 0;
        $itemsCreated = 0;
        
        // إنشاء طلبات للشهرين الماضيين
        for ($month = 2; $month >= 0; $month--) {
            $startDate = date('Y-m-01', strtotime("-$month months"));
            $endDate = date('Y-m-t', strtotime("-$month months"));
            
            // عدد الطلبات للشهر (متغير حسب الشهر)
            $ordersPerMonth = 15 + ($month * 5); // الشهر الحالي أكثر
            
            for ($i = 0; $i < $ordersPerMonth; $i++) {
                // تاريخ عشوائي في الشهر
                $randomDay = rand(1, date('t', strtotime($startDate)));
                $orderDate = date('Y-m-d H:i:s', strtotime($startDate . " +$randomDay days +" . rand(8, 20) . " hours"));
                
                // إنشاء الطلب
                $orderData = [
                    'order_number' => 'ORD-' . date('Ymd', strtotime($orderDate)) . '-' . strtoupper(substr(uniqid(), -6)),
                    'customer_name' => 'عميل تجريبي ' . ($i + 1),
                    'customer_email' => 'customer' . ($i + 1) . '@example.com',
                    'customer_phone' => '05' . rand(********, ********),
                    'total_amount' => 0, // سيتم حسابه لاحقاً
                    'status' => (rand(1, 10) > 2) ? 'confirmed' : 'delivered', // 80% مؤكدة
                    'payment_method' => (rand(1, 2) == 1) ? 'cash_on_delivery' : 'bank_transfer',
                    'created_at' => $orderDate,
                    'updated_at' => $orderDate
                ];
                
                $orderId = $database->insert('orders', $orderData);
                $ordersCreated++;
                
                // إضافة عناصر للطلب (1-4 منتجات)
                $itemsCount = rand(1, 4);
                $totalAmount = 0;
                
                $selectedProducts = array_rand($products, min($itemsCount, count($products)));
                if (!is_array($selectedProducts)) {
                    $selectedProducts = [$selectedProducts];
                }
                
                foreach ($selectedProducts as $productIndex) {
                    $product = $products[$productIndex];
                    $quantity = rand(1, 3);
                    
                    // تطبيق تخفيض عشوائي أحياناً
                    $price = $product['price'];
                    if (rand(1, 5) == 1) {
                        $price = $price * 0.9; // خصم 10%
                    }
                    
                    $itemData = [
                        'order_id' => $orderId,
                        'product_id' => $product['id'],
                        'quantity' => $quantity,
                        'price' => $price,
                        'created_at' => $orderDate,
                        'updated_at' => $orderDate
                    ];
                    
                    $database->insert('order_items', $itemData);
                    $itemsCreated++;
                    
                    $totalAmount += ($price * $quantity);
                }
                
                // تحديث إجمالي الطلب
                $database->update('orders', 
                    ['total_amount' => $totalAmount], 
                    'id = :id', 
                    ['id' => $orderId]
                );
            }
        }
        
        // تسجيل النشاط
        logActivity('sample_data_created', "تم إنشاء $ordersCreated طلب و $itemsCreated عنصر كبيانات تجريبية");
        
        $message = "تم إنشاء $ordersCreated طلب و $itemsCreated عنصر بنجاح";
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = "خطأ في إنشاء البيانات التجريبية: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// فحص البيانات الحالية
$currentOrders = 0;
$currentItems = 0;

try {
    $currentOrders = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
    $currentItems = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
} catch (Exception $e) {
    // الجداول غير موجودة
}

echo "<h2>إنشاء بيانات تجريبية للطلبات والمبيعات</h2>";

// عرض الرسائل
if ($message) {
    echo "<div class='alert alert-$messageType' style='padding: 15px; margin: 20px 0; border-radius: 5px; background: " . 
         ($messageType == 'success' ? '#d4edda' : '#f8d7da') . ";'>";
    echo $message;
    echo "</div>";
}

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>📊 البيانات الحالية:</h3>";
echo "<ul>";
echo "<li><strong>عدد الطلبات:</strong> $currentOrders</li>";
echo "<li><strong>عدد عناصر الطلبات:</strong> $currentItems</li>";
echo "</ul>";
echo "</div>";

if ($currentOrders == 0) {
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>⚠️ لا توجد بيانات للطلبات</h3>";
    echo "<p>تحتاج لإنشاء بيانات تجريبية لاختبار تقرير المبيعات الشهرية</p>";
    echo "</div>";
}

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>🎯 ما سيتم إنشاؤه:</h3>";
echo "<ul>";
echo "<li><strong>طلبات للشهر الحالي:</strong> 25 طلب</li>";
echo "<li><strong>طلبات للشهر الماضي:</strong> 20 طلب</li>";
echo "<li><strong>طلبات لما قبل شهرين:</strong> 15 طلب</li>";
echo "<li><strong>عناصر لكل طلب:</strong> 1-4 منتجات</li>";
echo "<li><strong>كميات متنوعة:</strong> 1-3 قطع لكل منتج</li>";
echo "<li><strong>أسعار متغيرة:</strong> مع خصومات عشوائية</li>";
echo "</ul>";
echo "</div>";

echo "<form method='POST' style='margin: 20px 0;'>";
echo "<button type='submit' name='create_sample_data' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 16px; cursor: pointer;' onclick='return confirm(\"هل تريد إنشاء بيانات تجريبية؟ سيتم إضافة طلبات وهمية لاختبار النظام.\");'>";
echo "<i class='fas fa-plus'></i> إنشاء بيانات تجريبية";
echo "</button>";
echo "</form>";

// عرض عينة من البيانات الموجودة
if ($currentItems > 0) {
    echo "<h3>📋 عينة من البيانات الموجودة:</h3>";
    
    try {
        $sampleData = $database->fetchAll("
            SELECT 
                o.order_number,
                o.customer_name,
                o.total_amount,
                o.status,
                o.created_at,
                COUNT(oi.id) as items_count,
                SUM(oi.quantity) as total_quantity
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            GROUP BY o.id
            ORDER BY o.created_at DESC
            LIMIT 10
        ");
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>رقم الطلب</th><th>العميل</th><th>المبلغ</th><th>عدد المنتجات</th><th>الكمية الإجمالية</th><th>الحالة</th><th>التاريخ</th>";
        echo "</tr>";
        
        foreach ($sampleData as $order) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($order['order_number']) . "</td>";
            echo "<td>" . htmlspecialchars($order['customer_name']) . "</td>";
            echo "<td>" . formatPrice($order['total_amount']) . "</td>";
            echo "<td>" . $order['items_count'] . "</td>";
            echo "<td>" . $order['total_quantity'] . "</td>";
            echo "<td>";
            switch($order['status']) {
                case 'confirmed':
                    echo "<span style='background: #d4edda; padding: 2px 8px; border-radius: 3px;'>مؤكد</span>";
                    break;
                case 'delivered':
                    echo "<span style='background: #d1ecf1; padding: 2px 8px; border-radius: 3px;'>تم التوصيل</span>";
                    break;
                default:
                    echo $order['status'];
            }
            echo "</td>";
            echo "<td>" . formatDate($order['created_at'], 'd/m/Y') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في عرض البيانات: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>🔗 روابط مفيدة:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='check-database-structure.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فحص قاعدة البيانات</a>";
echo "<a href='monthly-sales-report.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تقرير المبيعات الشهرية</a>";
echo "<a href='smart-stock-analysis.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>التحليل الذكي</a>";
echo "<a href='inventory.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المخزون</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "</div>";

echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h4>📝 ملاحظات مهمة:</h4>";
echo "<ul>";
echo "<li>البيانات التجريبية وهمية ومخصصة للاختبار فقط</li>";
echo "<li>يمكن حذف البيانات التجريبية لاحقاً من قاعدة البيانات</li>";
echo "<li>البيانات تشمل طلبات للشهرين الماضيين لاختبار المقارنات</li>";
echo "<li>الأسعار والكميات عشوائية لمحاكاة بيانات حقيقية</li>";
echo "</ul>";
echo "</div>";
?>
