<?php
/**
 * اختبار شامل لنظام إدارة المخزون
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px;'>";
    echo "<h3>❌ غير مصرح لك بالوصول</h3>";
    echo "<p>تحتاج لتسجيل الدخول كمدير</p>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    echo "</div>";
    exit;
}

echo "<h2>اختبار شامل لنظام إدارة المخزون</h2>";

// 1. فحص الجداول المطلوبة
echo "<h3>1. فحص الجداول المطلوبة:</h3>";

$requiredTables = [
    'products' => 'جدول المنتجات',
    'stock_movements' => 'جدول حركات المخزون',
    'activity_logs' => 'جدول سجل الأنشطة'
];

foreach ($requiredTables as $table => $description) {
    try {
        $result = $database->fetch("SELECT COUNT(*) as count FROM $table");
        echo "<p style='color: green;'>✅ $description موجود - عدد السجلات: " . $result['count'] . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ $description غير موجود أو به مشكلة: " . $e->getMessage() . "</p>";
    }
}

// 2. فحص حقول المخزون في جدول المنتجات
echo "<h3>2. فحص حقول المخزون في جدول المنتجات:</h3>";

$requiredColumns = [
    'stock_quantity' => 'كمية المخزون',
    'min_stock_level' => 'الحد الأدنى للمخزون',
    'location' => 'موقع المنتج',
    'sku' => 'رمز المنتج'
];

foreach ($requiredColumns as $column => $description) {
    try {
        $checkColumn = $database->fetchAll("SHOW COLUMNS FROM products LIKE '$column'");
        if (!empty($checkColumn)) {
            echo "<p style='color: green;'>✅ العمود $column ($description) موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ العمود $column ($description) غير موجود</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في فحص العمود $column: " . $e->getMessage() . "</p>";
    }
}

// 3. فحص المنتجات مع بيانات المخزون
echo "<h3>3. فحص المنتجات مع بيانات المخزون:</h3>";

try {
    $products = $database->fetchAll("SELECT id, name, stock_quantity, min_stock_level, sku, location FROM products LIMIT 10");
    
    if (!empty($products)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>اسم المنتج</th><th>SKU</th><th>المخزون</th><th>الحد الأدنى</th><th>الموقع</th><th>الحالة</th>";
        echo "</tr>";
        
        foreach ($products as $product) {
            $stockStatus = '';
            $statusColor = '';
            
            if ($product['stock_quantity'] == 0) {
                $stockStatus = 'نفد المخزون';
                $statusColor = 'red';
            } elseif ($product['stock_quantity'] <= $product['min_stock_level']) {
                $stockStatus = 'مخزون منخفض';
                $statusColor = 'orange';
            } else {
                $stockStatus = 'متوفر';
                $statusColor = 'green';
            }
            
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['sku'] ?? 'غير محدد') . "</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "<td>" . $product['min_stock_level'] . "</td>";
            echo "<td>" . htmlspecialchars($product['location'] ?? 'غير محدد') . "</td>";
            echo "<td style='color: $statusColor;'><strong>$stockStatus</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات في قاعدة البيانات</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في استرجاع المنتجات: " . $e->getMessage() . "</p>";
}

// 4. فحص حركات المخزون
echo "<h3>4. فحص حركات المخزون:</h3>";

try {
    $movements = $database->fetchAll("
        SELECT sm.*, p.name as product_name, u.username 
        FROM stock_movements sm 
        LEFT JOIN products p ON sm.product_id = p.id 
        LEFT JOIN users u ON sm.user_id = u.id 
        ORDER BY sm.created_at DESC 
        LIMIT 10
    ");
    
    if (!empty($movements)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>المنتج</th><th>نوع الحركة</th><th>الكمية</th><th>المستخدم</th><th>الملاحظات</th><th>التاريخ</th>";
        echo "</tr>";
        
        foreach ($movements as $movement) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($movement['product_name'] ?? 'منتج محذوف') . "</td>";
            echo "<td>" . htmlspecialchars($movement['movement_type']) . "</td>";
            echo "<td>" . $movement['quantity'] . "</td>";
            echo "<td>" . htmlspecialchars($movement['username'] ?? 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($movement['notes'] ?? '-') . "</td>";
            echo "<td>" . formatDate($movement['created_at'], 'd/m/Y H:i') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد حركات مخزون مسجلة</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في استرجاع حركات المخزون: " . $e->getMessage() . "</p>";
}

// 5. إحصائيات المخزون
echo "<h3>5. إحصائيات المخزون:</h3>";

try {
    $stats = [
        'total_products' => $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'],
        'low_stock' => $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND stock_quantity <= min_stock_level")['count'],
        'out_of_stock' => $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active' AND stock_quantity = 0")['count'],
        'total_stock_value' => $database->fetch("SELECT SUM(stock_quantity * price) as total FROM products WHERE status = 'active'")['total'] ?? 0,
        'total_movements' => $database->fetch("SELECT COUNT(*) as count FROM stock_movements")['count']
    ];
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h4 style='color: #0066cc; margin: 0;'>" . number_format($stats['total_products']) . "</h4>";
    echo "<p style='margin: 5px 0 0 0; color: #666;'>إجمالي المنتجات</p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h4 style='color: #856404; margin: 0;'>" . number_format($stats['low_stock']) . "</h4>";
    echo "<p style='margin: 5px 0 0 0; color: #666;'>مخزون منخفض</p>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h4 style='color: #721c24; margin: 0;'>" . number_format($stats['out_of_stock']) . "</h4>";
    echo "<p style='margin: 5px 0 0 0; color: #666;'>نفد المخزون</p>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h4 style='color: #155724; margin: 0;'>" . formatPrice($stats['total_stock_value']) . "</h4>";
    echo "<p style='margin: 5px 0 0 0; color: #666;'>قيمة المخزون</p>";
    echo "</div>";
    
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 8px; text-align: center;'>";
    echo "<h4 style='color: #0c5460; margin: 0;'>" . number_format($stats['total_movements']) . "</h4>";
    echo "<p style='margin: 5px 0 0 0; color: #666;'>حركات المخزون</p>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في حساب الإحصائيات: " . $e->getMessage() . "</p>";
}

// 6. اختبار الدوال
echo "<h3>6. اختبار الدوال:</h3>";

try {
    // اختبار دالة logActivity
    logActivity('test_inventory', 'اختبار نظام إدارة المخزون');
    echo "<p style='color: green;'>✅ دالة logActivity تعمل بشكل صحيح</p>";
    
    // اختبار دالة formatPrice
    $testPrice = formatPrice(123.45);
    echo "<p style='color: green;'>✅ دالة formatPrice تعمل: $testPrice</p>";
    
    // اختبار دالة formatDate
    $testDate = formatDate(date('Y-m-d H:i:s'), 'd/m/Y H:i');
    echo "<p style='color: green;'>✅ دالة formatDate تعمل: $testDate</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار الدوال: " . $e->getMessage() . "</p>";
}

// 7. اختبار الصفحات
echo "<h3>7. اختبار الصفحات:</h3>";

$inventoryPages = [
    'inventory.php' => 'إدارة المخزون',
    'stock-movements.php' => 'حركات المخزون',
    'stock-modal.php?id=1' => 'مودال إدارة المخزون',
    'activity-logs.php' => 'سجل الأنشطة',
    'export-inventory.php' => 'تصدير المخزون'
];

foreach ($inventoryPages as $page => $description) {
    $pageExists = file_exists($page);
    if ($pageExists) {
        echo "<p style='color: green;'>✅ صفحة $description موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ صفحة $description غير موجودة</p>";
    }
}

// النتيجة النهائية
echo "<h3>8. النتيجة النهائية:</h3>";

$allTablesExist = true;
$allColumnsExist = true;
$allPagesExist = true;

// فحص الجداول
foreach ($requiredTables as $table => $description) {
    try {
        $database->fetch("SELECT 1 FROM $table LIMIT 1");
    } catch (Exception $e) {
        $allTablesExist = false;
        break;
    }
}

// فحص الأعمدة
foreach ($requiredColumns as $column => $description) {
    try {
        $checkColumn = $database->fetchAll("SHOW COLUMNS FROM products LIKE '$column'");
        if (empty($checkColumn)) {
            $allColumnsExist = false;
            break;
        }
    } catch (Exception $e) {
        $allColumnsExist = false;
        break;
    }
}

// فحص الصفحات
foreach ($inventoryPages as $page => $description) {
    if (!file_exists($page)) {
        $allPagesExist = false;
        break;
    }
}

if ($allTablesExist && $allColumnsExist && $allPagesExist) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎉 نظام إدارة المخزون جاهز للعمل!</h4>";
    echo "<p>جميع الجداول والحقول والصفحات موجودة ومتاحة</p>";
    echo "<h5>يمكنك الآن:</h5>";
    echo "<ul>";
    echo "<li>إدارة مخزون المنتجات</li>";
    echo "<li>تتبع حركات المخزون</li>";
    echo "<li>مراقبة المخزون المنخفض</li>";
    echo "<li>تصدير تقارير المخزون</li>";
    echo "<li>مراجعة سجل الأنشطة</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>❌ نظام إدارة المخزون غير مكتمل</h4>";
    echo "<p>تحتاج لتشغيل تحديث قاعدة البيانات أولاً</p>";
    echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث قاعدة البيانات</a>";
    echo "</div>";
}

echo "<h3>9. روابط النظام:</h3>";
echo "<div style='display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;'>";
echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تحديث قاعدة البيانات</a>";
echo "<a href='inventory.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المخزون</a>";
echo "<a href='stock-movements.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>حركات المخزون</a>";
echo "<a href='activity-logs.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>سجل الأنشطة</a>";
echo "<a href='products.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إدارة المنتجات</a>";
echo "<a href='index.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>لوحة التحكم</a>";
echo "</div>";
?>
