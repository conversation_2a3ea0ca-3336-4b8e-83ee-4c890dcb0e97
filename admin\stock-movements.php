<?php
/**
 * حركات المخزون
 * Stock Movements
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

// معاملات البحث والتصفية
$search = isset($_GET['search']) ? cleanInput($_GET['search']) : '';
$movementType = isset($_GET['movement_type']) ? cleanInput($_GET['movement_type']) : '';
$dateFrom = isset($_GET['date_from']) ? cleanInput($_GET['date_from']) : '';
$dateTo = isset($_GET['date_to']) ? cleanInput($_GET['date_to']) : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = ADMIN_ITEMS_PER_PAGE;
$offset = ($page - 1) * $itemsPerPage;

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(p.name LIKE :search OR p.sku LIKE :search OR sm.notes LIKE :search)";
    $params['search'] = '%' . $search . '%';
}

if ($movementType) {
    $whereConditions[] = "sm.movement_type = :movement_type";
    $params['movement_type'] = $movementType;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(sm.created_at) >= :date_from";
    $params['date_from'] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(sm.created_at) <= :date_to";
    $params['date_to'] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// الحصول على حركات المخزون
$sql = "SELECT sm.*, p.name as product_name, p.sku, p.image, u.username 
        FROM stock_movements sm 
        LEFT JOIN products p ON sm.product_id = p.id 
        LEFT JOIN users u ON sm.user_id = u.id 
        {$whereClause} 
        ORDER BY sm.created_at DESC 
        LIMIT " . intval($itemsPerPage) . " OFFSET " . intval($offset);

$movements = $database->fetchAll($sql, $params);

// حساب إجمالي الحركات
$countSql = "SELECT COUNT(*) as total FROM stock_movements sm 
             LEFT JOIN products p ON sm.product_id = p.id 
             {$whereClause}";
$totalResult = $database->fetch($countSql, $params);
$totalMovements = $totalResult['total'];
$totalPages = ceil($totalMovements / $itemsPerPage);

$pageTitle = "حركات المخزون - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">حركات المخزون</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Filters -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">البحث والتصفية</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="stock-movements.php" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="اسم المنتج، SKU، أو ملاحظات..." 
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="movement_type" class="form-label">نوع الحركة</label>
                                <select class="form-select" id="movement_type" name="movement_type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="in" <?php echo ($movementType === 'in') ? 'selected' : ''; ?>>إدخال</option>
                                    <option value="out" <?php echo ($movementType === 'out') ? 'selected' : ''; ?>>إخراج</option>
                                    <option value="adjustment" <?php echo ($movementType === 'adjustment') ? 'selected' : ''; ?>>تعديل</option>
                                    <option value="sale" <?php echo ($movementType === 'sale') ? 'selected' : ''; ?>>بيع</option>
                                    <option value="return" <?php echo ($movementType === 'return') ? 'selected' : ''; ?>>إرجاع</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($dateFrom); ?>">
                            </div>
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($dateTo); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn-admin btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Movements Table -->
                <div class="admin-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title">
                            حركات المخزون (<?php echo number_format($totalMovements); ?>)
                        </h5>
                        <div>
                            <a href="inventory.php" class="btn-admin btn-secondary btn-sm">
                                <i class="fas fa-warehouse"></i> إدارة المخزون
                            </a>
                            <button type="button" class="btn-admin btn-success btn-sm" onclick="exportMovements()">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($movements)): ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>نوع الحركة</th>
                                        <th>الكمية</th>
                                        <th>المستخدم</th>
                                        <th>الملاحظات</th>
                                        <th>التاريخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($movements as $movement): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if ($movement['image']): ?>
                                                <img src="<?php echo UPLOADS_URL . '/' . $movement['image']; ?>" 
                                                     alt="<?php echo htmlspecialchars($movement['product_name']); ?>" 
                                                     class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;">
                                                <?php endif; ?>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($movement['product_name']); ?></strong>
                                                    <?php if ($movement['sku']): ?>
                                                    <br><small class="text-muted">SKU: <?php echo htmlspecialchars($movement['sku']); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $typeClass = '';
                                            $typeText = '';
                                            $typeIcon = '';
                                            switch($movement['movement_type']) {
                                                case 'in':
                                                    $typeClass = 'success';
                                                    $typeText = 'إدخال';
                                                    $typeIcon = 'fa-arrow-up';
                                                    break;
                                                case 'out':
                                                    $typeClass = 'danger';
                                                    $typeText = 'إخراج';
                                                    $typeIcon = 'fa-arrow-down';
                                                    break;
                                                case 'adjustment':
                                                    $typeClass = 'warning';
                                                    $typeText = 'تعديل';
                                                    $typeIcon = 'fa-edit';
                                                    break;
                                                case 'sale':
                                                    $typeClass = 'primary';
                                                    $typeText = 'بيع';
                                                    $typeIcon = 'fa-shopping-cart';
                                                    break;
                                                case 'return':
                                                    $typeClass = 'info';
                                                    $typeText = 'إرجاع';
                                                    $typeIcon = 'fa-undo';
                                                    break;
                                                default:
                                                    $typeClass = 'secondary';
                                                    $typeText = $movement['movement_type'];
                                                    $typeIcon = 'fa-question';
                                            }
                                            ?>
                                            <span class="badge badge-<?php echo $typeClass; ?>">
                                                <i class="fas <?php echo $typeIcon; ?>"></i> <?php echo $typeText; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo ($movement['movement_type'] == 'in' || $movement['movement_type'] == 'return') ? 'success' : 'danger'; ?> fs-6">
                                                <?php echo ($movement['movement_type'] == 'in' || $movement['movement_type'] == 'return') ? '+' : '-'; ?>
                                                <?php echo number_format($movement['quantity']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($movement['username'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <?php if ($movement['notes']): ?>
                                                <span title="<?php echo htmlspecialchars($movement['notes']); ?>">
                                                    <?php echo truncateText($movement['notes'], 50); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatDate($movement['created_at'], 'd/m/Y H:i'); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <nav aria-label="صفحات حركات المخزون" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">السابق</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">التالي</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <!-- No Movements -->
                        <div class="text-center py-5">
                            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                            <h5>لا توجد حركات مخزون</h5>
                            <p class="text-muted">
                                <?php if ($search || $movementType || $dateFrom || $dateTo): ?>
                                    لم نجد أي حركات تطابق معايير البحث المحددة
                                <?php else: ?>
                                    لم يتم تسجيل أي حركات مخزون بعد
                                <?php endif; ?>
                            </p>
                            <a href="inventory.php" class="btn-admin btn-primary">
                                <i class="fas fa-warehouse"></i> إدارة المخزون
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    function exportMovements() {
        const params = new URLSearchParams(window.location.search);
        params.set('export', '1');
        window.open('export-stock-movements.php?' + params.toString(), '_blank');
    }
    </script>
</body>
</html>
