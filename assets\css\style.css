/* 
 * تصميم موقع المنتجات الطبيعية
 * Natural Products Website Styles
 */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات */
:root {
    --primary-color: #2d5016;
    --secondary-color: #4a7c59;
    --accent-color: #8fbc8f;
    --light-green: #f0f8f0;
    --dark-green: #1a3009;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --white: #ffffff;
    --light-bg: #f8f9fa;
}

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* الهيدر */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-top {
    border-bottom: 1px solid rgba(255,255,255,0.2);
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
}

.header-contact {
    font-size: 0.9rem;
}

.header-contact span {
    margin-left: 20px;
}

.header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--white);
    text-decoration: none;
}

.search-box {
    flex: 1;
    max-width: 400px;
    margin: 0 20px;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
}

.search-box button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--accent-color);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    color: var(--white);
}

.cart-icon {
    position: relative;
    color: var(--white);
    font-size: 1.5rem;
    text-decoration: none;
}

.cart-count {
    position: absolute;
    top: -8px;
    left: -8px;
    background: var(--danger-color);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* القائمة الرئيسية */
.navbar {
    background: var(--dark-green);
    padding: 0;
}

.navbar ul {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.navbar li {
    position: relative;
}

.navbar a {
    display: block;
    padding: 15px 20px;
    color: var(--white);
    text-decoration: none;
    transition: background-color 0.3s;
}

.navbar a:hover {
    background: var(--primary-color);
}

.navbar .dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--dark-green);
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
    z-index: 1000;
}

.navbar li:hover .dropdown {
    opacity: 1;
    visibility: visible;
}

/* المحتوى الرئيسي */
.main-content {
    min-height: 500px;
    padding: 2rem 0;
}

/* البانر الرئيسي */
.hero-banner {
    background: linear-gradient(rgba(45, 80, 22, 0.7), rgba(45, 80, 22, 0.7)), 
                url('../images/hero-bg.jpg') center/cover;
    color: var(--white);
    text-align: center;
    padding: 4rem 0;
    margin-bottom: 3rem;
}

.hero-banner h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-banner p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 1rem;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--dark-green);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* بطاقات المنتجات */
.product-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 2rem;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--danger-color);
    color: var(--white);
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.product-description {
    color: var(--light-text);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.product-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.old-price {
    text-decoration: line-through;
    color: var(--light-text);
    font-size: 1rem;
    margin-right: 10px;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.product-actions .btn {
    flex: 1;
    padding: 10px;
    font-size: 0.9rem;
}

/* الأقسام */
.section {
    padding: 3rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--accent-color);
}

/* الفوتر */
.footer {
    background: var(--dark-green);
    color: var(--white);
    padding: 3rem 0 1rem;
    margin-top: 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--white);
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section ul li a:hover {
    color: var(--accent-color);
}

.footer-bottom {
    border-top: 1px solid rgba(255,255,255,0.2);
    padding-top: 1rem;
    text-align: center;
    color: rgba(255,255,255,0.8);
}

/* الرسائل */
.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .header-main {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-box {
        order: 3;
        max-width: 100%;
        margin: 0;
    }
    
    .navbar ul {
        flex-direction: column;
    }
    
    .hero-banner h1 {
        font-size: 2rem;
    }
    
    .col-3, .col-4, .col-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .product-actions {
        flex-direction: column;
    }
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }
