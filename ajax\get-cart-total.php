<?php
/**
 * الحصول على مجموع السلة
 * Get Cart Total
 */

header('Content-Type: application/json');
require_once '../includes/functions.php';

try {
    // الحصول على مجموع السلة
    $total = $cartManager->getCartTotal();
    
    // إرجاع النتيجة
    echo json_encode([
        'success' => true,
        'total' => number_format($total, 2),
        'formatted_total' => formatPrice($total)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'total' => '0.00'
    ]);
}
?>
