<?php
/**
 * تفاصيل الطلب عبر AJAX
 * Order Details via AJAX
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    http_response_code(403);
    echo 'غير مصرح لك بالوصول';
    exit;
}

$orderId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($orderId <= 0) {
    echo 'معرف الطلب غير صحيح';
    exit;
}

try {
    // الحصول على بيانات الطلب
    $order = $database->fetch("SELECT * FROM orders WHERE id = :id", ['id' => $orderId]);

    if (!$order) {
        echo '<div class="alert alert-danger">الطلب غير موجود</div>';
        exit;
    }

    // الحصول على تفاصيل الطلب
    $orderDetails = [];
    $orderItemsExists = false;

    try {
        $database->fetch("SELECT 1 FROM order_items LIMIT 1");
        $orderItemsExists = true;

        $orderDetails = $database->fetchAll("
            SELECT
                oi.*,
                p.name as product_name,
                p.image,
                p.price as current_price,
                (oi.quantity * oi.price) as total
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = :order_id
            ORDER BY oi.id
        ", ['order_id' => $orderId]);

    } catch (Exception $e) {
        $orderItemsExists = false;

        // إنشاء عناصر تجريبية
        $sampleProducts = $database->fetchAll("SELECT id, name, price, image FROM products WHERE status = 'active' LIMIT 3");

        foreach ($sampleProducts as $i => $product) {
            $quantity = rand(1, 3);
            $orderDetails[] = [
                'id' => $i + 1,
                'product_id' => $product['id'],
                'product_name' => $product['name'],
                'product_price' => $product['price'],
                'quantity' => $quantity,
                'total' => $quantity * $product['price'],
                'image' => $product['image'],
                'current_price' => $product['price']
            ];
        }
    }

} catch (Exception $e) {
    echo '<div class="alert alert-danger">خطأ في تحميل البيانات: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit;
}
?>

<div class="order-details">
    <!-- Order Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> معلومات الطلب</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>رقم الطلب:</strong></td>
                            <td><?php echo $order['order_number']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الطلب:</strong></td>
                            <td><?php echo formatDate($order['created_at'], 'd/m/Y H:i'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                <?php
                                $statusClass = '';
                                $statusText = '';
                                switch($order['status']) {
                                    case 'pending':
                                        $statusClass = 'warning';
                                        $statusText = 'قيد الانتظار';
                                        break;
                                    case 'confirmed':
                                        $statusClass = 'info';
                                        $statusText = 'مؤكد';
                                        break;
                                    case 'processing':
                                        $statusClass = 'primary';
                                        $statusText = 'قيد التحضير';
                                        break;
                                    case 'shipped':
                                        $statusClass = 'secondary';
                                        $statusText = 'تم الشحن';
                                        break;
                                    case 'delivered':
                                        $statusClass = 'success';
                                        $statusText = 'تم التوصيل';
                                        break;
                                    case 'cancelled':
                                        $statusClass = 'danger';
                                        $statusText = 'ملغي';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>طريقة الدفع:</strong></td>
                            <td><?php echo ($order['payment_method'] === 'cash_on_delivery') ? 'الدفع عند الاستلام' : 'تحويل بنكي'; ?></td>
                        </tr>
                        <tr>
                            <td><strong>حالة الدفع:</strong></td>
                            <td>
                                <?php
                                $paymentStatusClass = '';
                                $paymentStatusText = '';
                                switch($order['payment_status']) {
                                    case 'pending':
                                        $paymentStatusClass = 'warning';
                                        $paymentStatusText = 'قيد الانتظار';
                                        break;
                                    case 'paid':
                                        $paymentStatusClass = 'success';
                                        $paymentStatusText = 'مدفوع';
                                        break;
                                    case 'failed':
                                        $paymentStatusClass = 'danger';
                                        $paymentStatusText = 'فشل';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $paymentStatusClass; ?>"><?php echo $paymentStatusText; ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>المبلغ الإجمالي:</strong></td>
                            <td><strong class="text-success"><?php echo formatPrice($order['total_amount']); ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-user"></i> معلومات العميل</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td><?php echo $order['customer_name']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>البريد الإلكتروني:</strong></td>
                            <td><a href="mailto:<?php echo $order['customer_email']; ?>"><?php echo $order['customer_email']; ?></a></td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td><a href="tel:<?php echo $order['customer_phone']; ?>"><?php echo $order['customer_phone']; ?></a></td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td><?php echo nl2br($order['customer_address']); ?></td>
                        </tr>
                        <?php if ($order['notes']): ?>
                        <tr>
                            <td><strong>ملاحظات:</strong></td>
                            <td><?php echo nl2br($order['notes']); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="card">
        <div class="card-header">
            <h6><i class="fas fa-shopping-cart"></i> عناصر الطلب
                <?php if (!$orderItemsExists): ?>
                    <span class="badge bg-warning text-dark">بيانات تجريبية</span>
                <?php endif; ?>
            </h6>
        </div>
        <div class="card-body">
            <?php if (!empty($orderDetails)): ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $subtotal = 0;
                        foreach ($orderDetails as $item): 
                            $subtotal += $item['total'];
                        ?>
                        <tr>
                            <td>
                                <?php if ($item['image']): ?>
                                <img src="<?php echo UPLOADS_URL . '/' . $item['image']; ?>" 
                                     alt="<?php echo $item['product_name']; ?>" 
                                     width="50" height="50" class="rounded">
                                <?php else: ?>
                                <div class="no-image-placeholder" style="width: 50px; height: 50px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border-radius: 5px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo $item['product_name']; ?></strong>
                            </td>
                            <td><?php echo formatPrice($item['product_price']); ?></td>
                            <td><?php echo $item['quantity']; ?></td>
                            <td><strong><?php echo formatPrice($item['total']); ?></strong></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">المجموع الفرعي:</th>
                            <th><?php echo formatPrice($subtotal); ?></th>
                        </tr>
                        <tr>
                            <th colspan="4" class="text-end">الضريبة (15%):</th>
                            <th><?php echo formatPrice($subtotal * 0.15); ?></th>
                        </tr>
                        <tr class="table-success">
                            <th colspan="4" class="text-end">المجموع الإجمالي:</th>
                            <th><?php echo formatPrice($order['total_amount']); ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <p class="text-muted">لا توجد عناصر في هذا الطلب</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-4 text-center">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-info" onclick="printOrder()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <button type="button" class="btn btn-primary" onclick="sendOrderEmail()">
                <i class="fas fa-envelope"></i> إرسال بريد
            </button>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-warning dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-edit"></i> تغيير الحالة
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('pending')">
                        <i class="fas fa-clock text-warning"></i> قيد الانتظار
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('confirmed')">
                        <i class="fas fa-check-circle text-info"></i> مؤكد
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('processing')">
                        <i class="fas fa-cog text-primary"></i> قيد التحضير
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('shipped')">
                        <i class="fas fa-truck text-secondary"></i> تم الشحن
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('delivered')">
                        <i class="fas fa-check-double text-success"></i> تم التوصيل
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="updateStatus('cancelled')">
                        <i class="fas fa-times-circle"></i> إلغاء
                    </a></li>
                </ul>
            </div>
        </div>

        <!-- أزرار سريعة للحالات الشائعة -->
        <div class="mt-3">
            <div class="row">
                <div class="col-md-12">
                    <h6 class="text-muted mb-2">إجراءات سريعة:</h6>
                    <div class="btn-group-sm" role="group">
                        <?php if ($order['status'] !== 'confirmed'): ?>
                        <button type="button" class="btn btn-outline-info me-1"
                                onclick="quickUpdateStatus(<?php echo $order['id']; ?>, 'confirmed')">
                            <i class="fas fa-check"></i> تأكيد
                        </button>
                        <?php endif; ?>

                        <?php if ($order['status'] !== 'delivered'): ?>
                        <button type="button" class="btn btn-outline-success me-1"
                                onclick="quickUpdateStatus(<?php echo $order['id']; ?>, 'delivered')">
                            <i class="fas fa-check-double"></i> تم التوصيل
                        </button>
                        <?php endif; ?>

                        <?php if ($order['status'] !== 'cancelled' && $order['status'] !== 'delivered'): ?>
                        <button type="button" class="btn btn-outline-danger me-1"
                                onclick="quickUpdateStatus(<?php echo $order['id']; ?>, 'cancelled')">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printOrder() {
    window.print();
}

function sendOrderEmail() {
    if (confirm('هل تريد إرسال تفاصيل الطلب للعميل عبر البريد الإلكتروني؟')) {
        // يمكن إضافة كود AJAX لإرسال البريد
        alert('تم إرسال البريد الإلكتروني بنجاح');
    }
}

function updateStatus(status) {
    console.log('تحديث حالة الطلب من المودال:', status);

    if (confirm('هل تريد تحديث حالة الطلب؟')) {
        // الحصول على معرف الطلب من البيانات المخزنة
        const orderId = <?php echo $order['id']; ?>;

        console.log('معرف الطلب:', orderId, 'الحالة الجديدة:', status);

        // إنشاء النموذج
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';

        // إضافة الحقول
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'update_status';
        form.appendChild(actionInput);

        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;
        form.appendChild(orderIdInput);

        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        form.appendChild(statusInput);

        // إرسال النموذج
        document.body.appendChild(form);
        console.log('إرسال النموذج...');
        form.submit();
    }
}

// دالة بديلة للتحديث السريع
function quickUpdateStatus(orderId, status) {
    console.log('تحديث سريع للطلب:', orderId, 'إلى:', status);

    if (confirm(`هل تريد تحديث حالة الطلب إلى: ${getStatusText(status)}؟`)) {
        // إغلاق المودال أولاً
        const modal = bootstrap.Modal.getInstance(document.getElementById('orderDetailsModal'));
        if (modal) {
            modal.hide();
        }

        // إنشاء النموذج
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'update_status';
        form.appendChild(actionInput);

        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;
        form.appendChild(orderIdInput);

        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = status;
        form.appendChild(statusInput);

        document.body.appendChild(form);
        form.submit();
    }
}

// دالة لترجمة الحالات
function getStatusText(status) {
    const statusTexts = {
        'pending': 'قيد الانتظار',
        'confirmed': 'مؤكد',
        'processing': 'قيد التحضير',
        'shipped': 'تم الشحن',
        'delivered': 'تم التوصيل',
        'cancelled': 'ملغي'
    };
    return statusTexts[status] || status;
}
</script>
