<?php
/**
 * تفاصيل الطلب عبر AJAX
 * Order Details via AJAX
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    http_response_code(403);
    echo 'غير مصرح لك بالوصول';
    exit;
}

$orderId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($orderId <= 0) {
    echo 'معرف الطلب غير صحيح';
    exit;
}

// الحصول على بيانات الطلب
$order = $orderManager->getOrderById($orderId);

if (!$order) {
    echo 'الطلب غير موجود';
    exit;
}

// الحصول على تفاصيل الطلب
$orderDetails = $orderManager->getOrderDetails($orderId);
?>

<div class="order-details">
    <!-- Order Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-info-circle"></i> معلومات الطلب</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>رقم الطلب:</strong></td>
                            <td><?php echo $order['order_number']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>تاريخ الطلب:</strong></td>
                            <td><?php echo formatDate($order['created_at'], 'd/m/Y H:i'); ?></td>
                        </tr>
                        <tr>
                            <td><strong>الحالة:</strong></td>
                            <td>
                                <?php
                                $statusClass = '';
                                $statusText = '';
                                switch($order['status']) {
                                    case 'pending':
                                        $statusClass = 'warning';
                                        $statusText = 'قيد الانتظار';
                                        break;
                                    case 'confirmed':
                                        $statusClass = 'info';
                                        $statusText = 'مؤكد';
                                        break;
                                    case 'processing':
                                        $statusClass = 'primary';
                                        $statusText = 'قيد التحضير';
                                        break;
                                    case 'shipped':
                                        $statusClass = 'secondary';
                                        $statusText = 'تم الشحن';
                                        break;
                                    case 'delivered':
                                        $statusClass = 'success';
                                        $statusText = 'تم التوصيل';
                                        break;
                                    case 'cancelled':
                                        $statusClass = 'danger';
                                        $statusText = 'ملغي';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>طريقة الدفع:</strong></td>
                            <td><?php echo ($order['payment_method'] === 'cash_on_delivery') ? 'الدفع عند الاستلام' : 'تحويل بنكي'; ?></td>
                        </tr>
                        <tr>
                            <td><strong>حالة الدفع:</strong></td>
                            <td>
                                <?php
                                $paymentStatusClass = '';
                                $paymentStatusText = '';
                                switch($order['payment_status']) {
                                    case 'pending':
                                        $paymentStatusClass = 'warning';
                                        $paymentStatusText = 'قيد الانتظار';
                                        break;
                                    case 'paid':
                                        $paymentStatusClass = 'success';
                                        $paymentStatusText = 'مدفوع';
                                        break;
                                    case 'failed':
                                        $paymentStatusClass = 'danger';
                                        $paymentStatusText = 'فشل';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $paymentStatusClass; ?>"><?php echo $paymentStatusText; ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>المبلغ الإجمالي:</strong></td>
                            <td><strong class="text-success"><?php echo formatPrice($order['total_amount']); ?></strong></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6><i class="fas fa-user"></i> معلومات العميل</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>الاسم:</strong></td>
                            <td><?php echo $order['customer_name']; ?></td>
                        </tr>
                        <tr>
                            <td><strong>البريد الإلكتروني:</strong></td>
                            <td><a href="mailto:<?php echo $order['customer_email']; ?>"><?php echo $order['customer_email']; ?></a></td>
                        </tr>
                        <tr>
                            <td><strong>الهاتف:</strong></td>
                            <td><a href="tel:<?php echo $order['customer_phone']; ?>"><?php echo $order['customer_phone']; ?></a></td>
                        </tr>
                        <tr>
                            <td><strong>العنوان:</strong></td>
                            <td><?php echo nl2br($order['customer_address']); ?></td>
                        </tr>
                        <?php if ($order['notes']): ?>
                        <tr>
                            <td><strong>ملاحظات:</strong></td>
                            <td><?php echo nl2br($order['notes']); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Items -->
    <div class="card">
        <div class="card-header">
            <h6><i class="fas fa-shopping-cart"></i> عناصر الطلب</h6>
        </div>
        <div class="card-body">
            <?php if (!empty($orderDetails)): ?>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>المجموع</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $subtotal = 0;
                        foreach ($orderDetails as $item): 
                            $subtotal += $item['total'];
                        ?>
                        <tr>
                            <td>
                                <?php if ($item['image']): ?>
                                <img src="<?php echo UPLOADS_URL . '/' . $item['image']; ?>" 
                                     alt="<?php echo $item['product_name']; ?>" 
                                     width="50" height="50" class="rounded">
                                <?php else: ?>
                                <div class="no-image-placeholder" style="width: 50px; height: 50px; background: #f8f9fa; display: flex; align-items: center; justify-content: center; border-radius: 5px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?php echo $item['product_name']; ?></strong>
                            </td>
                            <td><?php echo formatPrice($item['product_price']); ?></td>
                            <td><?php echo $item['quantity']; ?></td>
                            <td><strong><?php echo formatPrice($item['total']); ?></strong></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="4" class="text-end">المجموع الفرعي:</th>
                            <th><?php echo formatPrice($subtotal); ?></th>
                        </tr>
                        <tr>
                            <th colspan="4" class="text-end">الضريبة (15%):</th>
                            <th><?php echo formatPrice($subtotal * 0.15); ?></th>
                        </tr>
                        <tr class="table-success">
                            <th colspan="4" class="text-end">المجموع الإجمالي:</th>
                            <th><?php echo formatPrice($order['total_amount']); ?></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <p class="text-muted">لا توجد عناصر في هذا الطلب</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-4 text-center">
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-info" onclick="printOrder()">
                <i class="fas fa-print"></i> طباعة
            </button>
            <button type="button" class="btn btn-primary" onclick="sendOrderEmail()">
                <i class="fas fa-envelope"></i> إرسال بريد
            </button>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-warning dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-edit"></i> تغيير الحالة
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('pending')">قيد الانتظار</a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('confirmed')">مؤكد</a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('processing')">قيد التحضير</a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('shipped')">تم الشحن</a></li>
                    <li><a class="dropdown-item" href="#" onclick="updateStatus('delivered')">تم التوصيل</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="#" onclick="updateStatus('cancelled')">إلغاء</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function printOrder() {
    window.print();
}

function sendOrderEmail() {
    if (confirm('هل تريد إرسال تفاصيل الطلب للعميل عبر البريد الإلكتروني؟')) {
        // يمكن إضافة كود AJAX لإرسال البريد
        alert('تم إرسال البريد الإلكتروني بنجاح');
    }
}

function updateStatus(status) {
    if (confirm('هل تريد تحديث حالة الطلب؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
            <input type="hidden" name="status" value="${status}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
