<?php
/**
 * اختبار سريع للدوال بعد إصلاح التعريف المزدوج
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار سريع للدوال</h2>";

try {
    require_once '../includes/functions.php';
    echo "<p style='color: green;'>✅ تم تحميل functions.php بنجاح</p>";
    
    // اختبار قاعدة البيانات
    $testQuery = $database->fetch("SELECT COUNT(*) as count FROM products");
    echo "<p style='color: green;'>✅ قاعدة البيانات تعمل - عدد المنتجات: " . $testQuery['count'] . "</p>";
    
    // اختبار دالة logActivity
    logActivity('test_function', 'اختبار دالة logActivity بعد الإصلاح');
    echo "<p style='color: green;'>✅ دالة logActivity تعمل بشكل صحيح</p>";
    
    // اختبار الدوال الأخرى
    $testPrice = formatPrice(123.45);
    echo "<p style='color: green;'>✅ دالة formatPrice تعمل: $testPrice</p>";
    
    $testDate = formatDate(date('Y-m-d H:i:s'));
    echo "<p style='color: green;'>✅ دالة formatDate تعمل: $testDate</p>";
    
    $testText = truncateText('هذا نص طويل للاختبار', 20);
    echo "<p style='color: green;'>✅ دالة truncateText تعمل: $testText</p>";
    
    // اختبار الكلاسات
    $products = $productManager->getAllProducts(1, 3, 'active');
    echo "<p style='color: green;'>✅ ProductManager يعمل - تم استرجاع " . count($products) . " منتجات</p>";
    
    $categories = $categoryManager->getMainCategories();
    echo "<p style='color: green;'>✅ CategoryManager يعمل - عدد الأقسام: " . count($categories) . "</p>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎉 جميع الدوال تعمل بشكل صحيح!</h4>";
    echo "<p>تم حل مشكلة التعريف المزدوج وجميع الدوال تعمل الآن</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>❌ خطأ في النظام</h4>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<h3>روابط الاختبار:</h3>";
echo "<a href='update_inventory_database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تحديث قاعدة البيانات</a>";
echo "<a href='test_inventory_system.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار نظام المخزون</a>";
echo "<a href='inventory.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إدارة المخزون</a>";
echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>لوحة التحكم</a>";
?>
