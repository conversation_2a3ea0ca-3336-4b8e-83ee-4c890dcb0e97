<?php
/**
 * تشخيص مشكلة حفظ مسار الصور في قاعدة البيانات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

echo "<h2>تشخيص مشكلة حفظ مسار الصور</h2>";

// الحصول على منتج للاختبار
$productId = isset($_GET['id']) ? intval($_GET['id']) : 3;
$product = $database->fetch("SELECT * FROM products WHERE id = :id", ['id' => $productId]);

if (!$product) {
    echo "<p style='color: red;'>❌ المنتج غير موجود</p>";
    exit;
}

echo "<h3>1. معلومات المنتج الحالي:</h3>";
echo "<ul>";
echo "<li><strong>ID:</strong> " . $product['id'] . "</li>";
echo "<li><strong>الاسم:</strong> " . htmlspecialchars($product['name']) . "</li>";
echo "<li><strong>الصورة الحالية:</strong> " . ($product['image'] ? $product['image'] : 'لا توجد') . "</li>";
echo "<li><strong>معرض الصور:</strong> " . ($product['gallery'] ? $product['gallery'] : 'لا يوجد') . "</li>";
echo "</ul>";

if ($product['image']) {
    $imagePath = UPLOADS_URL . '/' . $product['image'];
    echo "<p><strong>رابط الصورة الحالية:</strong> <a href='$imagePath' target='_blank'>$imagePath</a></p>";
    echo "<img src='$imagePath' alt='الصورة الحالية' style='max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;'>";
}

// اختبار تحديث الصورة
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>2. نتيجة اختبار التحديث:</h3>";
    
    if (isset($_FILES['test_image']) && $_FILES['test_image']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['test_image'];
        
        echo "<h4>معلومات الملف المرفوع:</h4>";
        echo "<ul>";
        echo "<li><strong>اسم الملف:</strong> " . $file['name'] . "</li>";
        echo "<li><strong>حجم الملف:</strong> " . number_format($file['size']) . " بايت</li>";
        echo "<li><strong>نوع الملف:</strong> " . $file['type'] . "</li>";
        echo "</ul>";
        
        // اختبار دالة uploadFile
        echo "<h4>اختبار دالة uploadFile:</h4>";
        $uploadResult = uploadFile($file, 'products');
        
        if ($uploadResult['success']) {
            echo "<p style='color: green;'>✅ تم رفع الملف بنجاح</p>";
            echo "<ul>";
            echo "<li><strong>اسم الملف الجديد:</strong> " . $uploadResult['filename'] . "</li>";
            echo "<li><strong>المسار:</strong> " . $uploadResult['path'] . "</li>";
            echo "</ul>";
            
            $newImageName = $uploadResult['filename'];
            
            // عرض الصورة الجديدة
            $newImageUrl = UPLOADS_URL . '/products/' . $newImageName;
            echo "<p><strong>رابط الصورة الجديدة:</strong> <a href='$newImageUrl' target='_blank'>$newImageUrl</a></p>";
            echo "<img src='$newImageUrl' alt='الصورة الجديدة' style='max-width: 200px; height: auto; border: 1px solid #ddd; padding: 5px;'>";
            
            // اختبار تحديث قاعدة البيانات
            echo "<h4>اختبار تحديث قاعدة البيانات:</h4>";
            
            // حذف الصورة القديمة
            if ($product['image'] && file_exists(UPLOADS_PATH . '/' . $product['image'])) {
                if (unlink(UPLOADS_PATH . '/' . $product['image'])) {
                    echo "<p style='color: green;'>✅ تم حذف الصورة القديمة</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ لم يتم حذف الصورة القديمة</p>";
                }
            }
            
            // تحديث قاعدة البيانات
            $updateData = [
                'image' => $newImageName,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            echo "<p><strong>البيانات المراد تحديثها:</strong></p>";
            echo "<pre>" . print_r($updateData, true) . "</pre>";
            
            $updateResult = $database->update('products', $updateData, 'id = :id', ['id' => $productId]);
            
            if ($updateResult) {
                echo "<p style='color: green;'>✅ تم تحديث قاعدة البيانات بنجاح</p>";
                
                // التحقق من التحديث
                $updatedProduct = $database->fetch("SELECT * FROM products WHERE id = :id", ['id' => $productId]);
                echo "<p><strong>الصورة في قاعدة البيانات بعد التحديث:</strong> " . $updatedProduct['image'] . "</p>";
                
                if ($updatedProduct['image'] === $newImageName) {
                    echo "<p style='color: green;'>✅ تم حفظ مسار الصورة بنجاح في قاعدة البيانات</p>";
                } else {
                    echo "<p style='color: red;'>❌ لم يتم حفظ مسار الصورة في قاعدة البيانات</p>";
                    echo "<p><strong>المتوقع:</strong> $newImageName</p>";
                    echo "<p><strong>الفعلي:</strong> " . $updatedProduct['image'] . "</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ فشل في تحديث قاعدة البيانات</p>";
                
                // اختبار استعلام التحديث يدوياً
                echo "<h4>اختبار استعلام التحديث يدوياً:</h4>";
                try {
                    $sql = "UPDATE products SET image = :image, updated_at = :updated_at WHERE id = :id";
                    $params = [
                        'image' => $newImageName,
                        'updated_at' => date('Y-m-d H:i:s'),
                        'id' => $productId
                    ];
                    
                    echo "<p><strong>الاستعلام:</strong> $sql</p>";
                    echo "<p><strong>المعاملات:</strong></p>";
                    echo "<pre>" . print_r($params, true) . "</pre>";
                    
                    $manualResult = $database->query($sql, $params);
                    
                    if ($manualResult) {
                        echo "<p style='color: green;'>✅ نجح الاستعلام اليدوي</p>";
                    } else {
                        echo "<p style='color: red;'>❌ فشل الاستعلام اليدوي</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ خطأ في الاستعلام اليدوي: " . $e->getMessage() . "</p>";
                }
            }
            
        } else {
            echo "<p style='color: red;'>❌ فشل في رفع الملف: " . $uploadResult['message'] . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ لم يتم رفع أي ملف أو حدث خطأ</p>";
    }
}

// فحص دالة update في كلاس Database
echo "<h3>3. فحص دالة update في كلاس Database:</h3>";
echo "<p><strong>نوع كلاس Database:</strong> " . get_class($database) . "</p>";

// اختبار بسيط لدالة update
echo "<h4>اختبار بسيط لدالة update:</h4>";
$testUpdateResult = $database->update('products', ['updated_at' => date('Y-m-d H:i:s')], 'id = :id', ['id' => $productId]);
echo "<p><strong>نتيجة اختبار التحديث:</strong> " . ($testUpdateResult ? 'نجح ✅' : 'فشل ❌') . "</p>";

// فحص بنية جدول products
echo "<h3>4. فحص بنية جدول products:</h3>";
try {
    $columns = $database->fetchAll("DESCRIBE products");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في فحص بنية الجدول: " . $e->getMessage() . "</p>";
}

?>

<h3>5. نموذج اختبار تحديث الصورة:</h3>

<form method="POST" enctype="multipart/form-data" style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
    <div style="margin-bottom: 15px;">
        <label for="test_image"><strong>اختر صورة جديدة للمنتج:</strong></label><br>
        <input type="file" id="test_image" name="test_image" accept="image/*" required>
    </div>
    <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
        اختبار تحديث الصورة
    </button>
</form>

<h3>6. روابط مفيدة:</h3>
<a href="edit-product.php?id=<?php echo $productId; ?>" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تعديل المنتج</a>
<a href="products.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">قائمة المنتجات</a>
<a href="debug_upload.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">تشخيص الرفع</a>
