<?php
/**
 * الصفحة الرئيسية
 * Homepage
 */

require_once 'includes/functions.php';

// الحصول على المنتجات المميزة
$featuredProducts = $productManager->getFeaturedProducts(8);

// الحصول على الأقسام الرئيسية
$mainCategories = $categoryManager->getMainCategories();

// معالجة الرسائل
$message = '';
$messageType = 'info';

if (isset($_GET['message'])) {
    $message = cleanInput($_GET['message']);
    $messageType = 'success';
}

// تسجيل زيارة الصفحة
logActivity('page_visit', 'زيارة الصفحة الرئيسية');

$pageTitle = getSiteSetting('site_name', 'متجر المنتجات الطبيعية');
$pageDescription = getSiteSetting('site_description', 'أفضل المنتجات الطبيعية من الأعشاب والعسل والجينسنج');
$pageKeywords = getSiteSetting('site_keywords', 'أعشاب, عسل, جينسنج, منتجات طبيعية');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="keywords" content="<?php echo $pageKeywords; ?>">
    <meta name="author" content="متجر المنتجات الطبيعية">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $pageTitle; ?>">
    <meta property="og:description" content="<?php echo $pageDescription; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/logo.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_PATH; ?>/images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone', '+966123456789'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email', '<EMAIL>'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات..." value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartManager->getCartCount(); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="navbar-nav d-flex flex-row">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link">الرئيسية</a>
                </li>
                <?php foreach ($mainCategories as $category): ?>
                <li class="nav-item dropdown">
                    <a href="products.php?category=<?php echo $category['id']; ?>" class="nav-link">
                        <?php echo $category['name']; ?>
                    </a>
                    <?php 
                    $subCategories = $categoryManager->getSubCategories($category['id']);
                    if (!empty($subCategories)): 
                    ?>
                    <ul class="dropdown">
                        <?php foreach ($subCategories as $subCategory): ?>
                        <li><a href="products.php?category=<?php echo $subCategory['id']; ?>"><?php echo $subCategory['name']; ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>
                </li>
                <?php endforeach; ?>
                <li class="nav-item">
                    <a href="contact.php" class="nav-link">اتصل بنا</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="container mt-3">
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    </div>
    <?php endif; ?>

    <!-- Hero Banner -->
    <section class="hero-banner">
        <div class="container">
            <h1>أفضل المنتجات الطبيعية</h1>
            <p>اكتشف مجموعتنا الواسعة من الأعشاب الطبيعية والعسل الخالص ومنتجات الجينسنج عالية الجودة</p>
            <a href="products.php" class="btn btn-primary">تسوق الآن</a>
        </div>
    </section>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Categories Section -->
            <section class="section">
                <h2 class="section-title">أقسامنا الرئيسية</h2>
                <div class="row">
                    <?php foreach ($mainCategories as $category): ?>
                    <div class="col-md-4 mb-4">
                        <div class="category-card text-center">
                            <?php if ($category['image']): ?>
                            <img src="<?php echo UPLOADS_URL . '/' . $category['image']; ?>" alt="<?php echo $category['name']; ?>" class="img-fluid rounded mb-3">
                            <?php endif; ?>
                            <h3><?php echo $category['name']; ?></h3>
                            <p><?php echo $category['description']; ?></p>
                            <a href="products.php?category=<?php echo $category['id']; ?>" class="btn btn-outline">استكشف المنتجات</a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- Featured Products -->
            <?php if (!empty($featuredProducts)): ?>
            <section class="section">
                <h2 class="section-title">المنتجات المميزة</h2>
                <div class="row">
                    <?php foreach ($featuredProducts as $product): ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="product-card">
                            <div class="product-image">
                                <?php if ($product['image']): ?>
                                <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" alt="<?php echo $product['name']; ?>">
                                <?php else: ?>
                                <img src="<?php echo ASSETS_PATH; ?>/images/no-image.jpg" alt="<?php echo $product['name']; ?>">
                                <?php endif; ?>
                                
                                <?php if ($product['sale_price']): ?>
                                <span class="product-badge">خصم</span>
                                <?php endif; ?>
                            </div>
                            <div class="product-info">
                                <h3 class="product-title"><?php echo $product['name']; ?></h3>
                                <p class="product-description"><?php echo truncateText($product['short_description'], 80); ?></p>
                                
                                <div class="product-price">
                                    <div>
                                        <?php if ($product['sale_price']): ?>
                                        <span class="price"><?php echo formatPrice($product['sale_price']); ?></span>
                                        <span class="old-price"><?php echo formatPrice($product['price']); ?></span>
                                        <?php else: ?>
                                        <span class="price"><?php echo formatPrice($product['price']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($product['weight']): ?>
                                    <small class="text-muted"><?php echo $product['weight']; ?></small>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="product-actions">
                                    <a href="product-details.php?id=<?php echo $product['id']; ?>" class="btn btn-outline">عرض التفاصيل</a>
                                    <button onclick="addToCart(<?php echo $product['id']; ?>)" class="btn btn-primary">أضف للسلة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <div class="text-center mt-4">
                    <a href="products.php" class="btn btn-secondary">عرض جميع المنتجات</a>
                </div>
            </section>
            <?php endif; ?>

            <!-- Features Section -->
            <section class="section bg-light">
                <div class="container">
                    <div class="row">
                        <div class="col-md-3 text-center mb-4">
                            <i class="fas fa-leaf fa-3x text-success mb-3"></i>
                            <h4>منتجات طبيعية 100%</h4>
                            <p>جميع منتجاتنا طبيعية وخالية من المواد الكيميائية</p>
                        </div>
                        <div class="col-md-3 text-center mb-4">
                            <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                            <h4>توصيل سريع</h4>
                            <p>نوصل طلباتك في أسرع وقت ممكن</p>
                        </div>
                        <div class="col-md-3 text-center mb-4">
                            <i class="fas fa-award fa-3x text-warning mb-3"></i>
                            <h4>جودة مضمونة</h4>
                            <p>نضمن جودة جميع منتجاتنا</p>
                        </div>
                        <div class="col-md-3 text-center mb-4">
                            <i class="fas fa-headset fa-3x text-info mb-3"></i>
                            <h4>دعم على مدار الساعة</h4>
                            <p>فريق الدعم متاح لمساعدتك في أي وقت</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>عن المتجر</h3>
                    <p>متجر متخصص في بيع أفضل المنتجات الطبيعية من الأعشاب والعسل والجينسنج بأعلى معايير الجودة.</p>
                </div>
                <div class="footer-section">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                        <li><a href="products.php">المنتجات</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>معلومات التواصل</h3>
                    <ul>
                        <li><i class="fas fa-map-marker-alt"></i> <?php echo getSiteSetting('contact_address', 'المملكة العربية السعودية'); ?></li>
                        <li><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone', '+966123456789'); ?></li>
                        <li><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email', '<EMAIL>'); ?></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>تابعنا</h3>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-white"><i class="fab fa-whatsapp fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> جميع الحقوق محفوظة - متجر المنتجات الطبيعية</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
</body>
</html>
