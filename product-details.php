<?php
/**
 * صفحة تفاصيل المنتج
 * Product Details Page
 */

require_once 'includes/functions.php';

// الحصول على معرف المنتج
$productId = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($productId <= 0) {
    header('Location: products.php');
    exit;
}

// الحصول على بيانات المنتج
$product = $productManager->getProductById($productId);

if (!$product) {
    header('Location: products.php');
    exit;
}

// الحصول على منتجات مشابهة
$relatedProducts = $productManager->getProductsByCategory($product['category_id'], 4);

// تسجيل النشاط
logActivity('product_view', "عرض منتج: {$product['name']}");

$pageTitle = $product['seo_title'] ?: $product['name'] . " - " . getSiteSetting('site_name');
$pageDescription = $product['seo_description'] ?: $product['short_description'];
$pageKeywords = $product['seo_keywords'] ?: $product['name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="description" content="<?php echo $pageDescription; ?>">
    <meta name="keywords" content="<?php echo $pageKeywords; ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo $product['name']; ?>">
    <meta property="og:description" content="<?php echo $product['short_description']; ?>">
    <meta property="og:type" content="product">
    <meta property="og:url" content="<?php echo SITE_URL . '/product-details.php?id=' . $product['id']; ?>">
    <?php if ($product['image']): ?>
    <meta property="og:image" content="<?php echo SITE_URL . '/' . UPLOADS_URL . '/' . $product['image']; ?>">
    <?php endif; ?>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/style.css">
    
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": "<?php echo addslashes($product['name']); ?>",
        "description": "<?php echo addslashes($product['short_description']); ?>",
        "sku": "<?php echo $product['sku']; ?>",
        "brand": {
            "@type": "Brand",
            "name": "<?php echo getSiteSetting('site_name'); ?>"
        },
        "offers": {
            "@type": "Offer",
            "price": "<?php echo $product['sale_price'] ?: $product['price']; ?>",
            "priceCurrency": "SAR",
            "availability": "<?php echo ($product['stock_quantity'] > 0) ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'; ?>"
        }
        <?php if ($product['image']): ?>
        ,"image": "<?php echo SITE_URL . '/' . UPLOADS_URL . '/' . $product['image']; ?>"
        <?php endif; ?>
    }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <!-- Header Top -->
            <div class="header-top">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="header-contact">
                            <span><i class="fas fa-phone"></i> <?php echo getSiteSetting('contact_phone'); ?></span>
                            <span><i class="fas fa-envelope"></i> <?php echo getSiteSetting('contact_email'); ?></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <?php if (isLoggedIn()): ?>
                            <span>مرحباً، <?php echo $_SESSION['user_name']; ?></span>
                            <a href="logout.php" class="text-white ms-3">تسجيل الخروج</a>
                        <?php else: ?>
                            <a href="login.php" class="text-white">تسجيل الدخول</a>
                            <a href="register.php" class="text-white ms-3">إنشاء حساب</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Header Main -->
            <div class="header-main">
                <div class="row align-items-center w-100">
                    <div class="col-md-3">
                        <a href="<?php echo SITE_URL; ?>" class="logo">
                            <i class="fas fa-leaf"></i> G8 Store
                        </a>
                    </div>
                    <div class="col-md-6">
                        <form class="search-box" action="products.php" method="GET">
                            <input type="text" name="search" placeholder="ابحث عن المنتجات...">
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </form>
                    </div>
                    <div class="col-md-3 text-end">
                        <a href="cart.php" class="cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="cart-count"><?php echo $cartManager->getCartCount(); ?></span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="navbar-nav d-flex flex-row">
                <li class="nav-item">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link">الرئيسية</a>
                </li>
                <?php 
                $mainCategories = $categoryManager->getMainCategories();
                foreach ($mainCategories as $category): 
                ?>
                <li class="nav-item dropdown">
                    <a href="products.php?category=<?php echo $category['id']; ?>" class="nav-link">
                        <?php echo $category['name']; ?>
                    </a>
                    <?php 
                    $subCategories = $categoryManager->getSubCategories($category['id']);
                    if (!empty($subCategories)): 
                    ?>
                    <ul class="dropdown">
                        <?php foreach ($subCategories as $subCategory): ?>
                        <li><a href="products.php?category=<?php echo $subCategory['id']; ?>"><?php echo $subCategory['name']; ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                    <?php endif; ?>
                </li>
                <?php endforeach; ?>
                <li class="nav-item">
                    <a href="contact.php" class="nav-link">اتصل بنا</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <div class="container mt-3">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo SITE_URL; ?>">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="products.php?category=<?php echo $product['category_id']; ?>"><?php echo $product['category_name']; ?></a></li>
                <li class="breadcrumb-item active"><?php echo $product['name']; ?></li>
            </ol>
        </nav>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Product Details -->
            <div class="product-details">
                <div class="row">
                    <!-- Product Images -->
                    <div class="col-lg-6 mb-4">
                        <div class="product-images">
                            <div class="main-image mb-3">
                                <?php if ($product['image']): ?>
                                <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="img-fluid rounded" id="mainImage">
                                <?php else: ?>
                                <img src="<?php echo ASSETS_PATH; ?>/images/no-image.jpg" alt="<?php echo $product['name']; ?>" class="img-fluid rounded" id="mainImage">
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($product['gallery']): ?>
                            <div class="gallery-thumbnails">
                                <div class="row">
                                    <?php 
                                    $gallery = json_decode($product['gallery'], true);
                                    if ($gallery && is_array($gallery)):
                                        foreach ($gallery as $image): 
                                    ?>
                                    <div class="col-3 mb-2">
                                        <img src="<?php echo UPLOADS_URL . '/' . $image; ?>" alt="<?php echo $product['name']; ?>" class="img-fluid rounded thumbnail" onclick="changeMainImage(this.src)">
                                    </div>
                                    <?php 
                                        endforeach;
                                    endif; 
                                    ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Product Info -->
                    <div class="col-lg-6">
                        <div class="product-info">
                            <h1 class="product-title"><?php echo $product['name']; ?></h1>
                            <p class="product-category">
                                <a href="products.php?category=<?php echo $product['category_id']; ?>"><?php echo $product['category_name']; ?></a>
                            </p>
                            
                            <?php if ($product['sku']): ?>
                            <p class="product-sku">رقم المنتج: <span><?php echo $product['sku']; ?></span></p>
                            <?php endif; ?>
                            
                            <div class="product-price mb-3">
                                <?php if ($product['sale_price']): ?>
                                <span class="current-price"><?php echo formatPrice($product['sale_price']); ?></span>
                                <span class="old-price"><?php echo formatPrice($product['price']); ?></span>
                                <span class="discount-badge">
                                    خصم <?php echo round((($product['price'] - $product['sale_price']) / $product['price']) * 100); ?>%
                                </span>
                                <?php else: ?>
                                <span class="current-price"><?php echo formatPrice($product['price']); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($product['short_description']): ?>
                            <div class="product-description mb-3">
                                <p><?php echo nl2br($product['short_description']); ?></p>
                            </div>
                            <?php endif; ?>
                            
                            <div class="product-details-list mb-3">
                                <?php if ($product['weight']): ?>
                                <div class="detail-item">
                                    <strong>الوزن:</strong> <?php echo $product['weight']; ?>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($product['size']): ?>
                                <div class="detail-item">
                                    <strong>الحجم:</strong> <?php echo $product['size']; ?>
                                </div>
                                <?php endif; ?>
                                
                                <div class="detail-item">
                                    <strong>الحالة:</strong> 
                                    <?php if ($product['stock_quantity'] > 0): ?>
                                    <span class="text-success">متوفر (<?php echo $product['stock_quantity']; ?> قطعة)</span>
                                    <?php else: ?>
                                    <span class="text-danger">غير متوفر</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Add to Cart Form -->
                            <?php if ($product['stock_quantity'] > 0): ?>
                            <form class="add-to-cart-form" onsubmit="return addToCartForm(event)">
                                <div class="quantity-selector mb-3">
                                    <label for="quantity" class="form-label">الكمية:</label>
                                    <div class="input-group" style="max-width: 150px;">
                                        <button type="button" class="btn btn-outline-secondary quantity-btn" data-action="decrease">-</button>
                                        <input type="number" class="form-control text-center" id="quantity" name="quantity" value="1" min="1" max="<?php echo $product['stock_quantity']; ?>">
                                        <button type="button" class="btn btn-outline-secondary quantity-btn" data-action="increase">+</button>
                                    </div>
                                </div>
                                
                                <div class="product-actions">
                                    <button type="submit" class="btn btn-primary btn-lg me-2">
                                        <i class="fas fa-cart-plus"></i> أضف إلى السلة
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="toggleWishlist(<?php echo $product['id']; ?>)">
                                        <i class="far fa-heart"></i> المفضلة
                                    </button>
                                </div>
                                
                                <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                            </form>
                            <?php else: ?>
                            <div class="out-of-stock">
                                <button class="btn btn-secondary btn-lg" disabled>
                                    <i class="fas fa-times"></i> غير متوفر
                                </button>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Social Share -->
                            <div class="social-share mt-4">
                                <h6>شارك المنتج:</h6>
                                <div class="share-buttons">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(SITE_URL . '/product-details.php?id=' . $product['id']); ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook"></i> فيسبوك
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(SITE_URL . '/product-details.php?id=' . $product['id']); ?>&text=<?php echo urlencode($product['name']); ?>" target="_blank" class="btn btn-outline-info btn-sm">
                                        <i class="fab fa-twitter"></i> تويتر
                                    </a>
                                    <a href="https://wa.me/?text=<?php echo urlencode($product['name'] . ' - ' . SITE_URL . '/product-details.php?id=' . $product['id']); ?>" target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-whatsapp"></i> واتساب
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Description -->
            <?php if ($product['description']): ?>
            <div class="product-full-description mt-5">
                <div class="card">
                    <div class="card-header">
                        <h3>وصف المنتج</h3>
                    </div>
                    <div class="card-body">
                        <?php echo nl2br($product['description']); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Related Products -->
            <?php if (!empty($relatedProducts) && count($relatedProducts) > 1): ?>
            <div class="related-products mt-5">
                <h3 class="section-title">منتجات مشابهة</h3>
                <div class="row">
                    <?php foreach ($relatedProducts as $relatedProduct): ?>
                        <?php if ($relatedProduct['id'] != $product['id']): ?>
                        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                            <div class="product-card">
                                <div class="product-image">
                                    <a href="product-details.php?id=<?php echo $relatedProduct['id']; ?>">
                                        <?php if ($relatedProduct['image']): ?>
                                        <img src="<?php echo UPLOADS_URL . '/' . $relatedProduct['image']; ?>" alt="<?php echo $relatedProduct['name']; ?>">
                                        <?php else: ?>
                                        <img src="<?php echo ASSETS_PATH; ?>/images/no-image.jpg" alt="<?php echo $relatedProduct['name']; ?>">
                                        <?php endif; ?>
                                    </a>
                                    
                                    <?php if ($relatedProduct['sale_price']): ?>
                                    <span class="product-badge">خصم</span>
                                    <?php endif; ?>
                                </div>
                                <div class="product-info">
                                    <h4 class="product-title">
                                        <a href="product-details.php?id=<?php echo $relatedProduct['id']; ?>"><?php echo $relatedProduct['name']; ?></a>
                                    </h4>
                                    
                                    <div class="product-price">
                                        <?php if ($relatedProduct['sale_price']): ?>
                                        <span class="price"><?php echo formatPrice($relatedProduct['sale_price']); ?></span>
                                        <span class="old-price"><?php echo formatPrice($relatedProduct['price']); ?></span>
                                        <?php else: ?>
                                        <span class="price"><?php echo formatPrice($relatedProduct['price']); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="product-actions">
                                        <a href="product-details.php?id=<?php echo $relatedProduct['id']; ?>" class="btn btn-outline btn-sm">عرض التفاصيل</a>
                                        <button onclick="addToCart(<?php echo $relatedProduct['id']; ?>)" class="btn btn-primary btn-sm">أضف للسلة</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_PATH; ?>/js/main.js"></script>
    
    <script>
    function changeMainImage(src) {
        document.getElementById('mainImage').src = src;
    }
    
    function addToCartForm(event) {
        event.preventDefault();
        const form = event.target;
        const productId = form.product_id.value;
        const quantity = parseInt(form.quantity.value);
        
        addToCart(productId, quantity);
        return false;
    }
    </script>
</body>
</html>
