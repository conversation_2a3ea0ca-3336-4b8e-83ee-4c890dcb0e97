<?php
/**
 * صفحة منتجات مبسطة للاختبار
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

echo "<h1>صفحة المنتجات المبسطة</h1>";

// الحصول على المنتجات مباشرة
$products = $database->fetchAll("SELECT p.*, c.name as category_name 
                                FROM products p 
                                LEFT JOIN categories c ON p.category_id = c.id 
                                ORDER BY p.created_at DESC");

$totalProducts = count($products);

echo "<p><strong>عدد المنتجات:</strong> " . $totalProducts . "</p>";

?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المنتجات - مبسط</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
    
    <style>
    body {
        font-family: 'Arial', sans-serif;
        direction: rtl;
        text-align: right;
        padding: 20px;
        background: #f8f9fa;
    }
    
    .simple-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin: 20px 0;
    }
    
    .simple-table th {
        background: #2d5016;
        color: white;
        padding: 15px;
        text-align: right;
    }
    
    .simple-table td {
        padding: 15px;
        border-bottom: 1px solid #ddd;
    }
    
    .simple-table tr:hover {
        background: #f8f9fa;
    }
    
    .status-badge {
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }
    
    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
    .status-out_of_stock { background: #fff3cd; color: #856404; }
    
    .action-btn {
        padding: 5px 10px;
        margin: 2px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-edit { background: #17a2b8; color: white; }
    .btn-delete { background: #dc3545; color: white; }
    
    .no-image {
        width: 50px;
        height: 50px;
        background: #f8f9fa;
        border: 2px dashed #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 5px;
        color: #999;
    }
    </style>
</head>
<body>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>إدارة المنتجات (مبسط)</h2>
                <div>
                    <a href="products.php" class="btn btn-primary">الصفحة الأصلية</a>
                    <a href="add-product.php" class="btn btn-success">إضافة منتج</a>
                </div>
            </div>
            
            <?php if (!empty($products)): ?>
            
            <div class="alert alert-success">
                ✅ تم العثور على <?php echo $totalProducts; ?> منتج
            </div>
            
            <table class="simple-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>الصورة</th>
                        <th>اسم المنتج</th>
                        <th>القسم</th>
                        <th>السعر</th>
                        <th>المخزون</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($products as $product): ?>
                    <tr>
                        <td><?php echo $product['id']; ?></td>
                        <td>
                            <?php if ($product['image']): ?>
                            <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                                 alt="<?php echo $product['name']; ?>" 
                                 width="50" height="50" style="border-radius: 5px; object-fit: cover;">
                            <?php else: ?>
                            <div class="no-image">
                                <i class="fas fa-image"></i>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                            <?php if ($product['sku']): ?>
                            <br><small style="color: #666;">رقم المنتج: <?php echo $product['sku']; ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                        <td>
                            <?php if ($product['sale_price']): ?>
                            <span style="color: #28a745; font-weight: bold;"><?php echo formatPrice($product['sale_price']); ?></span>
                            <br><small style="color: #999; text-decoration: line-through;"><?php echo formatPrice($product['price']); ?></small>
                            <?php else: ?>
                            <span style="font-weight: bold;"><?php echo formatPrice($product['price']); ?></span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span style="color: <?php echo ($product['stock_quantity'] <= 5) ? '#dc3545' : '#28a745'; ?>; font-weight: bold;">
                                <?php echo $product['stock_quantity']; ?>
                            </span>
                        </td>
                        <td>
                            <?php
                            $statusClass = '';
                            $statusText = '';
                            switch($product['status']) {
                                case 'active':
                                    $statusClass = 'status-active';
                                    $statusText = 'نشط';
                                    break;
                                case 'inactive':
                                    $statusClass = 'status-inactive';
                                    $statusText = 'غير نشط';
                                    break;
                                case 'out_of_stock':
                                    $statusClass = 'status-out_of_stock';
                                    $statusText = 'نفد المخزون';
                                    break;
                                default:
                                    $statusClass = 'status-inactive';
                                    $statusText = $product['status'];
                            }
                            ?>
                            <span class="status-badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                        </td>
                        <td><?php echo formatDate($product['created_at'], 'd/m/Y'); ?></td>
                        <td>
                            <a href="edit-product.php?id=<?php echo $product['id']; ?>" 
                               class="action-btn btn-edit" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button onclick="deleteProduct(<?php echo $product['id']; ?>)" 
                                    class="action-btn btn-delete" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php else: ?>
            
            <div class="alert alert-warning text-center">
                <i class="fas fa-box fa-3x mb-3" style="color: #856404;"></i>
                <h4>لا توجد منتجات</h4>
                <p>لم يتم إضافة أي منتجات بعد</p>
                <a href="add-product.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة منتج جديد
                </a>
            </div>
            
            <?php endif; ?>
            
            <div class="mt-4">
                <h4>معلومات التشخيص:</h4>
                <ul>
                    <li><strong>إجمالي المنتجات في قاعدة البيانات:</strong> <?php echo $totalProducts; ?></li>
                    <li><strong>ASSETS_PATH:</strong> <?php echo defined('ASSETS_PATH') ? ASSETS_PATH : 'غير معرف'; ?></li>
                    <li><strong>UPLOADS_URL:</strong> <?php echo defined('UPLOADS_URL') ? UPLOADS_URL : 'غير معرف'; ?></li>
                    <li><strong>المستخدم الحالي:</strong> <?php echo $_SESSION['user_name'] ?? 'غير محدد'; ?></li>
                    <li><strong>صلاحيات الإدارة:</strong> <?php echo isAdmin() ? 'نعم' : 'لا'; ?></li>
                </ul>
            </div>
            
            <div class="mt-4">
                <h4>روابط مفيدة:</h4>
                <a href="products.php" class="btn btn-primary">الصفحة الأصلية</a>
                <a href="test_display.php" class="btn btn-info">اختبار العرض</a>
                <a href="debug_products_display.php" class="btn btn-warning">تشخيص مفصل</a>
                <a href="fix_products.php" class="btn btn-danger">إصلاح المشاكل</a>
            </div>
        </div>
    </div>
</div>

<script>
function deleteProduct(id) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        // هنا يمكن إضافة كود الحذف
        alert('سيتم تنفيذ الحذف (غير مفعل في النسخة المبسطة)');
    }
}

// تشخيص JavaScript
console.log('JavaScript يعمل بشكل صحيح');
console.log('عدد المنتجات:', <?php echo $totalProducts; ?>);

// فحص تحميل CSS
document.addEventListener('DOMContentLoaded', function() {
    console.log('الصفحة تم تحميلها بالكامل');
    
    const table = document.querySelector('.simple-table');
    if (table) {
        console.log('✅ الجدول موجود');
        console.log('عدد الصفوف:', table.querySelectorAll('tbody tr').length);
    } else {
        console.log('❌ الجدول غير موجود');
    }
});
</script>

</body>
</html>
