<?php
/**
 * إضافة عناصر للطلبات
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// إنشاء جدول order_items إذا لم يكن موجود
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_table'])) {
    try {
        $sql = "CREATE TABLE IF NOT EXISTS order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            product_id INT NOT NULL,
            product_name VARCHAR(255) NOT NULL,
            price DECIMAL(10,2) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_order_id (order_id),
            INDEX idx_product_id (product_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $database->query($sql);
        
        logActivity('create_order_items_table', 'تم إنشاء جدول order_items');
        $message = "✅ تم إنشاء جدول order_items بنجاح";
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// إضافة عناصر تجريبية لطلب محدد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_items'])) {
    try {
        $orderId = intval($_POST['order_id']);
        
        // التحقق من وجود الطلب
        $order = $database->fetch("SELECT * FROM orders WHERE id = :id", ['id' => $orderId]);
        if (!$order) {
            throw new Exception('الطلب غير موجود');
        }
        
        // الحصول على منتجات عشوائية
        $products = $database->fetchAll("SELECT id, name, price FROM products WHERE status = 'active' ORDER BY RAND() LIMIT 5");
        
        if (empty($products)) {
            throw new Exception('لا توجد منتجات نشطة');
        }
        
        // حذف العناصر الموجودة للطلب
        $database->query("DELETE FROM order_items WHERE order_id = :order_id", ['order_id' => $orderId]);
        
        $totalAmount = 0;
        $itemsAdded = 0;
        
        foreach ($products as $product) {
            $quantity = rand(1, 3);
            $price = $product['price'];
            $itemTotal = $quantity * $price;
            $totalAmount += $itemTotal;
            
            $itemData = [
                'order_id' => $orderId,
                'product_id' => $product['id'],
                'product_name' => $product['name'],
                'price' => $price,
                'quantity' => $quantity
            ];
            
            $database->insert('order_items', $itemData);
            $itemsAdded++;
        }
        
        // تحديث إجمالي الطلب
        $database->update('orders', 
            ['total_amount' => $totalAmount, 'updated_at' => date('Y-m-d H:i:s')], 
            'id = :id', 
            ['id' => $orderId]
        );
        
        logActivity('add_order_items', "تم إضافة {$itemsAdded} عناصر للطلب {$order['order_number']}");
        $message = "✅ تم إضافة {$itemsAdded} عناصر للطلب {$order['order_number']} بإجمالي " . formatPrice($totalAmount);
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إضافة العناصر: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// إضافة عناصر لجميع الطلبات الفارغة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_all_items'])) {
    try {
        // البحث عن الطلبات التي لا تحتوي على عناصر
        $emptyOrders = $database->fetchAll("
            SELECT o.* FROM orders o 
            LEFT JOIN order_items oi ON o.id = oi.order_id 
            WHERE oi.order_id IS NULL 
            ORDER BY o.created_at DESC
        ");
        
        if (empty($emptyOrders)) {
            throw new Exception('جميع الطلبات تحتوي على عناصر');
        }
        
        $products = $database->fetchAll("SELECT id, name, price FROM products WHERE status = 'active' LIMIT 10");
        
        if (empty($products)) {
            throw new Exception('لا توجد منتجات نشطة');
        }
        
        $ordersProcessed = 0;
        $totalItemsAdded = 0;
        
        foreach ($emptyOrders as $order) {
            $selectedProducts = array_rand($products, min(rand(2, 5), count($products)));
            if (!is_array($selectedProducts)) {
                $selectedProducts = [$selectedProducts];
            }
            
            $orderTotal = 0;
            $orderItemsCount = 0;
            
            foreach ($selectedProducts as $productIndex) {
                $product = $products[$productIndex];
                $quantity = rand(1, 3);
                $price = $product['price'];
                $itemTotal = $quantity * $price;
                $orderTotal += $itemTotal;
                
                $itemData = [
                    'order_id' => $order['id'],
                    'product_id' => $product['id'],
                    'product_name' => $product['name'],
                    'price' => $price,
                    'quantity' => $quantity
                ];
                
                $database->insert('order_items', $itemData);
                $orderItemsCount++;
                $totalItemsAdded++;
            }
            
            // تحديث إجمالي الطلب
            $database->update('orders', 
                ['total_amount' => $orderTotal, 'updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $order['id']]
            );
            
            $ordersProcessed++;
        }
        
        logActivity('add_all_order_items', "تم إضافة عناصر لـ {$ordersProcessed} طلبات ({$totalItemsAdded} عنصر إجمالي)");
        $message = "✅ تم إضافة عناصر لـ {$ordersProcessed} طلبات بإجمالي {$totalItemsAdded} عنصر";
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = "❌ خطأ في إضافة العناصر: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// فحص حالة النظام
$systemStatus = [];

// فحص جدول order_items
try {
    $orderItemsCount = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
    $systemStatus['order_items'] = ['exists' => true, 'count' => $orderItemsCount];
} catch (Exception $e) {
    $systemStatus['order_items'] = ['exists' => false, 'count' => 0];
}

// فحص الطلبات
try {
    $ordersCount = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
    $systemStatus['orders'] = ['exists' => true, 'count' => $ordersCount];
} catch (Exception $e) {
    $systemStatus['orders'] = ['exists' => false, 'count' => 0];
}

// فحص المنتجات
try {
    $productsCount = $database->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'];
    $systemStatus['products'] = ['exists' => true, 'count' => $productsCount];
} catch (Exception $e) {
    $systemStatus['products'] = ['exists' => false, 'count' => 0];
}

// الحصول على الطلبات الفارغة
$emptyOrdersCount = 0;
if ($systemStatus['order_items']['exists'] && $systemStatus['orders']['exists']) {
    try {
        $emptyOrdersCount = $database->fetch("
            SELECT COUNT(*) as count FROM orders o 
            LEFT JOIN order_items oi ON o.id = oi.order_id 
            WHERE oi.order_id IS NULL
        ")['count'];
    } catch (Exception $e) {
        $emptyOrdersCount = 0;
    }
}

$pageTitle = "إضافة عناصر للطلبات - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    .status-card {
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        border: 1px solid #dee2e6;
    }
    .status-good { background: #d4edda; border-color: #c3e6cb; }
    .status-warning { background: #fff3cd; border-color: #ffeaa7; }
    .status-danger { background: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <h1 class="mt-4 mb-4">
                    <i class="fas fa-plus-circle"></i> إضافة عناصر للطلبات
                </h1>
                
                <!-- عرض الرسائل -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <!-- حالة النظام -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="status-card <?php echo $systemStatus['order_items']['exists'] ? 'status-good' : 'status-danger'; ?>">
                            <h5>
                                <i class="fas fa-table"></i> جدول order_items
                                <?php if ($systemStatus['order_items']['exists']): ?>
                                    <span class="badge bg-success">موجود</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </h5>
                            <p>عدد العناصر: <strong><?php echo number_format($systemStatus['order_items']['count']); ?></strong></p>
                            
                            <?php if (!$systemStatus['order_items']['exists']): ?>
                            <form method="POST" class="mt-3">
                                <button type="submit" name="create_table" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إنشاء الجدول
                                </button>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="status-card <?php echo $systemStatus['orders']['exists'] ? 'status-good' : 'status-danger'; ?>">
                            <h5>
                                <i class="fas fa-shopping-cart"></i> الطلبات
                                <?php if ($systemStatus['orders']['exists']): ?>
                                    <span class="badge bg-success">موجود</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير موجود</span>
                                <?php endif; ?>
                            </h5>
                            <p>عدد الطلبات: <strong><?php echo number_format($systemStatus['orders']['count']); ?></strong></p>
                            <p>طلبات فارغة: <strong class="text-warning"><?php echo number_format($emptyOrdersCount); ?></strong></p>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="status-card <?php echo $systemStatus['products']['count'] > 0 ? 'status-good' : 'status-warning'; ?>">
                            <h5>
                                <i class="fas fa-box"></i> المنتجات النشطة
                                <?php if ($systemStatus['products']['count'] > 0): ?>
                                    <span class="badge bg-success">متاح</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">قليل</span>
                                <?php endif; ?>
                            </h5>
                            <p>عدد المنتجات: <strong><?php echo number_format($systemStatus['products']['count']); ?></strong></p>
                        </div>
                    </div>
                </div>
                
                <!-- إجراءات سريعة -->
                <?php if ($systemStatus['order_items']['exists'] && $systemStatus['orders']['exists'] && $systemStatus['products']['count'] > 0): ?>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-magic"></i> إضافة عناصر لجميع الطلبات الفارغة</h5>
                            </div>
                            <div class="card-body">
                                <p>سيتم إضافة عناصر تجريبية لجميع الطلبات التي لا تحتوي على عناصر.</p>
                                <p><strong>الطلبات الفارغة:</strong> <?php echo number_format($emptyOrdersCount); ?></p>
                                
                                <?php if ($emptyOrdersCount > 0): ?>
                                <form method="POST" onsubmit="return confirm('هل تريد إضافة عناصر لجميع الطلبات الفارغة؟')">
                                    <button type="submit" name="add_all_items" class="btn btn-success btn-lg">
                                        <i class="fas fa-magic"></i> إضافة عناصر لجميع الطلبات
                                    </button>
                                </form>
                                <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-check-circle"></i> جميع الطلبات تحتوي على عناصر
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-plus"></i> إضافة عناصر لطلب محدد</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <div class="mb-3">
                                        <label for="order_id" class="form-label">اختر الطلب:</label>
                                        <select class="form-select" id="order_id" name="order_id" required>
                                            <option value="">اختر طلب...</option>
                                            <?php
                                            $recentOrders = $database->fetchAll("SELECT id, order_number, customer_name, total_amount FROM orders ORDER BY created_at DESC LIMIT 20");
                                            foreach ($recentOrders as $order):
                                            ?>
                                            <option value="<?php echo $order['id']; ?>">
                                                <?php echo $order['order_number']; ?> - <?php echo $order['customer_name']; ?> (<?php echo formatPrice($order['total_amount']); ?>)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" name="add_items" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> إضافة عناصر
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- روابط سريعة -->
                <div class="mt-4">
                    <h5>روابط مفيدة:</h5>
                    <div class="btn-group" role="group">
                        <a href="orders.php" class="btn btn-outline-primary">
                            <i class="fas fa-list"></i> إدارة الطلبات
                        </a>
                        <a href="test-modal-actions.php" class="btn btn-outline-info">
                            <i class="fas fa-eye"></i> اختبار المودال
                        </a>
                        <a href="create-sample-data.php" class="btn btn-outline-success">
                            <i class="fas fa-database"></i> إنشاء بيانات تجريبية
                        </a>
                        <a href="activity-logs.php" class="btn btn-outline-secondary">
                            <i class="fas fa-history"></i> سجل الأنشطة
                        </a>
                    </div>
                </div>
                
                <!-- تعليمات -->
                <div class="mt-4">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> تعليمات:</h5>
                        <ol>
                            <li><strong>إنشاء الجدول:</strong> إذا لم يكن جدول order_items موجود، انقر "إنشاء الجدول"</li>
                            <li><strong>إضافة عناصر:</strong> استخدم "إضافة عناصر لجميع الطلبات" لحل المشكلة بسرعة</li>
                            <li><strong>اختبار النتيجة:</strong> اذهب لصفحة الطلبات وافتح أي طلب لرؤية العناصر</li>
                            <li><strong>التحقق:</strong> استخدم "اختبار المودال" للتأكد من عمل كل شيء</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
