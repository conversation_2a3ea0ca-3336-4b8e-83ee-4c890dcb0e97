/* 
 * تصميم لوحة التحكم
 * Admin Panel Styles
 */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

/* المتغيرات */
:root {
    --admin-primary: #2d5016;
    --admin-secondary: #4a7c59;
    --admin-accent: #8fbc8f;
    --admin-dark: #1a3009;
    --admin-light: #f0f8f0;
    --sidebar-width: 250px;
    --header-height: 70px;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --white: #ffffff;
    --light-bg: #f8f9fa;
}

/* الإعدادات العامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
}

/* تخطيط الإدارة */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* الشريط الجانبي */
.admin-sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--admin-primary), var(--admin-dark));
    color: var(--white);
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white);
    text-decoration: none;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
}

.sidebar-menu li {
    margin-bottom: 0.5rem;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s;
    border-right: 3px solid transparent;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255,255,255,0.1);
    color: var(--white);
    border-right-color: var(--admin-accent);
}

.sidebar-menu i {
    margin-left: 10px;
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.admin-content {
    flex: 1;
    margin-right: var(--sidebar-width);
    min-height: 100vh;
}

/* هيدر الإدارة */
.admin-header {
    background: var(--white);
    height: var(--header-height);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    position: sticky;
    top: 0;
    z-index: 999;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--admin-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    position: relative;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.user-info:hover {
    background: var(--light-bg);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--admin-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
}

/* المحتوى الداخلي */
.admin-main {
    padding: 2rem;
}

/* البطاقات */
.admin-card {
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    background: var(--admin-light);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--admin-primary);
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
}

.stat-icon.primary { background: var(--admin-primary); }
.stat-icon.success { background: var(--success-color); }
.stat-icon.warning { background: var(--warning-color); }
.stat-icon.danger { background: var(--danger-color); }
.stat-icon.info { background: var(--info-color); }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.stat-content p {
    color: var(--light-text);
    margin: 0;
}

/* الجداول */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-table th {
    background: var(--admin-light);
    padding: 1rem;
    text-align: right;
    font-weight: 600;
    color: var(--admin-primary);
    border-bottom: 1px solid var(--border-color);
}

.admin-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.admin-table tr:hover {
    background: var(--light-bg);
}

/* الأزرار */
.btn-admin {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;
}

.btn-admin:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: var(--admin-primary);
    color: var(--white);
}

.btn-success {
    background: var(--success-color);
    color: var(--white);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-color);
}

.btn-danger {
    background: var(--danger-color);
    color: var(--white);
}

.btn-info {
    background: var(--info-color);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--admin-primary);
    color: var(--admin-primary);
}

.btn-outline:hover {
    background: var(--admin-primary);
    color: var(--white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(45, 80, 22, 0.25);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
    padding-left: 2.5rem;
}

/* الرسائل */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    border: none;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-right: 4px solid var(--success-color);
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border-right: 4px solid var(--danger-color);
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-right: 4px solid var(--warning-color);
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-right: 4px solid var(--info-color);
}

/* الشارات */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 3px;
    text-transform: uppercase;
}

.badge-success { background: var(--success-color); color: var(--white); }
.badge-warning { background: var(--warning-color); color: var(--text-color); }
.badge-danger { background: var(--danger-color); color: var(--white); }
.badge-info { background: var(--info-color); color: var(--white); }
.badge-primary { background: var(--admin-primary); color: var(--white); }

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(100%);
    }
    
    .admin-sidebar.show {
        transform: translateX(0);
    }
    
    .admin-content {
        margin-right: 0;
    }
    
    .admin-main {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-header {
        padding: 0 1rem;
    }
}

/* أدوات مساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }
.w-100 { width: 100%; }
.h-100 { height: 100%; }
