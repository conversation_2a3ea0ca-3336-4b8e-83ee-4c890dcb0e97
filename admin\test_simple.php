<?php
/**
 * اختبار بسيط للمنتجات
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار بسيط للمنتجات</h2>";

try {
    require_once '../includes/functions.php';
    
    echo "<h3>1. اختبار قاعدة البيانات:</h3>";
    
    // اختبار مباشر
    $products = $database->fetchAll("SELECT * FROM products");
    echo "عدد المنتجات في قاعدة البيانات: " . count($products) . "<br>";
    
    if (count($products) > 0) {
        echo "<h4>المنتجات الموجودة:</h4>";
        foreach ($products as $product) {
            echo "- " . $product['name'] . " (ID: " . $product['id'] . ")<br>";
        }
    } else {
        echo "<p style='color: red;'>❌ لا توجد منتجات</p>";
        
        // إضافة منتج تجريبي مباشرة
        echo "<h3>2. إضافة منتج تجريبي:</h3>";
        
        $testProduct = [
            'name' => 'منتج تجريبي',
            'description' => 'هذا منتج تجريبي للاختبار',
            'short_description' => 'منتج تجريبي',
            'category_id' => 1,
            'price' => 50.00,
            'sku' => 'TEST001',
            'stock_quantity' => 10,
            'status' => 'active',
            'featured' => 1
        ];
        
        // التأكد من وجود قسم
        $category = $database->fetch("SELECT * FROM categories LIMIT 1");
        if (!$category) {
            echo "إضافة قسم تجريبي...<br>";
            $testCategory = [
                'name' => 'قسم تجريبي',
                'description' => 'قسم تجريبي للاختبار',
                'status' => 'active'
            ];
            $database->insert('categories', $testCategory);
            $testProduct['category_id'] = $database->lastInsertId();
        } else {
            $testProduct['category_id'] = $category['id'];
        }
        
        $result = $database->insert('products', $testProduct);
        if ($result) {
            echo "✅ تم إضافة منتج تجريبي بنجاح<br>";
            echo "معرف المنتج: " . $database->lastInsertId() . "<br>";
        } else {
            echo "❌ فشل في إضافة المنتج<br>";
        }
    }
    
    echo "<h3>3. اختبار الاستعلام المستخدم في admin/products.php:</h3>";
    
    $sql = "SELECT p.*, c.name as category_name 
            FROM products p 
            LEFT JOIN categories c ON p.category_id = c.id 
            ORDER BY p.created_at DESC 
            LIMIT 20";
    
    $adminProducts = $database->fetchAll($sql);
    echo "عدد المنتجات من استعلام الإدارة: " . count($adminProducts) . "<br>";
    
    if (count($adminProducts) > 0) {
        echo "<h4>المنتجات من استعلام الإدارة:</h4>";
        foreach ($adminProducts as $product) {
            echo "- " . $product['name'] . " (القسم: " . ($product['category_name'] ?? 'غير محدد') . ")<br>";
        }
    }
    
    echo "<h3>4. روابط الاختبار:</h3>";
    echo "<a href='products.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>صفحة المنتجات</a><br><br>";
    echo "<a href='add-product.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة منتج</a><br><br>";
    echo "<a href='../add_sample_data.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إضافة بيانات تجريبية</a>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
