<?php
/**
 * اختبار تقرير المبيعات الشهرية
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار تقرير المبيعات الشهرية</h2>";

echo "<h3>1. اختبار تحميل functions.php:</h3>";
try {
    require_once '../includes/functions.php';
    echo "<p style='color: green;'>✅ تم تحميل functions.php بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في تحميل functions.php: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h3>2. اختبار الاتصال بقاعدة البيانات:</h3>";
try {
    $testQuery = $database->fetch("SELECT COUNT(*) as count FROM products");
    echo "<p style='color: green;'>✅ قاعدة البيانات تعمل - عدد المنتجات: " . $testQuery['count'] . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h3>3. اختبار صلاحيات الإدارة:</h3>";
if (function_exists('isAdmin')) {
    if (isAdmin()) {
        echo "<p style='color: green;'>✅ أنت مسجل كمدير</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ غير مسجل كمدير</p>";
        echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a>";
    }
} else {
    echo "<p style='color: red;'>❌ دالة isAdmin غير موجودة</p>";
}

echo "<h3>4. اختبار وجود جدول order_items:</h3>";
try {
    $database->fetch("SELECT 1 FROM order_items LIMIT 1");
    echo "<p style='color: green;'>✅ جدول order_items موجود</p>";
    
    $itemsCount = $database->fetch("SELECT COUNT(*) as count FROM order_items")['count'];
    echo "<p>عدد عناصر الطلبات: $itemsCount</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ جدول order_items غير موجود أو فارغ</p>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
}

echo "<h3>5. اختبار وجود جدول orders:</h3>";
try {
    $ordersCount = $database->fetch("SELECT COUNT(*) as count FROM orders")['count'];
    echo "<p style='color: green;'>✅ جدول orders موجود - عدد الطلبات: $ordersCount</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ جدول orders غير موجود</p>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
}

echo "<h3>6. اختبار الدوال المطلوبة:</h3>";
$requiredFunctions = ['formatPrice', 'formatDate', 'getSiteSetting'];
foreach ($requiredFunctions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ دالة $func موجودة</p>";
    } else {
        echo "<p style='color: red;'>❌ دالة $func غير موجودة</p>";
    }
}

echo "<h3>7. اختبار تحميل الصفحة الأصلية:</h3>";
echo "<p>إذا كانت جميع الاختبارات السابقة ناجحة، جرب الروابط التالية:</p>";

echo "<div style='margin: 20px 0;'>";
echo "<a href='monthly-sales-report.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تقرير المبيعات الشهرية</a>";
echo "<a href='fix-sales-report.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إصلاح تقرير المبيعات</a>";
echo "<a href='create-sample-data.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إنشاء بيانات تجريبية</a>";
echo "<a href='check-database-structure.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>فحص قاعدة البيانات</a>";
echo "</div>";

echo "<h3>8. معلومات إضافية:</h3>";
echo "<ul>";
echo "<li><strong>مسار الملف:</strong> " . __FILE__ . "</li>";
echo "<li><strong>مجلد العمل:</strong> " . getcwd() . "</li>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>الوقت الحالي:</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

// اختبار إنشاء محتوى HTML بسيط
echo "<h3>9. اختبار محتوى HTML:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<p>إذا كنت ترى هذا النص، فإن PHP يعمل بشكل صحيح</p>";
echo "<p>المشكلة قد تكون في:</p>";
echo "<ul>";
echo "<li>خطأ في الكود PHP</li>";
echo "<li>مشكلة في قاعدة البيانات</li>";
echo "<li>مشكلة في الصلاحيات</li>";
echo "<li>مشكلة في تحميل الملفات المطلوبة</li>";
echo "</ul>";
echo "</div>";

// فحص ملف CSS
echo "<h3>10. فحص الملفات المطلوبة:</h3>";
$requiredFiles = [
    '../includes/functions.php' => 'ملف الدوال الأساسية',
    'includes/sidebar.php' => 'القائمة الجانبية',
    '../assets/css/admin.css' => 'ملف CSS للإدارة'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ $description موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ $description غير موجود: $file</p>";
    }
}

echo "<h3>النتيجة:</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<p>إذا كانت جميع الاختبارات ناجحة، فإن المشكلة قد تكون:</p>";
echo "<ol>";
echo "<li><strong>خطأ في الكود:</strong> تحقق من سجل أخطاء PHP</li>";
echo "<li><strong>مشكلة في الصلاحيات:</strong> تأكد من تسجيل الدخول كمدير</li>";
echo "<li><strong>مشكلة في قاعدة البيانات:</strong> تأكد من وجود الجداول المطلوبة</li>";
echo "<li><strong>مشكلة في الخادم:</strong> تحقق من إعدادات Apache/PHP</li>";
echo "</ol>";
echo "</div>";
?>
