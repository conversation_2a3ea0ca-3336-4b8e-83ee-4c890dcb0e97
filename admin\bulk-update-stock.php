<?php
/**
 * تحديث مجمع للحد الأدنى للمخزون
 */

require_once '../includes/functions.php';

// التحقق من صلاحيات الإدارة
if (!isAdmin()) {
    header('Location: login.php?redirect=' . urlencode($_SERVER['REQUEST_URI']));
    exit;
}

$message = '';
$messageType = 'info';

// معالجة التحديث المجمع
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bulk_update'])) {
    $updates = $_POST['products'] ?? [];
    $updatedCount = 0;
    $errors = [];

    foreach ($updates as $productId => $data) {
        if (isset($data['min_stock_level']) && is_numeric($data['min_stock_level'])) {
            $minStock = intval($data['min_stock_level']);

            // التحقق من القيمة الحالية
            $currentProduct = $database->fetch("SELECT name, min_stock_level FROM products WHERE id = :id", ['id' => $productId]);

            if ($currentProduct) {
                // تحديث حتى لو كانت القيمة نفسها (للتأكد من التحديث)
                try {
                    $updateResult = $database->update('products',
                        ['min_stock_level' => $minStock, 'updated_at' => date('Y-m-d H:i:s')],
                        'id = :id',
                        ['id' => $productId]
                    );

                    if ($updateResult) {
                        // تسجيل النشاط
                        logActivity('min_stock_updated', "تم تحديث الحد الأدنى للمنتج: {$currentProduct['name']} من {$currentProduct['min_stock_level']} إلى {$minStock}");
                        $updatedCount++;
                    } else {
                        $errors[] = "فشل تحديث المنتج: {$currentProduct['name']}";
                    }
                } catch (Exception $e) {
                    $errors[] = "خطأ في تحديث المنتج {$currentProduct['name']}: " . $e->getMessage();
                }
            } else {
                $errors[] = "المنتج برقم $productId غير موجود";
            }
        }
    }

    if ($updatedCount > 0) {
        $message = "تم تحديث الحد الأدنى لـ {$updatedCount} منتج بنجاح";
        $messageType = 'success';

        if (!empty($errors)) {
            $message .= "<br><small>أخطاء: " . implode(', ', $errors) . "</small>";
        }
    } else {
        $message = "لم يتم تحديث أي منتج";
        if (!empty($errors)) {
            $message .= "<br>الأخطاء: " . implode('<br>', $errors);
        }
        $messageType = 'warning';
    }
}

// الحصول على جميع المنتجات
$products = $database->fetchAll("
    SELECT p.*, c.name as category_name 
    FROM products p 
    LEFT JOIN categories c ON p.category_id = c.id 
    WHERE p.status = 'active' 
    ORDER BY p.name ASC
");

$pageTitle = "تحديث مجمع للحد الأدنى - " . getSiteSetting('site_name');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_PATH; ?>/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <header class="admin-header">
                <h1 class="header-title">تحديث مجمع للحد الأدنى</h1>
            </header>

            <!-- Main Content -->
            <div class="admin-main">
                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Instructions -->
                <div class="admin-card mb-4">
                    <div class="card-header">
                        <h5 class="card-title">كيفية تحديد الحد الأدنى المناسب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-fire text-danger"></i> منتجات سريعة البيع</h6>
                                    <p class="mb-0">الحد الأدنى: <strong>10-20 قطعة</strong></p>
                                    <small>مثل: العسل، الزيوت الطبيعية</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-clock text-warning"></i> منتجات متوسطة البيع</h6>
                                    <p class="mb-0">الحد الأدنى: <strong>5-10 قطع</strong></p>
                                    <small>مثل: الأعشاب، المكملات</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-secondary">
                                    <h6><i class="fas fa-turtle text-secondary"></i> منتجات بطيئة البيع</h6>
                                    <p class="mb-0">الحد الأدنى: <strong>2-5 قطع</strong></p>
                                    <small>مثل: المنتجات الموسمية</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bulk Update Form -->
                <div class="admin-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title">تحديث الحد الأدنى للمنتجات</h5>
                        <div>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="setAllMinStock()">
                                <i class="fas fa-magic"></i> تعيين للكل
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="bulkUpdateForm">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>القسم</th>
                                            <th>المخزون الحالي</th>
                                            <th>الحد الأدنى الحالي</th>
                                            <th>الحد الأدنى الجديد</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($products as $product): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($product['image']): ?>
                                                    <img src="<?php echo UPLOADS_URL . '/' . $product['image']; ?>" 
                                                         alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                                         class="me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;">
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                                        <?php if ($product['sku']): ?>
                                                        <br><small class="text-muted">SKU: <?php echo htmlspecialchars($product['sku']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($product['category_name'] ?? 'غير محدد'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo ($product['stock_quantity'] <= $product['min_stock_level']) ? 'danger' : 'success'; ?> fs-6">
                                                    <?php echo number_format($product['stock_quantity']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-warning text-dark fs-6">
                                                    <?php echo number_format($product['min_stock_level']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <input type="number" 
                                                       class="form-control form-control-sm min-stock-input" 
                                                       name="products[<?php echo $product['id']; ?>][min_stock_level]" 
                                                       value="<?php echo $product['min_stock_level']; ?>" 
                                                       min="0" 
                                                       max="1000"
                                                       style="width: 80px;"
                                                       data-product-id="<?php echo $product['id']; ?>"
                                                       data-current-stock="<?php echo $product['stock_quantity']; ?>">
                                            </td>
                                            <td>
                                                <span class="status-indicator" id="status-<?php echo $product['id']; ?>">
                                                    <?php if ($product['stock_quantity'] == 0): ?>
                                                        <span class="badge bg-danger">نفد المخزون</span>
                                                    <?php elseif ($product['stock_quantity'] <= $product['min_stock_level']): ?>
                                                        <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">متوفر</span>
                                                    <?php endif; ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        سيتم تحديث جميع المنتجات بالقيم المعروضة
                                    </small>
                                    <br>
                                    <small class="text-info">
                                        <i class="fas fa-lightbulb"></i>
                                        تأكد من تعديل القيم قبل الحفظ
                                    </small>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-secondary me-2" onclick="showChangedProducts()">
                                        <i class="fas fa-eye"></i> عرض المتغيرات
                                    </button>
                                    <button type="submit" name="bulk_update" class="btn btn-primary btn-lg" onclick="return confirmUpdate()">
                                        <i class="fas fa-save"></i> حفظ جميع التحديثات
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="admin-card mt-4">
                    <div class="card-header">
                        <h5 class="card-title">إجراءات سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-primary w-100" onclick="setMinStockByCategory('fast')">
                                    <i class="fas fa-fire"></i><br>
                                    منتجات سريعة<br>
                                    <small>(حد أدنى: 15)</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-warning w-100" onclick="setMinStockByCategory('medium')">
                                    <i class="fas fa-clock"></i><br>
                                    منتجات متوسطة<br>
                                    <small>(حد أدنى: 8)</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="setMinStockByCategory('slow')">
                                    <i class="fas fa-turtle"></i><br>
                                    منتجات بطيئة<br>
                                    <small>(حد أدنى: 3)</small>
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button type="button" class="btn btn-outline-info w-100" onclick="setMinStockBasedOnCurrent()">
                                    <i class="fas fa-calculator"></i><br>
                                    حساب تلقائي<br>
                                    <small>(20% من المخزون)</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
    // متغيرات لحفظ القيم الأصلية
    let originalValues = {};

    // حفظ القيم الأصلية عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.min-stock-input').forEach(input => {
            originalValues[input.dataset.productId] = input.value;
            input.addEventListener('input', function() {
                updateStatus(this);
                highlightChanged(this);
            });
        });
    });

    // تمييز الحقول المتغيرة
    function highlightChanged(input) {
        const productId = input.dataset.productId;
        const originalValue = originalValues[productId];
        const currentValue = input.value;

        if (originalValue !== currentValue) {
            input.style.backgroundColor = '#fff3cd';
            input.style.border = '2px solid #ffc107';
        } else {
            input.style.backgroundColor = '';
            input.style.border = '';
        }
    }

    // عرض المنتجات المتغيرة
    function showChangedProducts() {
        let changedProducts = [];
        document.querySelectorAll('.min-stock-input').forEach(input => {
            const productId = input.dataset.productId;
            const originalValue = originalValues[productId];
            const currentValue = input.value;

            if (originalValue !== currentValue) {
                const productName = input.closest('tr').querySelector('strong').textContent;
                changedProducts.push(`${productName}: ${originalValue} → ${currentValue}`);
            }
        });

        if (changedProducts.length > 0) {
            alert('المنتجات المتغيرة:\n\n' + changedProducts.join('\n'));
        } else {
            alert('لم يتم تغيير أي منتج');
        }
    }

    // تأكيد التحديث
    function confirmUpdate() {
        let changedCount = 0;
        document.querySelectorAll('.min-stock-input').forEach(input => {
            const productId = input.dataset.productId;
            const originalValue = originalValues[productId];
            const currentValue = input.value;

            if (originalValue !== currentValue) {
                changedCount++;
            }
        });

        if (changedCount === 0) {
            alert('لم يتم تغيير أي قيمة. لا حاجة للتحديث.');
            return false;
        }

        return confirm(`هل تريد تحديث الحد الأدنى لـ ${changedCount} منتج؟`);
    }

    // تعيين حد أدنى لجميع المنتجات
    function setAllMinStock() {
        const value = prompt('أدخل الحد الأدنى لجميع المنتجات:');
        if (value && !isNaN(value) && value >= 0) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                input.value = value;
                updateStatus(input);
                highlightChanged(input);
            });
        }
    }

    // إعادة تعيين النموذج
    function resetForm() {
        if (confirm('هل تريد إعادة تعيين جميع القيم للحالة الأصلية؟')) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                const productId = input.dataset.productId;
                input.value = originalValues[productId];
                updateStatus(input);
                highlightChanged(input);
            });
        }
    }
    
    // تعيين حد أدنى حسب فئة المنتج
    function setMinStockByCategory(category) {
        let value;
        switch(category) {
            case 'fast': value = 15; break;
            case 'medium': value = 8; break;
            case 'slow': value = 3; break;
        }

        if (confirm(`هل تريد تعيين الحد الأدنى إلى ${value} لجميع المنتجات؟`)) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                input.value = value;
                updateStatus(input);
                highlightChanged(input);
            });
        }
    }

    // حساب الحد الأدنى بناءً على المخزون الحالي
    function setMinStockBasedOnCurrent() {
        if (confirm('هل تريد حساب الحد الأدنى كـ 20% من المخزون الحالي؟')) {
            document.querySelectorAll('.min-stock-input').forEach(input => {
                const currentStock = parseInt(input.dataset.currentStock);
                const minStock = Math.max(1, Math.round(currentStock * 0.2));
                input.value = minStock;
                updateStatus(input);
                highlightChanged(input);
            });
        }
    }

    // تحديث حالة المنتج عند تغيير الحد الأدنى
    function updateStatus(input) {
        const productId = input.dataset.productId;
        const currentStock = parseInt(input.dataset.currentStock);
        const newMinStock = parseInt(input.value);
        const statusElement = document.getElementById('status-' + productId);

        let statusHtml;
        if (currentStock == 0) {
            statusHtml = '<span class="badge bg-danger">نفد المخزون</span>';
        } else if (currentStock <= newMinStock) {
            statusHtml = '<span class="badge bg-warning text-dark">مخزون منخفض</span>';
        } else {
            statusHtml = '<span class="badge bg-success">متوفر</span>';
        }

        statusElement.innerHTML = statusHtml;
    }
    </script>
</body>
</html>
